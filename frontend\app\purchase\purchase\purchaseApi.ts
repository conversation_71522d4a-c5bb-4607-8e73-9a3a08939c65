// purchaseApi.ts - API utilities for Purchase endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api/purchases';

export interface PurchaseItemSnapshot {
  productUuid: string;
  name: string;
  quantity: number;
  unitPrice: number;
  lineTotal: number;
  taxAmount: number;
  productUuidString: string;
}

export interface Purchase {
  uuid: string;
  purchaseOrderNumber: string;
  supplierUuid: string;
  supplierUuidString: string;
  supplierName?: string;
  supplierFiscalId?: string;
  supplierRc?: string;
  supplierArticleNumber?: string;
  orderUuid?: string;
  orderUuidString?: string;
  itemsSnapshot: PurchaseItemSnapshot[];
  subtotal: number;
  useTax: boolean;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  amountPaid: number;
  balanceDue: number;
  paymentMethod: string;
  // Removed paymentDate since it's redundant with updatedAt and excluded from logs
  purchaseDate: string;
  dueDate: string;
  status: 'paid' | 'partially_paid' | 'unpaid' | 'cancelled';
  createdBy: string;
  createdByString: string;
  updatedBy: string;
  updatedByString: string;
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseItem {
  uuid: string;
  purchaseUuid: string;
  productUuid: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface CreatePurchaseItemDto {
  productUuid: string;
  name: string;
  quantity: number;
  unitPrice: number;
  order?: number;
}

export interface CreatePurchaseDto {
  supplierUuid?: string;
  supplierName?: string;
  supplierFiscalId?: string;
  supplierRc?: string;
  supplierArticleNumber?: string;
  warehouseUuid?: string;
  warehouseName?: string;
  useTax: boolean;
  taxRate: number;
  paymentMethod: PaymentMethods;
  purchaseDate: string;
  dueDate: string;
  status: PurchaseStatus;
  notes?: string;
  createdBy: string;
  updatedBy: string;
  purchaseItems: CreatePurchaseItemDto[];
}

export interface UpdatePurchaseItemDto {
  id?: string;
  productUuid?: string;
  name?: string;
  quantity?: number;
  unitPrice?: number;
  order?: number;
}

export interface UpdatePurchaseDto {
  supplierUuid?: string;
  supplierName?: string;
  supplierFiscalId?: string;
  supplierRc?: string;
  supplierArticleNumber?: string;
  warehouseUuid?: string;
  warehouseName?: string;
  useTax?: boolean;
  taxRate?: number;
  paymentMethod?: PaymentMethods;
  purchaseDate?: string;
  dueDate?: string;
  status?: PurchaseStatus;
  notes?: string;
  updatedBy?: string;
  purchaseItems?: UpdatePurchaseItemDto[];
}

// Updated to match backend enum values
export enum PurchaseStatus {
  PAID = "paid",
  PARTIALLY_PAID = "partially_paid",
  UNPAID = "unpaid",
  CANCELLED = "cancelled",
}

// Updated to match backend enum values
export enum PaymentMethods {
  CASH = "cash",
  CREDIT_CARD = "credit_card",
  BANK_TRANSFER = "bank_transfer",
  MOBILE_PAYMENT = "mobile_payment",
  CHEQUE = "cheque",
  OTHER = "other",
}

export interface FilterPurchaseDto {
  warehouseUuid?: string;
  supplierUuid?: string;
  supplierName?: string; // Keep for backward compatibility
  status?: PurchaseStatus;
  paymentMethod?: PaymentMethods;
  purchaseNumber?: string; // Updated to match backend
  // Creation date range (createdFrom/createdTo)
  createdFrom?: string;
  createdTo?: string;
  // Purchase date range (startDate/endDate) - new backend filters
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
}

export interface PaginationQueryDto {
  page?: number;
  limit?: number;
}

export interface PurchasesResponse {
  data: Purchase[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
  totalPages: number;
}

/**
 * Get all purchases with optional filtering and pagination
 */
export async function getPurchases(pagination?: PaginationQueryDto, filter?: FilterPurchaseDto): Promise<PurchasesResponse> {
  const params = new URLSearchParams();
  
  // Add pagination parameters with validation
  if (pagination?.page && !isNaN(pagination.page) && pagination.page > 0) {
    params.append('page', pagination.page.toString());
  }
  if (pagination?.limit && !isNaN(pagination.limit) && pagination.limit > 0 && pagination.limit <= 100) {
    params.append('limit', pagination.limit.toString());
  }
  
  // Helper function to validate and add string parameters
  const addStringParam = (key: string, value: string | undefined) => {
    if (value && typeof value === 'string' && value.trim() !== '' && value.trim() !== 'undefined' && value.trim() !== 'null') {
      params.append(key, value.trim());
    }
  };
  
  // Add filter parameters with validation
  addStringParam('warehouseUuid', filter?.warehouseUuid);
  addStringParam('supplierUuid', filter?.supplierUuid);
  addStringParam('supplierName', filter?.supplierName);
  addStringParam('status', filter?.status);
  addStringParam('paymentMethod', filter?.paymentMethod);
  addStringParam('purchaseNumber', filter?.purchaseNumber);
  addStringParam('createdFrom', filter?.createdFrom);
  addStringParam('createdTo', filter?.createdTo);
  addStringParam('startDate', filter?.startDate);
  addStringParam('endDate', filter?.endDate);
  
  // Validate and add numeric filters - only send valid numbers
  if (filter?.minAmount !== undefined && filter.minAmount !== null && !isNaN(filter.minAmount) && filter.minAmount >= 0) {
    params.append('minAmount', filter.minAmount.toString());
  }
  if (filter?.maxAmount !== undefined && filter.maxAmount !== null && !isNaN(filter.maxAmount) && filter.maxAmount >= 0) {
    params.append('maxAmount', filter.maxAmount.toString());
  }

  const url = `${API_BASE}?${params.toString()}`;
  const headers = getAxiosAuthHeaders();
  
  try {
    const res = await axios.get(url, {
      headers,
    });
    return res.data;
  } catch (error: any) {
    throw error;
  }
}

/**
 * Get a single purchase by UUID
 */
export async function getPurchase(uuid: string): Promise<Purchase> {
  const res = await axios.get(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Create a new purchase
 */
export async function createPurchase(data: CreatePurchaseDto): Promise<Purchase> {
  const res = await axios.post(API_BASE, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Update purchase details
 */
export async function updatePurchase(uuid: string, data: UpdatePurchaseDto): Promise<Purchase> {
  const res = await axios.patch(`${API_BASE}/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Cancel a purchase
 */
export async function cancelPurchase(uuid: string, userUuid: string, reason?: string): Promise<Purchase> {
  const res = await axios.patch(`${API_BASE}/${uuid}/cancel`, { userUuid, reason }, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Update purchase status
 */
export async function updatePurchaseStatus(uuid: string, newStatus: PurchaseStatus, userUuid: string, oldStatus?: PurchaseStatus): Promise<Purchase> {
  const res = await axios.patch(`${API_BASE}/${uuid}/status`, {
    newStatus,
    userUuid,
    oldStatus
  }, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Add products to an existing purchase
 */
export async function addProductsToPurchase(uuid: string, userUuid: string, purchaseItems: CreatePurchaseItemDto[]): Promise<Purchase> {
  const res = await axios.patch(`${API_BASE}/${uuid}/add-products`, {
    userUuid,
    purchaseItems
  }, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Get purchases by warehouse
 */
export async function getPurchasesByWarehouse(warehouseUuid: string): Promise<Purchase[]> {
  const res = await axios.get(`${API_BASE}/warehouse/${warehouseUuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Delete a purchase (soft delete)
 */
export async function deletePurchase(uuid: string, userUuid: string): Promise<{ message: string }> {
  const res = await axios.delete(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
    data: { userUuid }
  });
  return res.data;
}
