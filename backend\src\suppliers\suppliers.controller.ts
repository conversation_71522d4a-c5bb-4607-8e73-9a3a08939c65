import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from "@nestjs/common";
import { SuppliersService } from "./suppliers.service";
import { CreateSupplierDto } from "./dto/create-supplier.dto";
import { UpdateSupplierDto } from "./dto/update-supplier.dto";
import { SupplierDto, toSupplierDto } from "./dto/supplier.dto";
import { FilterSupplierDto } from "./dto/filter-supplier.dto";
import {
  PaginationQueryDto,
  PaginatedResponseDto,
} from "../dto/pagination.dto";
import {
  ApiTags,
  ApiQuery,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from "@nestjs/swagger";

@ApiTags("suppliers")
@Controller("suppliers")
export class SuppliersController {
  constructor(private readonly suppliersService: SuppliersService) {}

  @Post()
  @ApiOperation({
    summary: "Create a new supplier",
    description:
      "Creates a new supplier. userUuid and name fields are required; all other fields are optional.",
  })
  @ApiBody({ type: CreateSupplierDto, description: "Supplier data to create" })
  @ApiResponse({
    status: 201,
    description: "Supplier created successfully",
    type: SupplierDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - validation error or warehouse not found",
  })
  async create(
    @Body() createSupplierDto: CreateSupplierDto,
  ): Promise<SupplierDto> {
    console.log('[SuppliersController] create called with:', JSON.stringify(createSupplierDto, null, 2));
    try {
      const supplier = await this.suppliersService.create(createSupplierDto);
      console.log('[SuppliersController] Supplier created successfully:', supplier.id);
      return toSupplierDto(supplier);
    } catch (error) {
      console.error('[SuppliersController] Error creating supplier:', error);
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: "Get all suppliers with pagination and filtering",
    description: "Returns a paginated list of suppliers with optional filtering.",
  })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number (default: 1)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page (default: 10)",
  })
  @ApiQuery({
    name: "warehouseUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the warehouse (optional)",
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the user (optional)",
  })
  @ApiQuery({
    name: "name",
    type: String,
    required: false,
    description: "Partial supplier name, case-insensitive (optional)",
  })
  @ApiQuery({
    name: "email",
    type: String,
    required: false,
    description: "Partial email address, case-insensitive (optional)",
  })
  @ApiQuery({
    name: "phone",
    type: String,
    required: false,
    description: "Partial phone number, case-insensitive (optional)",
  })
  @ApiQuery({
    name: "code",
    type: String,
    required: false,
    description: "Partial supplier code, case-insensitive (optional)",
  })
  @ApiQuery({
    name: "fiscalId",
    type: String,
    required: false,
    description: "Partial fiscal ID, case-insensitive (optional)",
  })
  @ApiQuery({
    name: "sortBy",
    required: false,
    enum: [
      "name",
      "email",
      "phone",
      "code",
      "fiscalId",
      "createdAt",
      "updatedAt",
    ],
    description: "Sort by field",
  })
  @ApiQuery({
    name: "sortOrder",
    required: false,
    enum: ["asc", "desc"],
    description: "Sort order (default: asc)",
  })
  @ApiResponse({
    status: 200,
    description: "Paginated list of suppliers retrieved successfully",
    type: PaginatedResponseDto<SupplierDto>,
  })
  async findAll(
    @Query() paginationQuery: PaginationQueryDto,
    @Query() filter: FilterSupplierDto,
  ): Promise<PaginatedResponseDto<SupplierDto>> {
    console.log('[SuppliersController] findAll called');
    console.log('[SuppliersController] paginationQuery:', JSON.stringify(paginationQuery, null, 2));
    console.log('[SuppliersController] filter:', JSON.stringify(filter, null, 2));
    console.log('[SuppliersController] Raw query params:', JSON.stringify({ paginationQuery, filter }, null, 2));
    
    try {
      const { page = 1, limit = 10 } = paginationQuery;
      console.log('[SuppliersController] Parsed page:', page, 'limit:', limit);
      console.log('[SuppliersController] Calling suppliersService.findAll with filter:', JSON.stringify(filter, null, 2));
      
      const result = await this.suppliersService.findAll(filter, page, limit);
      console.log('[SuppliersController] Service returned result with', result.data.length, 'suppliers');
      
      const supplierDtos = result.data.map(toSupplierDto);
      return new PaginatedResponseDto(
        supplierDtos,
        result.total,
        result.page,
        result.limit,
      );
    } catch (error) {
      console.error('[SuppliersController] Error in findAll:', error);
      throw error;
    }
  }

  @Post("filter")
  @ApiOperation({ 
    summary: "Filter suppliers with advanced criteria and pagination",
    description: "Advanced filtering with pagination support. Use this endpoint for complex filtering scenarios."
  })
  @ApiBody({ type: FilterSupplierDto })
  @ApiResponse({
    status: 200,
    description: "Filtered suppliers retrieved successfully",
    type: PaginatedResponseDto<SupplierDto>,
  })
  @ApiResponse({ status: 400, description: "Invalid filter parameters" })
  async filterSuppliers(
    @Body() filter: FilterSupplierDto,
    @Query() paginationQuery: PaginationQueryDto,
  ): Promise<PaginatedResponseDto<SupplierDto>> {
    console.log('[SuppliersController] filterSuppliers (POST) called');
    console.log('[SuppliersController] filter:', JSON.stringify(filter, null, 2));
    console.log('[SuppliersController] paginationQuery:', JSON.stringify(paginationQuery, null, 2));
    
    try {
      const { page = 1, limit = 10 } = paginationQuery;
      console.log('[SuppliersController] Parsed page:', page, 'limit:', limit);
      console.log('[SuppliersController] Calling suppliersService.findAll with filter:', JSON.stringify(filter, null, 2));
      
      const result = await this.suppliersService.findAll(filter, page, limit);
      console.log('[SuppliersController] Service returned result with', result.data.length, 'suppliers');
      
      const supplierDtos = result.data.map(toSupplierDto);
      return new PaginatedResponseDto(
        supplierDtos,
        result.total,
        result.page,
        result.limit,
      );
    } catch (error) {
      console.error('[SuppliersController] Error in filterSuppliers (POST):', error);
      throw error;
    }
  }

  @Get(":uuid")
  @ApiOperation({
    summary: "Get supplier by UUID",
    description: "Returns a specific supplier by its UUID. Can be filtered by userUuid.",
  })
  @ApiParam({
    name: "uuid",
    description: "UUIDv7 string of the supplier",
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the user (optional)",
  })
  @ApiResponse({
    status: 200,
    description: "Supplier retrieved successfully",
    type: SupplierDto,
  })
  @ApiResponse({
    status: 404,
    description: "Supplier not found",
  })
  async findOne(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Query("userUuid") userUuid?: string,
  ): Promise<SupplierDto> {
    console.log('[SuppliersController] findOne called for UUID:', uuid);
    console.log('[SuppliersController] userUuid filter:', userUuid);
    
    try {
      const supplier = await this.suppliersService.findOne(uuid, userUuid);
      console.log('[SuppliersController] Supplier found:', supplier.id);
      return toSupplierDto(supplier);
    } catch (error) {
      console.error('[SuppliersController] Error in findOne:', error);
      throw error;
    }
  }

  @Patch(":uuid")
  @ApiOperation({
    summary: "Update supplier",
    description:
      "Updates a specific supplier by its UUID. All fields are optional. Can be filtered by userUuid.",
  })
  @ApiParam({
    name: "uuid",
    description: "UUIDv7 string of the supplier",
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the user (optional)",
  })
  @ApiBody({
    type: UpdateSupplierDto,
    description: "Supplier data to update",
  })
  @ApiResponse({
    status: 200,
    description: "Supplier updated successfully",
    type: SupplierDto,
  })
  @ApiResponse({
    status: 404,
    description: "Supplier not found or warehouse not found",
  })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateSupplierDto: UpdateSupplierDto,
    @Query("userUuid") userUuid?: string,
  ): Promise<SupplierDto> {
    console.log('[SuppliersController] update called for UUID:', uuid);
    console.log('[SuppliersController] updateSupplierDto:', JSON.stringify(updateSupplierDto, null, 2));
    console.log('[SuppliersController] userUuid filter:', userUuid);
    
    try {
      const supplier = await this.suppliersService.update(uuid, updateSupplierDto, userUuid);
      console.log('[SuppliersController] Supplier updated successfully:', supplier.id);
      return toSupplierDto(supplier);
    } catch (error) {
      console.error('[SuppliersController] Error in update:', error);
      throw error;
    }
  }

  @Delete(":uuid")
  @ApiOperation({
    summary: "Soft delete supplier",
    description:
      "Soft deletes a supplier by setting isDeleted to true. The supplier will not appear in normal queries but data is preserved. Can be filtered by userUuid.",
  })
  @ApiParam({
    name: "uuid",
    description: "UUIDv7 string of the supplier",
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the user (optional)",
  })
  @ApiResponse({
    status: 200,
    description: "Supplier soft deleted successfully",
  })
  @ApiResponse({
    status: 404,
    description: "Supplier not found",
  })
  async remove(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Query("userUuid") userUuid?: string,
  ): Promise<void> {
    console.log('[SuppliersController] remove called for UUID:', uuid);
    console.log('[SuppliersController] userUuid filter:', userUuid);
    
    try {
      await this.suppliersService.remove(uuid, userUuid);
      console.log('[SuppliersController] Supplier soft deleted successfully:', uuid);
    } catch (error) {
      console.error('[SuppliersController] Error in remove:', error);
      throw error;
    }
  }
}
