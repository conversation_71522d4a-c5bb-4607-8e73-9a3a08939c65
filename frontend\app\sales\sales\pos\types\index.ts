// POS module type definitions

export interface Product {
  uuid: string;
  warehouseUuid: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  category?: string;
  price?: number;
  cost?: number;
  stockQuantity?: number;
  unit?: string;
  // Stock information from main storage
  currentStock?: number;
  stockStorageUuid?: string;
  // Customer pricing fields from backend
  retailPrice?: number;
  wholesalePrice?: number;
  midWholesalePrice?: number;
  institutionalPrice?: number;
  customerPrice?: number;
  customerType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}

export interface SaleItem {
  productUuid: string;
  name: string; // Use name field that backend expects
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  order?: number; // Add order field to preserve cart order
}

export interface Sale {
  uuid?: string;
  warehouseUuid: string;
  userUuid: string;
  customerUuid?: string;
  customerName?: string;
  items: SaleItem[];
  subtotal: number;
  tax: number;
  total: number;
  status: 'draft' | 'pending' | 'completed' | 'cancelled';
  paymentMethod?: string;
  amountPaid?: number;
  notes?: string;
  createdAt?: string;
}

// Component prop types
export interface ProductListProps {
  products: Product[];
  selectedIndex: number;
  searchTerm: string;
  cartItems?: SaleItem[];
  onProductSelect: (product: Product, index: number) => void;
  isLoading?: boolean;
  disabled?: boolean;
}

export interface SalesCartProps {
  items: SaleItem[];
  selectedCustomer: Customer | null;
  notes: string;
  subtotal: number;
  tax: number;
  total: number;
  isSubmitting: boolean;
  error: string;
  paymentMethod: string;
  amountPaid: number;
  taxEnabled: boolean;
  taxRate: number;
  notesEnabled: boolean;
  onRemoveItem: (productUuid: string) => void;
  onUpdateQuantity: (productUuid: string, quantity: number) => void;
  onUpdatePrice: (productUuid: string, unitPrice: number) => void;
  onNotesChange: (notes: string) => void;
  onSubmit: () => void;
  onCustomerSelect: () => void;
  onPaymentMethodChange: (method: string) => void;
  onAmountPaidChange: (amount: number) => void;
  onTaxEnabledChange: (enabled: boolean) => void;
  onTaxRateChange: (rate: number) => void;
  onNotesEnabledChange: (enabled: boolean) => void;
  highlightedCartItemUuid?: string | null;
  disabled?: boolean;
}

export interface QuantityModalProps {
  isOpen: boolean;
  product: Product | null;
  quantity: string;
  price: string;
  onQuantityChange: (quantity: string) => void;
  onPriceChange: (price: string) => void;
  onConfirm: () => void;
  onCancel: () => void;
  quantityInputRef?: React.RefObject<HTMLInputElement>;
  priceInputRef?: React.RefObject<HTMLInputElement>;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  disabled?: boolean;
}

export interface PaymentSelectorProps {
  selectedMethod: string;
  onMethodChange: (method: string) => void;
  total: number;
  amountPaid: number;
  onAmountPaidChange: (amount: number) => void;
  disabled?: boolean;
}

export interface TaxControlsProps {
  taxEnabled: boolean;
  taxRate: number;
  onTaxEnabledChange: (enabled: boolean) => void;
  onTaxRateChange: (rate: number) => void;
  disabled?: boolean;
}

export interface NotesControlsProps {
  notesEnabled: boolean;
  notes: string;
  onNotesEnabledChange: (enabled: boolean) => void;
  onNotesChange: (notes: string) => void;
  disabled?: boolean;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface UsePOSDataReturn {
  customers: Customer[];
  products: Product[];
  pagination: PaginationInfo;
  currentCustomerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  loadCustomers: () => void;
  loadProducts: (name?: string, page?: number, limit?: number, customerType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional') => void;
  loadNextPage: (name?: string) => void;
  loadPrevPage: (name?: string) => void;
  loadPage: (page: number, name?: string) => void;
  loadMoreProducts: (name?: string) => void;
  updateCustomerType: (customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional', currentSearchTerm?: string, currentPage?: number) => void;
  isLoading: boolean;
  error: string | null;
}

export interface UseSalesCartReturn {
  items: SaleItem[];
  addItem: (product: Product, quantity: number, unitPrice: number) => void;
  removeItem: (productUuid: string) => void;
  updateQuantity: (productUuid: string, quantity: number) => void;
  updatePrice: (productUuid: string, unitPrice: number) => void;
  clearCart: () => void;
  subtotal: number;
  tax: number;
  total: number;
  amountPaid: number;
  setAmountPaid: (amount: number) => void;
  taxEnabled: boolean;
  setTaxEnabled: (enabled: boolean) => void;
  taxRate: number;
  setTaxRate: (rate: number) => void;
  notesEnabled: boolean;
  setNotesEnabled: (enabled: boolean) => void;
}

export interface UseKeyboardNavigationReturn {
  selectedIndex: number;
  setSelectedIndex: (index: number | ((prevIndex: number) => number)) => void;
  handleKeyDown: (e: KeyboardEvent) => void;
}

export type PaymentMethod = 'cash' | 'credit_card' | 'bank_transfer' | 'mobile_payment' | 'cheque' | 'other'; 