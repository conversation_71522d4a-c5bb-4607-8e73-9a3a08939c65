"use client";

import React, { useEffect, useMemo, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useSuppliers } from "./useSuppliers";
import { Supplier, CreateSupplierDto, UpdateSupplierDto } from "./suppliersApi";
import ItemsTable, { ItemsTableColumn } from "@/components/itemsTable/ItemsTable";
import SupplierModal from "./components/SupplierModal";
import { toast } from "sonner";

// Import extracted hooks and components
import { useSupplierFilters, useSupplierActions } from "./hooks";
import { SearchAndFilters, SupplierTableActions } from "./components";

export default function SuppliersPage() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;

  // Custom hooks
  const filters = useSupplierFilters(warehouseUuid || undefined);
  const actions = useSupplierActions();

  // Fetch suppliers with enhanced filtering and sorting
  const { 
    data: suppliersResponse, 
    isLoading, 
    error: fetchError 
  } = useSuppliers(
    { page: filters.currentPage, limit: filters.pageSize },
    filters.filter
  );

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F2') {
        e.preventDefault();
        actions.handleAddSupplier();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [actions]);

  // Table columns
  const columns: ItemsTableColumn<Supplier>[] = useMemo(() => [
    {
      key: 'name',
      header: 'Name',
      cellClassName: 'text-left font-semibold',
      headerClassName: 'text-left',
    },
    {
      key: 'code',
      header: 'Code',
      cellClassName: 'text-center',
      headerClassName: 'text-center',
      render: (value: string) => value || '-',
    },
    {
      key: 'email',
      header: 'Email',
      cellClassName: 'text-center',
      headerClassName: 'text-center',
      render: (value: string) => value || '-',
    },
    {
      key: 'phone',
      header: 'Phone',
      cellClassName: 'text-center',
      headerClassName: 'text-center',
      render: (value: string) => value || '-',
    },
    {
      key: 'address',
      header: 'Address',
      cellClassName: 'text-left max-w-xs',
      headerClassName: 'text-left',
      render: (value: string) => {
        if (!value) return '-';
        return value.length > 50 ? `${value.substring(0, 50)}...` : value;
      },
    },
    {
      key: 'actions',
      header: 'Actions',
      headerClassName: 'text-center',
      cellClassName: 'text-center align-middle',
      render: (_: any, supplier: Supplier) => (
        <SupplierTableActions
          supplier={supplier}
          onViewDetails={actions.handleViewSupplierDetails}
          onEdit={actions.handleEditSupplier}
          onDelete={actions.handleDeleteSupplier}
          isUpdating={actions.isLoading}
          isDeleting={actions.isLoading}
        />
      ),
    },
  ], [
    actions.handleViewSupplierDetails,
    actions.handleEditSupplier,
    actions.handleDeleteSupplier,
    actions.isLoading,
  ]);

  // Data - extract from nested meta structure
  const suppliers = suppliersResponse?.data || [];
  const totalPages = suppliersResponse?.meta?.totalPages || Math.ceil(suppliers.length / filters.pageSize) || 1;
  const totalSuppliers = suppliersResponse?.meta?.total || suppliers.length || 0;

  // Error handling
  if (fetchError) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-xl font-semibold text-red-800">Error Loading Suppliers</h3>
          <p className="text-sm text-red-600 mt-1">
            {fetchError instanceof Error ? fetchError.message : 'An unexpected error occurred'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Search and Filters */}
      <SearchAndFilters
        filters={filters}
        onAddSupplier={actions.handleAddSupplier}
      />

      {/* Loading Indicator */}
      {isLoading && (
        <div className="mb-4 flex items-center justify-end">
          <div className="text-sm text-blue-600 flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            Loading...
          </div>
        </div>
      )}

      {/* Table */}
      <ItemsTable
        columns={columns}
        data={suppliers}
        noDataText={
          <div className="text-center py-8">
            <span className="text-gray-400">
              {filters.hasActiveFilters ? 'No suppliers match your filters.' : 'No suppliers found.'}
            </span>
            {filters.hasActiveFilters && (
              <button
                onClick={filters.clearAllFilters}
                className="ml-2 text-blue-600 hover:text-blue-700 underline"
              >
                Clear filters
              </button>
            )}
          </div>
        }
        containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
        pagination={{
          currentPage: filters.currentPage,
          totalPages: totalPages,
          onPageChange: filters.setCurrentPage,
          totalItems: totalSuppliers,
          itemsPerPage: filters.pageSize,
        }}
      />

      {/* Modals */}
      <SupplierModal
        isOpen={actions.isAddModalOpen}
        onClose={actions.closeAddModal}
        onSubmit={actions.handleSubmitSupplier}
        supplier={null}
        error={null}
        isLoading={actions.isLoading}
        warehouseUuid={warehouseUuid}
      />

      <SupplierModal
        isOpen={actions.isEditModalOpen}
        onClose={actions.closeEditModal}
        onSubmit={actions.handleSubmitSupplier}
        supplier={actions.selectedSupplier}
        error={null}
        isLoading={actions.isLoading}
      />

      {/* Delete Confirmation Modal */}
      {actions.isDeleteModalOpen && actions.supplierToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Delete Supplier
            </h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete "{actions.supplierToDelete.name}"? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={actions.closeDeleteModal}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                disabled={actions.isLoading}
              >
                Cancel
              </button>
              <button
                onClick={actions.handleConfirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                disabled={actions.isLoading}
              >
                {actions.isLoading ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
