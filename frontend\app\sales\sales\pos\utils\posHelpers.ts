// POS Helper Functions

import type { Customer, Product, SaleItem, Sale } from '../types';

/**
 * Format currency for display
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount);
}

/**
 * Format product name for display
 */
export function formatProductName(product: Product): string {
  return product.name || 'Unnamed Product';
}

/**
 * Check if product is in stock
 */
export function isProductInStock(product: Product): boolean {
  return (product.currentStock || 0) > 0;
}

/**
 * Get stock status for display
 */
export function getStockStatus(product: Product): {
  text: string;
  color: string;
} {
  const stock = product.currentStock || 0;
  
  if (stock <= 0) {
    return {
      text: 'Out of Stock',
      color: 'text-red-600'
    };
  } else if (stock <= 5) {
    return {
      text: `Low Stock (${stock})`,
      color: 'text-yellow-600'
    };
  } else {
    return {
      text: `In Stock (${stock})`,
      color: 'text-green-600'
    };
  }
}

/**
 * Get payment method display name
 */
export function getPaymentMethodName(method: string): string {
  const methodNames: Record<string, string> = {
    'cash': 'Cash',
    'credit_card': 'Credit Card',
    'bank_transfer': 'Bank Transfer',
    'mobile_payment': 'Mobile Payment',
    'cheque': 'Cheque',
    'other': 'Other'
  };
  
  return methodNames[method] || method;
} 