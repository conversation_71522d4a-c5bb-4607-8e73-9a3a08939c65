import { useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { createSupplier } from '@/components/SupplierModal';
import type { CreateSupplierDto, Supplier } from '@/components/SupplierModal';
import type { POSPurchaseState } from './usePOSState';
import { createPurchase, updatePurchase, getPurchase, CreatePurchaseDto, UpdatePurchaseDto, PurchaseStatus, PaymentMethods } from '../../purchaseApi';

const API_BASE_URL = '/api';

export function usePOSOperations() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;

  const savePurchase = useCallback(async (purchaseState: POSPurchaseState) => {
    try {
      if (!warehouseUuid || !userUuid) {
        throw new Error('User or warehouse information is missing');
      }

      if (!purchaseState.supplier) {
        throw new Error('Supplier is required');
      }

      if (purchaseState.items.length === 0) {
        throw new Error('At least one item is required');
      }

      const now = new Date();
      const dueDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

      // Determine status based on payment
      let status: PurchaseStatus;
      if (purchaseState.amountPaid >= purchaseState.total) {
        status = PurchaseStatus.PAID;
      } else if (purchaseState.amountPaid > 0) {
        status = PurchaseStatus.PARTIALLY_PAID;
      } else {
        status = PurchaseStatus.UNPAID;
      }

      if (purchaseState.mode === 'edit' && purchaseState.originalPurchaseUuid) {
        // Update existing purchase
        const updateData: UpdatePurchaseDto = {
          supplierUuid: purchaseState.supplier.uuid,
          warehouseUuid,
          useTax: purchaseState.taxEnabled,
          taxRate: purchaseState.taxRate,
          paymentMethod: purchaseState.paymentMethod as PaymentMethods,
          purchaseDate: now.toISOString(),
          dueDate: dueDate.toISOString(),
          status,
          notes: purchaseState.notes || undefined,
          updatedBy: userUuid,
          purchaseItems: purchaseState.items.map((item, index) => ({
            productUuid: item.productUuid,
            name: item.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            order: item.order ?? index,
          })),
        };

        const result = await updatePurchase(purchaseState.originalPurchaseUuid, updateData);
        return { success: true, data: result };
      } else {
        // Create new purchase
        const createData: CreatePurchaseDto = {
          supplierUuid: purchaseState.supplier.uuid,
          warehouseUuid,
          useTax: purchaseState.taxEnabled,
          taxRate: purchaseState.taxRate,
          paymentMethod: purchaseState.paymentMethod as PaymentMethods,
          purchaseDate: now.toISOString(),
          dueDate: dueDate.toISOString(),
          status,
          notes: purchaseState.notes || undefined,
          createdBy: userUuid,
          updatedBy: userUuid,
          purchaseItems: purchaseState.items.map((item, index) => ({
            productUuid: item.productUuid,
            name: item.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            order: item.order ?? index,
          })),
        };

        const result = await createPurchase(createData);
        return { success: true, data: result };
      }
    } catch (error) {
      console.error('Error saving purchase:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save purchase'
      };
    }
  }, [warehouseUuid, userUuid]);

  const loadPurchase = useCallback(async (purchaseId: string) => {
    try {
      const purchase = await getPurchase(purchaseId);
      return purchase;
    } catch (error) {
      console.error('Error loading purchase:', error);
      return null;
    }
  }, []);

  const createNewSupplier = useCallback(async (supplierData: CreateSupplierDto): Promise<Supplier> => {
    try {
      // Add warehouse UUID if not provided
      const supplierWithWarehouse = {
        ...supplierData,
        warehouseUuid: supplierData.warehouseUuid || warehouseUuid,
      };

      const newSupplier = await createSupplier(supplierWithWarehouse);
      return newSupplier;
    } catch (error) {
      console.error('Error creating supplier:', error);
      throw error;
    }
  }, [warehouseUuid]);

  const getDefaultSupplier = useCallback(async (): Promise<Supplier | null> => {
    try {
      if (!warehouseUuid) return null;

      const response = await fetch(`${API_BASE_URL}/suppliers/default?warehouseUuid=${warehouseUuid}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        return null; // No default supplier is not an error
      }

      const supplier = await response.json();
      return supplier;
    } catch (error) {
      console.error('Error getting default supplier:', error);
      return null;
    }
  }, [warehouseUuid]);

  const searchSuppliers = useCallback(async (query: string): Promise<Supplier[]> => {
    try {
      if (!warehouseUuid || !query.trim()) return [];

      const response = await fetch(`${API_BASE_URL}/suppliers/filter`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          filter: {
            warehouseUuid,
            name: query.trim(),
          },
          paginationQuery: {
            page: 1,
            limit: 10,
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to search suppliers: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Error searching suppliers:', error);
      return [];
    }
  }, [warehouseUuid]);

  const validateStock = useCallback(async (items: any[]): Promise<{ valid: boolean; errors: string[] }> => {
    try {
      if (!warehouseUuid) {
        return { valid: false, errors: ['Warehouse information is missing'] };
      }

      const response = await fetch(`${API_BASE_URL}/products/validate-stock`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          warehouseUuid,
          items: items.map(item => ({
            productUuid: item.productUuid,
            quantity: item.quantity,
          })),
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to validate stock: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error validating stock:', error);
      return { valid: true, errors: [] }; // Don't block purchase creation on validation errors
    }
  }, [warehouseUuid]);

  return {
    savePurchase,
    loadPurchase,
    createNewSupplier,
    getDefaultSupplier,
    searchSuppliers,
    validateStock,
  };
}
