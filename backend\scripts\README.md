# Seed Scripts

This directory contains scripts for seeding the database with test data.

## Available Scripts

### Individual Seed Scripts

1. **`seed-customers.ts`** - Creates customers using the customer controller API
   - Creates 1000 customers with realistic Algerian names and addresses
   - Creates regions for each warehouse
   - Assigns customers to warehouses and regions
   - Sets initial credit balances based on customer type
   - Uses the customer controller API for consistency

2. **`seed-products-postgresql.ts`** - Creates products and product categories
   - Creates product categories for each warehouse
   - Creates 1000 products per warehouse
   - Sets realistic prices and descriptions

3. **`seed-suppliers.ts`** - Creates suppliers using the supplier controller API
   - Creates 500 suppliers with realistic Algerian company data
   - Distributes suppliers across all warehouses
   - Generates authentic company names, addresses, and contact information
   - Uses the supplier controller API for consistency

4. **`seed-user-account-plans.js`** - Creates user account plans and features
   - Creates default account plans (Free, Basic, Professional, Enterprise)
   - Assigns features to each plan

### Master Seed Script

**`seed_all.ts`** - Runs all seed scripts in the correct order
- Orchestrates execution of all seed scripts
- Performs database backup before seeding
- Checks prerequisites (API availability, warehouses)
- Provides detailed progress tracking and error handling
- Stops execution if required scripts fail

## Usage

### Prerequisites

1. Ensure your backend server is running on `http://localhost:8000`
2. Create at least one warehouse before running seed scripts
3. Make sure you have the required dependencies installed

### Running Individual Scripts

```bash
# Run customer seeding
npx ts-node scripts/seed-customers.ts

# Run product seeding
npx ts-node scripts/seed-products-postgresql.ts

# Run supplier seeding
npx ts-node scripts/seed-suppliers.ts

# Run user account plans seeding
node scripts/seed-user-account-plans.js
```

### Running All Scripts

```bash
# Run all seed scripts in the correct order
npx ts-node scripts/seed_all.ts
```

## Script Order

The master script runs scripts in this order:

1. **User Account Plans** (optional) - Creates account plans and features
2. **Products** (required) - Creates product categories and products
3. **Suppliers** (required) - Creates suppliers with company data
4. **Customers** (required) - Creates customers, regions, and credit balances

## Features

### Customer Seeding (`seed-customers.ts`)

- **Realistic Data**: Uses authentic Algerian names, addresses, and phone numbers
- **Geographic Distribution**: Distributes customers across Algiers metropolitan area
- **Customer Types**: Creates retail, wholesale, mid-wholesale, and institutional customers
- **Credit Management**: Sets initial credit balances based on customer type
- **API Integration**: Uses customer controller API for consistency
- **Batch Processing**: Processes customers in batches to avoid overwhelming the server
- **Error Handling**: Retries failed operations with exponential backoff

### Master Script (`seed_all.ts`)

- **Prerequisite Checking**: Verifies API availability and warehouse existence
- **Database Backup**: Performs full backup before seeding
- **Progress Tracking**: Shows detailed progress for each script
- **Error Handling**: Stops execution if required scripts fail
- **Timeout Protection**: 15-minute timeout per script
- **Summary Report**: Provides detailed results summary

### Supplier Seeding (`seed-suppliers.ts`)

- **Realistic Data**: Uses authentic Algerian company names and addresses
- **Geographic Distribution**: Distributes suppliers across Algiers metropolitan area
- **Company Types**: Creates various business types (SARL, EURL, SPA, etc.)
- **Supplier Categories**: Covers 25 different supplier types and industries
- **API Integration**: Uses supplier controller API for consistency
- **Batch Processing**: Processes suppliers in batches to avoid overwhelming the server
- **Error Handling**: Retries failed operations with exponential backoff

## Configuration

### Customer Seeding Configuration

```typescript
const TOTAL_CUSTOMERS = 1000;    // Number of customers to create
const TOTAL_REGIONS = 15;        // Number of regions per warehouse
const BATCH_SIZE = 50;           // Customers per batch
const API_BASE_URL = 'http://localhost:8000';
```

### Supplier Seeding Configuration

```typescript
const TOTAL_SUPPLIERS = 500;     // Number of suppliers to create
const BATCH_SIZE = 50;           // Suppliers per batch
const API_BASE_URL = 'http://localhost:8000';
```

### Master Script Configuration

```typescript
const SEED_SCRIPTS = [
  {
    name: 'User Account Plans',
    scriptPath: 'seed-user-account-plans.js',
    required: false,
    order: 1,
    estimatedTime: '30 seconds'
  },
  // ... other scripts
];
```

## Error Handling

- **API Unavailable**: Scripts check API availability before starting
- **Missing Warehouses**: Scripts verify warehouses exist before seeding
- **Duplicate Data**: Scripts handle duplicate regions gracefully
- **Network Issues**: Retry logic with exponential backoff
- **Timeout Protection**: 15-minute timeout per script

## Backup

All scripts perform a full database backup before execution using `create_backup.py`. If backup fails, scripts continue with a warning.

## Output

Scripts provide detailed console output including:
- Progress indicators
- Success/failure counts
- Error messages
- Timing information
- Summary statistics

## Troubleshooting

### Common Issues

1. **API Not Available**
   - Ensure backend server is running on port 8000
   - Check if the server is healthy at `/health` endpoint

2. **No Warehouses Found**
   - Create at least one warehouse before running seed scripts
   - Use the warehouses API to create warehouses

3. **Script Timeout**
   - Increase timeout in master script if needed
   - Check server performance and database connection

4. **Duplicate Data Errors**
   - Scripts handle duplicates gracefully
   - Check if data already exists before running

### Debug Mode

Add more detailed logging by modifying the console.log statements in the scripts.

## Dependencies

- `axios` - HTTP client for API calls
- `ts-node` - TypeScript execution
- `child_process` - Process execution
- `path` - Path utilities
- `fs` - File system utilities

## Notes

- Scripts are designed to be idempotent (safe to run multiple times)
- Customer data is realistic but fictional
- Geographic coordinates are based on Algiers, Algeria
- Credit balances are randomly generated based on customer type
- All scripts include proper error handling and logging 