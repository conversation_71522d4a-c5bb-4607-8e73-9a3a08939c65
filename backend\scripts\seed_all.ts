/**
 * Master Seed Script - Runs all seed scripts in the correct order
 * 
 * Usage: npx ts-node scripts/seed_all.ts
 * 
 * This script orchestrates the execution of all seed scripts in the proper sequence:
 * 1. User Account Plans (if needed)
 * 2. Products
 * 3. Customers
 * 
 * ⚠️ CRITICAL: This script performs a full database backup before execution
 */

import axios from 'axios';
const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');
const fs = require('fs');

// Node.js global declarations
declare const process: any;
declare const require: any;
declare const module: any;
declare const __dirname: string;

// Configuration
const API_BASE_URL = 'http://localhost:8000';

// Seed script configurations
interface SeedScript {
  name: string;
  description: string;
  scriptPath: string;
  required: boolean;
  order: number;
  estimatedTime: string;
}

const SEED_SCRIPTS: SeedScript[] = [
  {
    name: 'User Account Plans',
    description: 'Creates default user account plans and features',
    scriptPath: 'seed-user-account-plans.js',
    required: false,
    order: 1,
    estimatedTime: '30 seconds'
  },
  {
    name: 'Products',
    description: 'Creates product categories and products for each warehouse',
    scriptPath: 'seed-products-postgresql.ts',
    required: true,
    order: 2,
    estimatedTime: '5-10 minutes'
  },
  {
    name: 'Suppliers',
    description: 'Creates suppliers with realistic Algerian company data',
    scriptPath: 'seed-suppliers.ts',
    required: true,
    order: 3,
    estimatedTime: '3-5 minutes'
  },
  {
    name: 'Customers',
    description: 'Creates customers with regions and credit balances',
    scriptPath: 'seed-customers.ts',
    required: true,
    order: 4,
    estimatedTime: '3-5 minutes'
  }
];

// Sort scripts by order
const SORTED_SCRIPTS = [...SEED_SCRIPTS].sort((a, b) => a.order - b.order);

interface SeedResult {
  script: SeedScript;
  success: boolean;
  error?: string;
  duration: number;
  output?: string;
}

async function checkApiAvailability(): Promise<void> {
  console.log('🔍 Checking API availability...');
  
  try {
    await axios.get(`${API_BASE_URL}/health`, { timeout: 5000 });
    console.log('✅ API is available and responding');
  } catch (error) {
    throw new Error(`API not available at ${API_BASE_URL}. Please ensure the backend server is running.`);
  }
}

async function performFullDatabaseBackup(): Promise<void> {
  console.log('⚠️  Performing full database backup before seeding...');
  
  try {
    const backupScriptPath = path.join(__dirname, 'create_backup.py');
    const execAsync = promisify(exec);
    
    console.log('Running backup script...');
    const { stdout, stderr } = await execAsync(`python "${backupScriptPath}"`);
    
    if (stderr) {
      console.warn('Backup script warnings:', stderr);
    }
    
    console.log('✅ Backup completed successfully');
  } catch (error) {
    console.error('❌ Backup failed:', error);
    console.error('⚠️  Proceeding without backup (use at your own risk)');
  }
}

async function checkPrerequisites(): Promise<void> {
  console.log('🔍 Checking prerequisites...');
  
  // Check if warehouses exist (required for products and customers)
  try {
    const response = await axios.get(`${API_BASE_URL}/warehouses`);
    const warehouseCount = response.data.data.length;
    
    if (warehouseCount === 0) {
      throw new Error('No warehouses found. Please create at least one warehouse before running seed scripts.');
    }
    
    console.log(`✅ Found ${warehouseCount} warehouse(s)`);
  } catch (error) {
    if (error.response?.status === 404) {
      throw new Error('Warehouses endpoint not found. Please ensure the backend server is running and warehouses module is available.');
    }
    throw error;
  }
}

async function runSeedScript(script: SeedScript): Promise<SeedResult> {
  const startTime = Date.now();
  const scriptPath = path.join(__dirname, script.scriptPath);
  
  console.log(`\n🚀 Running ${script.name}...`);
  console.log(`📝 Description: ${script.description}`);
  console.log(`⏱️  Estimated time: ${script.estimatedTime}`);
  console.log(`📁 Script: ${script.scriptPath}`);
  console.log('─'.repeat(60));
  
  try {
    const execAsync = promisify(exec);
    
    // Determine the command based on file extension
    let command: string;
    if (script.scriptPath.endsWith('.ts')) {
      command = `npx ts-node "${scriptPath}"`;
    } else if (script.scriptPath.endsWith('.js')) {
      command = `node "${scriptPath}"`;
    } else {
      throw new Error(`Unsupported script type: ${script.scriptPath}`);
    }
    
    const { stdout, stderr } = await execAsync(command, {
      cwd: path.dirname(__dirname), // Run from backend directory
      timeout: 15 * 60 * 1000 // 15 minutes timeout
    });
    
    const duration = Date.now() - startTime;
    
    if (stderr) {
      console.warn(`⚠️  ${script.name} warnings:`, stderr);
    }
    
    console.log(`✅ ${script.name} completed successfully in ${(duration / 1000).toFixed(1)}s`);
    
    return {
      script,
      success: true,
      duration,
      output: stdout
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error.message || 'Unknown error occurred';
    
    console.error(`❌ ${script.name} failed after ${(duration / 1000).toFixed(1)}s`);
    console.error(`Error: ${errorMessage}`);
    
    return {
      script,
      success: false,
      error: errorMessage,
      duration
    };
  }
}

async function runAllSeedScripts(): Promise<SeedResult[]> {
  const results: SeedResult[] = [];
  
  console.log('\n🌱 Starting seed script execution...');
  console.log('='.repeat(60));
  
  for (const script of SORTED_SCRIPTS) {
    const result = await runSeedScript(script);
    results.push(result);
    
    // If a required script fails, stop execution
    if (!result.success && script.required) {
      console.error(`\n❌ Required script "${script.name}" failed. Stopping execution.`);
      break;
    }
    
    // Add a small delay between scripts
    if (script !== SORTED_SCRIPTS[SORTED_SCRIPTS.length - 1]) {
      console.log('\n⏳ Waiting 3 seconds before next script...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  return results;
}

function displayResults(results: SeedResult[]): void {
  console.log('\n📊 SEEDING RESULTS SUMMARY');
  console.log('='.repeat(60));
  
  let totalDuration = 0;
  let successCount = 0;
  let failureCount = 0;
  
  for (const result of results) {
    const status = result.success ? '✅ SUCCESS' : '❌ FAILED';
    const duration = (result.duration / 1000).toFixed(1);
    totalDuration += result.duration;
    
    console.log(`${status} | ${result.script.name.padEnd(20)} | ${duration}s`);
    
    if (result.success) {
      successCount++;
    } else {
      failureCount++;
      if (result.error) {
        console.log(`    Error: ${result.error}`);
      }
    }
  }
  
  console.log('─'.repeat(60));
  console.log(`Total Duration: ${(totalDuration / 1000).toFixed(1)}s`);
  console.log(`Successful: ${successCount}/${results.length}`);
  console.log(`Failed: ${failureCount}/${results.length}`);
  
  if (failureCount === 0) {
    console.log('\n🎉 All seed scripts completed successfully!');
  } else {
    console.log('\n⚠️  Some seed scripts failed. Check the output above for details.');
  }
}

function displayScriptList(): void {
  console.log('\n📋 AVAILABLE SEED SCRIPTS');
  console.log('='.repeat(60));
  
  for (const script of SORTED_SCRIPTS) {
    const required = script.required ? 'REQUIRED' : 'OPTIONAL';
    console.log(`${script.order}. ${script.name.padEnd(20)} | ${required.padEnd(10)} | ${script.estimatedTime}`);
    console.log(`   ${script.description}`);
    console.log(`   Script: ${script.scriptPath}`);
    console.log('');
  }
}

async function main() {
  console.log('🚀 Starting Master Seed Script');
  console.log('=====================================');
  console.log('This script will run all seed scripts in the correct order.');
  console.log('Make sure your backend server is running before proceeding.');
  console.log('');
  
  try {
    // Display available scripts
    displayScriptList();
    
    // Check API availability
    await checkApiAvailability();
    
    // Perform backup
    await performFullDatabaseBackup();
    
    // Check prerequisites
    await checkPrerequisites();
    
    // Run all seed scripts
    const results = await runAllSeedScripts();
    
    // Display results
    displayResults(results);
    
    console.log('\n=====================================');
    console.log('🎯 Master seed script execution completed!');
    
    // Exit with appropriate code
    const hasFailures = results.some(r => !r.success && r.script.required);
    process.exit(hasFailures ? 1 : 0);
    
  } catch (error) {
    console.error('\n❌ Master seed script failed:', error.message);
    console.error('=====================================');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

export { main as seedAll, SEED_SCRIPTS, SORTED_SCRIPTS }; 