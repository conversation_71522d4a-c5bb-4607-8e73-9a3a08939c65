// customersApi.ts - API utilities for Customers endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api/customers';

export interface Customer {
  uuid: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  warehouseUuid: string;
  warehouseUuidString?: string;
  regionUuid?: string;
  regionUuidString?: string;
  creditLimit: number;
  currentCredit: number;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomerDto {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  warehouseUuid: string;
  regionUuid?: string;
  creditLimit?: number;
}

export interface UpdateCustomerDto {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  rc?: string;
  articleNumber?: string;
  customerType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  regionUuid?: string;
  creditLimit?: number;
}

export interface FilterCustomerDto {
  warehouseUuid?: string;
  name?: string;
  email?: string;
  phone?: string;
  fiscalId?: string;
  customerType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  regionUuid?: string;
  currentCredit?: number;
  minCredit?: number;
  maxCredit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationQueryDto {
  page?: number;
  limit?: number;
}

export interface PaginatedResponseDto<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface CustomerSale {
  uuid: string;
  customerUuid: string;
  totalAmount: number;
  amountPaid: number;
  balanceDue: number;
  status: string;
  paymentMethod?: string;
  invoiceNumber?: string;
  invoiceDate?: string;
  createdAt: string;
}

/**
 * Get all customers with pagination and filtering
 */
export async function getCustomers(
  paginationQuery: PaginationQueryDto = { page: 1, limit: 10 }, 
  filter?: FilterCustomerDto
): Promise<PaginatedResponseDto<Customer>> {
  const params = new URLSearchParams();
  
  // Use paginationQuery values for pagination
  const page = paginationQuery.page;
  const limit = paginationQuery.limit;
  if (page) params.set('page', String(page));
  if (limit) params.set('limit', String(limit));

  // Add filter parameters
  if (filter?.warehouseUuid) params.set('warehouseUuid', filter.warehouseUuid);
  if (filter?.name) params.set('name', filter.name);
  if (filter?.email) params.set('email', filter.email);
  if (filter?.phone) params.set('phone', filter.phone);
  if (filter?.fiscalId) params.set('fiscalId', filter.fiscalId);
  if (filter?.customerType) params.set('customerType', filter.customerType);
  if (filter?.regionUuid) params.set('regionUuid', filter.regionUuid);
  if (filter?.currentCredit !== undefined) params.set('currentCredit', String(filter.currentCredit));
  if (filter?.minCredit !== undefined) params.set('minCredit', String(filter.minCredit));
  if (filter?.maxCredit !== undefined) params.set('maxCredit', String(filter.maxCredit));
  if (filter?.sortBy) params.set('sortBy', filter.sortBy);
  if (filter?.sortOrder) params.set('sortOrder', filter.sortOrder);

  const res = await axios.get(`${API_BASE}?${params.toString()}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Filter customers using POST endpoint (for POS modal and advanced filtering)
 */
export async function filterCustomers(
  filter?: FilterCustomerDto,
  paginationQuery: PaginationQueryDto = { page: 1, limit: 10 }
): Promise<PaginatedResponseDto<Customer>> {
  console.log('Filter customers API call:', { filter, paginationQuery });
  
  const res = await axios.post(`${API_BASE}/filter`, filter, {
    headers: getAxiosAuthHeaders(),
    params: paginationQuery,
  });
  return res.data;
}

/**
 * Get a single customer by UUID
 */
export async function getCustomer(uuid: string): Promise<Customer> {
  const res = await axios.get(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Get customer sales history
 */
export async function getCustomerSales(
  customerUuid: string,
  paginationQuery: PaginationQueryDto = { page: 1, limit: 10 }
): Promise<PaginatedResponseDto<CustomerSale>> {
  const params = new URLSearchParams();
  if (paginationQuery.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery.limit) params.append('limit', paginationQuery.limit.toString());

  const res = await axios.get(`${API_BASE}/${customerUuid}/sales?${params.toString()}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Create a new customer
 */
export async function createCustomer(data: CreateCustomerDto): Promise<Customer> {
  const res = await axios.post(API_BASE, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Update an existing customer
 */
export async function updateCustomer(uuid: string, data: UpdateCustomerDto): Promise<Customer> {
  const res = await axios.patch(`${API_BASE}/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Delete a customer (soft delete)
 */
export async function deleteCustomer(uuid: string): Promise<void> {
  await axios.delete(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
}

/**
 * Get a single customer by UUID (alias for getCustomer)
 */
export async function getCustomerByUuid(uuid: string): Promise<Customer> {
  return getCustomer(uuid);
}
