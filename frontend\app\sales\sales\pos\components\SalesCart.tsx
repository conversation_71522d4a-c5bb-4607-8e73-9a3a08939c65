import React, { useRef, useEffect, useState } from 'react';
import { formatCurrency } from '../utils/posHelpers';
import { PaymentSelector } from './PaymentSelector';
import { TaxControls } from './TaxControls';
import { NotesControls } from './NotesControls';
import type { SalesCartProps } from '../types';

export function SalesCart({
  items,
  selectedCustomer,
  notes,
  subtotal,
  tax,
  total,
  isSubmitting,
  error,
  paymentMethod,
  amountPaid,
  taxEnabled,
  taxRate,
  notesEnabled,
  onRemoveItem,
  onUpdateQuantity,
  onUpdatePrice,
  onNotesChange,
  onSubmit,
  onCustomerSelect,
  onPaymentMethodChange,
  onAmountPaidChange,
  onTaxEnabledChange,
  onTaxRateChange,
  onNotesEnabledChange,
  highlightedCartItemUuid,
  isEditMode = false,
  isLoadMode = false,
  isLoading = false,
  disabled = false,
}: SalesCartProps & { isEditMode?: boolean; isLoadMode?: boolean; isLoading?: boolean }) {
  return (
    <aside className="bg-gray-50 border-l border-gray-200 w-full lg:min-w-80 lg:max-w-96 h-full flex flex-col">
      {/* Error Display - Fixed */}
      {error && (
        <div className="p-3 bg-red-50 border-b border-red-200 flex-shrink-0">
          <div className="text-red-700 text-xs">
            {error}
          </div>
        </div>
      )}

      {/* Customer Selection - Fixed */}
      <div className="p-3 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center justify-between mb-1">
          <label className="block text-xs font-medium text-gray-700">
            Customer <span className="text-red-500">*</span>
          </label>
          <span className="text-xs text-gray-500">Required</span>
        </div>
        <button
          onClick={onCustomerSelect}
          disabled={isLoading || disabled}
          className={`w-full p-2 text-left border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center space-x-2 ${
            selectedCustomer 
              ? 'border-green-300 bg-green-50' 
              : 'border-red-300 bg-red-50'
          } ${
            (isLoading || disabled)
              ? 'cursor-not-allowed opacity-50' 
              : 'hover:bg-gray-50'
          }`}
        >
          <svg 
            className={`w-4 h-4 flex-shrink-0 ${
              selectedCustomer ? 'text-green-600' : 'text-red-500'
            }`}
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" 
            />
          </svg>
          {selectedCustomer ? (
            <div className="flex-1 min-w-0">
              <div className="font-medium text-gray-900 truncate text-sm">{selectedCustomer.name}</div>
              {selectedCustomer.phone && (
                <div className="text-xs text-gray-500 truncate">{selectedCustomer.phone}</div>
              )}
            </div>
          ) : (
            <span className="text-red-600 flex-1 text-sm font-medium">Select customer (required)</span>
          )}
          <svg 
            className={`w-3 h-3 flex-shrink-0 ${
              selectedCustomer ? 'text-green-600' : 'text-red-500'
            }`}
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M19 9l-7 7-7-7" 
            />
          </svg>
        </button>
        {!selectedCustomer && (
          <div className="mt-1 text-xs text-red-600">
            A customer must be selected to complete the sale
          </div>
        )}
      </div>

      {/* Cart Content - Scrollable area */}
      <div className="flex-1 min-h-0 p-3">
        <style jsx>{`
          .cart-vertical-scroll::-webkit-scrollbar {
            width: 6px;
          }
          .cart-vertical-scroll::-webkit-scrollbar-track {
            background: #f3f4f6;
            border-radius: 3px;
          }
          .cart-vertical-scroll::-webkit-scrollbar-thumb {
            background: #9ca3af;
            border-radius: 3px;
          }
          .cart-vertical-scroll::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
          }
        `}</style>
        
        {isLoading ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-3"></div>
            <div className="text-base font-medium text-gray-600 mb-1">
              Loading...
            </div>
            <div className="text-xs text-gray-500">
              Please wait while we load the sale data
            </div>
          </div>
        ) : items.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-6">
            <div className="text-base font-medium text-gray-600 mb-1">
              No items added yet
            </div>
            <div className="text-xs text-gray-500">
              Select products to add to the sale
            </div>
          </div>
        ) : (
          <div 
            className="cart-vertical-scroll overflow-y-auto overflow-x-hidden pr-1 h-full"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: '#9ca3af #f3f4f6',
            }}
          >
            <div className="space-y-2">
              {items.map((item) => {
                const isHighlighted = highlightedCartItemUuid === item.productUuid;
                return (
                  <CartItem 
                    key={item.productUuid}
                    item={item}
                    isHighlighted={isHighlighted}
                    onUpdateQuantity={onUpdateQuantity}
                    onUpdatePrice={onUpdatePrice}
                    onRemoveItem={onRemoveItem}
                    disabled={isLoading || disabled}
                  />
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Footer - Fixed at bottom */}
      <div className="border-t border-gray-200 p-3 flex-shrink-0 bg-white">
        {/* Tax Controls */}
        <TaxControls
          taxEnabled={taxEnabled}
          taxRate={taxRate}
          onTaxEnabledChange={onTaxEnabledChange}
          onTaxRateChange={onTaxRateChange}
          disabled={isLoading || disabled}
        />

        {/* Payment Method and Amount */}
        <PaymentSelector
          selectedMethod={paymentMethod}
          onMethodChange={onPaymentMethodChange}
          total={total}
          amountPaid={amountPaid}
          onAmountPaidChange={onAmountPaidChange}
          disabled={isLoading || disabled}
        />

        {/* Notes Controls */}
        <NotesControls
          notesEnabled={notesEnabled}
          notes={notes}
          onNotesEnabledChange={onNotesEnabledChange}
          onNotesChange={onNotesChange}
          disabled={isLoading || disabled}
        />

        {/* Order Summary */}
        <div className="space-y-1 mb-3">
          <div className="flex justify-between">
            <span className="text-gray-600 text-sm">Subtotal:</span>
            <span className="font-medium text-gray-900 text-sm">{formatCurrency(subtotal)}</span>
          </div>
          {taxEnabled && (
            <div className="flex justify-between">
              <span className="text-gray-600 text-sm">
                Tax ({((typeof taxRate === 'number' && !isNaN(taxRate) ? taxRate : 0) * 100).toFixed(1)}%):
              </span>
              <span className="font-medium text-gray-900 text-sm">{formatCurrency(tax)}</span>
            </div>
          )}
          <div className="flex justify-between border-t pt-1">
            <span className="text-gray-600 text-sm">Total:</span>
            <span className="text-base font-bold text-gray-900">{formatCurrency(total)}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <button
            onClick={onSubmit}
            disabled={items.length === 0 || !paymentMethod || !selectedCustomer || isSubmitting}
            className="w-full bg-green-600 text-white py-2 px-3 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 font-medium disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            {isSubmitting ? 'Processing...' : (
              isEditMode ? 'Update Sale (F4)' : 
              isLoadMode ? 'Continue Sale' : 
              'Complete Sale (F4)'
            )}
          </button>
          
          {items.length > 0 && !isEditMode && !isLoadMode && (
            <button
              onClick={() => {
                if (window.confirm('Are you sure you want to clear the cart?')) {
                  window.dispatchEvent(new CustomEvent('clearCart'));
                }
              }}
              className="w-full bg-gray-600 text-white py-1.5 px-3 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm"
              disabled={isSubmitting}
            >
              Clear Cart
            </button>
          )}
          
          {isEditMode && (
            <button
              onClick={() => {
                // Clear URL parameters and navigate to clean sales page
                const newUrl = new URL(window.location.href);
                newUrl.searchParams.delete('edit');
                newUrl.searchParams.delete('view');
                window.location.href = newUrl.toString();
              }}
              className="w-full bg-gray-600 text-white py-1.5 px-3 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm"
              disabled={isSubmitting}
            >
              Cancel Edit
            </button>
          )}
        </div>
      </div>
    </aside>
  );
}

// CartItem component with auto-focus functionality - Compact design
interface CartItemProps {
  item: any;
  isHighlighted: boolean;
  onUpdateQuantity: (productUuid: string, quantity: number) => void;
  onUpdatePrice: (productUuid: string, unitPrice: number) => void;
  onRemoveItem: (productUuid: string) => void;
  disabled?: boolean;
}

function CartItem({ item, isHighlighted, onUpdateQuantity, onUpdatePrice, onRemoveItem, disabled = false }: CartItemProps) {
  const quantityInputRef = useRef<HTMLInputElement>(null);
  const priceInputRef = useRef<HTMLInputElement>(null);
  
  // Use string state for input values to maintain cursor position
  const [quantityInputValue, setQuantityInputValue] = useState('');
  const [priceInputValue, setPriceInputValue] = useState('');
  const [quantityCursorPosition, setQuantityCursorPosition] = useState(0);
  const [priceCursorPosition, setPriceCursorPosition] = useState(0);

  // Ensure quantity and unitPrice are always valid numbers
  const safeQuantity = typeof item.quantity === 'number' && !isNaN(item.quantity) ? item.quantity : 0;
  const safeUnitPrice = typeof item.unitPrice === 'number' && !isNaN(item.unitPrice) ? item.unitPrice : 0;

  // Initialize input values when item changes
  useEffect(() => {
    setQuantityInputValue(safeQuantity.toString());
    setPriceInputValue(safeUnitPrice.toString());
  }, [safeQuantity, safeUnitPrice]);

  // Auto-focus quantity input when item is highlighted
  useEffect(() => {
    if (isHighlighted && quantityInputRef.current) {
      // Add a small delay to ensure the DOM is ready and the component is fully rendered
      const timer = setTimeout(() => {
        if (quantityInputRef.current) {
          quantityInputRef.current.focus();
          quantityInputRef.current.select();
  
        }
      }, 100); // Small delay to ensure DOM is ready
      
      return () => clearTimeout(timer);
    }
  }, [isHighlighted, item.productUuid]);

  // Restore cursor position after value updates
  useEffect(() => {
    if (quantityInputRef.current && quantityCursorPosition > 0) {
      quantityInputRef.current.setSelectionRange(quantityCursorPosition, quantityCursorPosition);
    }
  }, [quantityInputValue, quantityCursorPosition]);

  useEffect(() => {
    if (priceInputRef.current && priceCursorPosition > 0) {
      priceInputRef.current.setSelectionRange(priceCursorPosition, priceCursorPosition);
    }
  }, [priceInputValue, priceCursorPosition]);

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart || 0;
    
    setQuantityInputValue(value);
    setQuantityCursorPosition(cursorPos);
    
    const numValue = parseFloat(value) || 0;
    onUpdateQuantity(item.productUuid, numValue);
  };

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart || 0;
    
    setPriceInputValue(value);
    setPriceCursorPosition(cursorPos);
    
    const numValue = parseFloat(value) || 0;
    onUpdatePrice(item.productUuid, numValue);
  };

  const handleQuantityBlur = () => {
    // Format the value on blur
    const numValue = parseFloat(quantityInputValue) || 0;
    setQuantityInputValue(numValue.toString());
  };

  const handlePriceBlur = () => {
    // Format the value on blur
    const numValue = parseFloat(priceInputValue) || 0;
    setPriceInputValue(numValue.toFixed(2));
  };

  return (
    <div 
      className={`${isHighlighted ? 'bg-yellow-50 border-yellow-400 border-2' : 'border border-gray-200'} p-2 rounded-md transition-all duration-300`}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <div className="font-medium text-gray-900 truncate mb-0.5 text-sm">
            {typeof item.name === 'string' ? item.name : 
             typeof (item as any).productName === 'string' ? (item as any).productName : 
             'Unknown Product'}
          </div>
          <div className="text-xs text-gray-600">
            {formatCurrency(safeUnitPrice)} each
          </div>
          <div className="flex items-center gap-2 mt-1.5">
            <div className="flex items-center gap-1">
              <label className="text-xs text-gray-500">Qty:</label>
              <input
                ref={quantityInputRef}
                type="number"
                min="0"
                step="0.01"
                value={quantityInputValue}
                onChange={handleQuantityChange}
                onBlur={handleQuantityBlur}
                disabled={disabled}
                className={`w-16 p-1 text-center border rounded text-xs transition-all duration-200 ${
                  isHighlighted 
                    ? 'border-yellow-400 border-2 bg-yellow-50 focus:ring-yellow-500 focus:border-yellow-500 shadow-sm' 
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                } focus:outline-none focus:ring-1 ${
                  disabled ? 'bg-gray-100 cursor-not-allowed opacity-50' : ''
                }`}
                placeholder={isHighlighted ? "Type quantity..." : ""}
              />
            </div>
            <div className="flex items-center gap-1">
              <label className="text-xs text-gray-500">Price:</label>
              <input
                ref={priceInputRef}
                type="number"
                min="0"
                step="0.01"
                value={priceInputValue}
                onChange={handlePriceChange}
                onBlur={handlePriceBlur}
                disabled={disabled}
                className={`w-20 p-1 text-center border rounded text-xs ${isHighlighted ? 'border-yellow-400 border-2 bg-yellow-50 focus:ring-yellow-500 focus:border-yellow-500' : 'border-gray-300'} focus:outline-none focus:ring-1 focus:ring-blue-500 ${
                  disabled ? 'bg-gray-100 cursor-not-allowed opacity-50' : ''
                }`}
              />
            </div>
          </div>
        </div>
        <div className="flex flex-col items-end">
          <button
            onClick={() => onRemoveItem(item.productUuid)}
            disabled={disabled}
            className={`p-0.5 rounded text-sm ${
              disabled 
                ? 'text-gray-400 cursor-not-allowed' 
                : 'text-red-500 hover:text-red-700 hover:bg-red-50'
            }`}
            title="Remove item"
          >
            ×
          </button>
          <div className="text-xs font-semibold mt-1">
            {formatCurrency(item.totalPrice)}
          </div>
        </div>
      </div>
    </div>
  );
} 