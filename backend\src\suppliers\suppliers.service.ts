import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Supplier } from "./supplier.entity";
import { Warehouse } from "../warehouses/warehouse.entity";
import { Uuid7 } from "../utils/uuid7";
import { FilterSupplierDto } from "./dto/filter-supplier.dto";

interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}

@Injectable()
export class SuppliersService {
  constructor(
    @InjectRepository(Supplier) private supplierRepository: Repository<Supplier>,
    @InjectRepository(Warehouse) private warehouseRepository: Repository<Warehouse>,
  ) {}

  async create(supplierData: any): Promise<Supplier> {
    console.log('[SuppliersService] create called with:', JSON.stringify(supplierData, null, 2));
    
    const { 
      warehouseUuid, 
      userUuid, 
      name, 
      email, 
      phone, 
      address, 
      code, 
      description,
      fiscalId,
      rc,
      articleNumber,
      latitude,
      longitude,
      notes
    } = supplierData;

    if (!userUuid || !name) {
      throw new BadRequestException("userUuid and name are required");
    }

    // Check if warehouse exists
    const warehouse = await this.warehouseRepository.findOne({
      where: { id: warehouseUuid },
    });
    if (!warehouse) {
      throw new NotFoundException("Warehouse not found");
    }

    const supplier = new Supplier();
    supplier.id = Supplier.generateId();
    supplier.userUuid = userUuid;
    supplier.warehouseUuid = warehouseUuid;
    supplier.name = name;
    supplier.email = email;
    supplier.phone = phone;
    supplier.address = address;
    supplier.code = code;
    supplier.description = description;
    supplier.fiscalId = fiscalId;
    supplier.rc = rc;
    supplier.articleNumber = articleNumber;
    supplier.latitude = latitude;
    supplier.longitude = longitude;
    supplier.notes = notes;
    supplier.isDeleted = false;

    const savedSupplier = await this.supplierRepository.save(supplier);
    console.log('[SuppliersService] Supplier created successfully:', savedSupplier.id);
    return savedSupplier;
  }

  async findAll(filter: FilterSupplierDto, page: number = 1, limit: number = 10): Promise<PaginatedResult<Supplier>> {
    console.log('[SuppliersService] findAll called with filter:', JSON.stringify(filter, null, 2));
    console.log('[SuppliersService] page:', page, 'limit:', limit);
    
    const queryBuilder = this.supplierRepository
      .createQueryBuilder('supplier')
      .where('supplier.isDeleted = :isDeleted', { isDeleted: false });

    // Apply filters
    if (filter.warehouseUuid) {
      queryBuilder.andWhere('supplier.warehouseUuid = :warehouseUuid', { warehouseUuid: filter.warehouseUuid });
    }

    if (filter.userUuid) {
      queryBuilder.andWhere('supplier.userUuid = :userUuid', { userUuid: filter.userUuid });
    }

    if (filter.name && filter.name.trim() !== '') {
      queryBuilder.andWhere('supplier.name ILIKE :name', { name: `%${filter.name.trim()}%` });
    }

    if (filter.email && filter.email.trim() !== '') {
      queryBuilder.andWhere('supplier.email ILIKE :email', { email: `%${filter.email.trim()}%` });
    }

    if (filter.phone && filter.phone.trim() !== '') {
      queryBuilder.andWhere('supplier.phone ILIKE :phone', { phone: `%${filter.phone.trim()}%` });
    }

    if (filter.code && filter.code.trim() !== '') {
      queryBuilder.andWhere('supplier.code ILIKE :code', { code: `%${filter.code.trim()}%` });
    }

    if (filter.fiscalId && filter.fiscalId.trim() !== '') {
      queryBuilder.andWhere('supplier.fiscalId ILIKE :fiscalId', { fiscalId: `%${filter.fiscalId.trim()}%` });
    }

    // Get total count
    const total = await queryBuilder.getCount();
    console.log('[SuppliersService] Total suppliers found:', total);

    // Apply pagination and sorting
    const offset = (page - 1) * limit;
    queryBuilder
      .orderBy('supplier.name', 'ASC')
      .offset(offset)
      .limit(limit);

    const data = await queryBuilder.getMany();
    console.log('[SuppliersService] Returning', data.length, 'suppliers for page', page);

    return {
      data,
      total,
      page,
      limit,
    };
  }

  // Legacy method for backward compatibility
  async findAllLegacy(warehouseUuid?: string, userUuid?: string): Promise<Supplier[]> {
    console.log('[SuppliersService] findAllLegacy called');
    console.log('[SuppliersService] warehouseUuid:', warehouseUuid, 'userUuid:', userUuid);
    
    const queryBuilder = this.supplierRepository
      .createQueryBuilder('supplier')
      .where('supplier.isDeleted = :isDeleted', { isDeleted: false });

    if (warehouseUuid) {
      queryBuilder.andWhere('supplier.warehouseUuid = :warehouseUuid', { warehouseUuid });
    }

    if (userUuid) {
      queryBuilder.andWhere('supplier.userUuid = :userUuid', { userUuid });
    }

    const suppliers = await queryBuilder.getMany();
    console.log('[SuppliersService] findAllLegacy returned', suppliers.length, 'suppliers');
    return suppliers;
  }

  async findOne(uuid: string, userUuid?: string): Promise<Supplier> {
    console.log('[SuppliersService] findOne called for UUID:', uuid);
    console.log('[SuppliersService] userUuid filter:', userUuid);
    
    const whereClause: any = { id: uuid, isDeleted: false };
    if (userUuid) {
      whereClause.userUuid = userUuid;
    }

    const supplier = await this.supplierRepository.findOne({
      where: whereClause,
    });
    if (!supplier) {
      console.log('[SuppliersService] Supplier not found for UUID:', uuid);
      throw new NotFoundException("Supplier not found");
    }
    
    console.log('[SuppliersService] Supplier found:', supplier.id);
    return supplier;
  }

  async update(uuid: string, updateData: any, userUuid?: string): Promise<Supplier> {
    console.log('[SuppliersService] update called for UUID:', uuid);
    console.log('[SuppliersService] updateData:', JSON.stringify(updateData, null, 2));
    console.log('[SuppliersService] userUuid filter:', userUuid);
    
    const whereClause: any = { id: uuid, isDeleted: false };
    if (userUuid) {
      whereClause.userUuid = userUuid;
    }

    const supplier = await this.supplierRepository.findOne({
      where: whereClause,
    });
    if (!supplier) {
      console.log('[SuppliersService] Supplier not found for update, UUID:', uuid);
      throw new NotFoundException("Supplier not found");
    }

    // Update fields
    Object.assign(supplier, updateData);
    const updatedSupplier = await this.supplierRepository.save(supplier);
    console.log('[SuppliersService] Supplier updated successfully:', updatedSupplier.id);
    return updatedSupplier;
  }

  async remove(uuid: string, userUuid?: string): Promise<void> {
    console.log('[SuppliersService] remove called for UUID:', uuid);
    console.log('[SuppliersService] userUuid filter:', userUuid);
    
    const whereClause: any = { id: uuid, isDeleted: false };
    if (userUuid) {
      whereClause.userUuid = userUuid;
    }

    const result = await this.supplierRepository.update(
      whereClause,
      { isDeleted: true }
    );
    if (result.affected === 0) {
      console.log('[SuppliersService] Supplier not found for deletion, UUID:', uuid);
      throw new NotFoundException("Supplier not found");
    }
    
    console.log('[SuppliersService] Supplier soft deleted successfully:', uuid);
  }

  async filterSuppliers(filters: { 
    warehouseUuid?: string; 
    userUuid?: string;
    name?: string; 
    email?: string;
    phone?: string;
    code?: string;
    fiscalId?: string;
  }): Promise<Supplier[]> {
    console.log('[SuppliersService] filterSuppliers called with filters:', JSON.stringify(filters, null, 2));
    
    const queryBuilder = this.supplierRepository
      .createQueryBuilder('supplier')
      .where('supplier.isDeleted = :isDeleted', { isDeleted: false });

    if (filters.warehouseUuid) {
      queryBuilder.andWhere('supplier.warehouseUuid = :warehouseUuid', { warehouseUuid: filters.warehouseUuid });
    }

    if (filters.userUuid) {
      queryBuilder.andWhere('supplier.userUuid = :userUuid', { userUuid: filters.userUuid });
    }

    if (filters.name && filters.name.trim() !== '') {
      queryBuilder.andWhere('supplier.name ILIKE :name', { name: `%${filters.name.trim()}%` });
    }

    if (filters.email && filters.email.trim() !== '') {
      queryBuilder.andWhere('supplier.email ILIKE :email', { email: `%${filters.email.trim()}%` });
    }

    if (filters.phone && filters.phone.trim() !== '') {
      queryBuilder.andWhere('supplier.phone ILIKE :phone', { phone: `%${filters.phone.trim()}%` });
    }

    if (filters.code && filters.code.trim() !== '') {
      queryBuilder.andWhere('supplier.code ILIKE :code', { code: `%${filters.code.trim()}%` });
    }

    if (filters.fiscalId && filters.fiscalId.trim() !== '') {
      queryBuilder.andWhere('supplier.fiscalId ILIKE :fiscalId', { fiscalId: `%${filters.fiscalId.trim()}%` });
    }

    const suppliers = await queryBuilder.getMany();
    console.log('[SuppliersService] filterSuppliers returned', suppliers.length, 'suppliers');
    return suppliers;
  }
} 