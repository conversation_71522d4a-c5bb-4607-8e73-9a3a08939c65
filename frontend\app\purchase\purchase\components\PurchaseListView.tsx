import React from 'react';
import { Purchase, FilterPurchaseDto } from '../purchaseApi';
import { PurchaseFilters } from './PurchaseFilters';
import ItemsTable from '@/components/itemsTable/ItemsTable';
import { FiEye, FiEdit, FiPrinter, FiX } from 'react-icons/fi';

interface PurchaseListViewProps {
  purchases: Purchase[];
  totalPages: number;
  currentPage: number;
  isLoading: boolean;
  isLoadingInvoice: boolean;
  isLoadingEditPurchase?: boolean;
  isLoadingViewDetails?: boolean;
  isLoadingPrintInvoice?: boolean;
  isLoadingCancel?: boolean;
  showFilters: boolean;
  filter: FilterPurchaseDto;
  onPageChange: (page: number) => void;
  onViewDetails: (purchase: Purchase) => void;
  onEdit: (purchase: Purchase) => void;
  onPrintInvoice: (purchase: Purchase) => void;
  onCancel: (purchase: Purchase) => void;
  onFilterChange: (filter: Partial<FilterPurchaseDto>) => void;
  onClearFilters: () => void;
  onToggleFilters: () => void;
}

// Helper function to get status badge color
const getStatusBadgeColor = (status: string) => {
  switch (status) {
    case 'paid':
      return 'bg-green-100 text-green-800';
    case 'partially_paid':
      return 'bg-yellow-100 text-yellow-800';
    case 'unpaid':
      return 'bg-red-100 text-red-800';
    case 'cancelled':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const PurchaseListView: React.FC<PurchaseListViewProps> = ({
  purchases,
  totalPages,
  currentPage,
  isLoading,
  isLoadingInvoice,
  isLoadingEditPurchase = false,
  isLoadingViewDetails = false,
  isLoadingPrintInvoice = false,
  isLoadingCancel = false,
  showFilters,
  filter,
  onPageChange,
  onViewDetails,
  onEdit,
  onPrintInvoice,
  onCancel,
  onFilterChange,
  onClearFilters,
  onToggleFilters,
}) => {
  // Check if any action is currently loading
  const isAnyActionLoading = isLoadingEditPurchase || isLoadingViewDetails || isLoadingPrintInvoice || isLoadingCancel;

  return (
    <div className="h-full flex flex-col overflow-y-auto p-8">
      <div className="max-w-7xl mx-auto w-full flex flex-col h-full">
        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Filters */}
          <PurchaseFilters
            showFilters={showFilters}
            filter={filter}
            onFilterChange={onFilterChange}
            onClearFilters={onClearFilters}
            onToggleFilters={onToggleFilters}
          />

          {/* Purchase List */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200">
            <ItemsTable
              columns={[
                {
                  key: 'purchaseOrderNumber',
                  header: 'Purchase Order #',
                  render: (value: string, row: Purchase) => (
                    <div className="font-semibold text-gray-900">{value}</div>
                  )
                },
                {
                  key: 'supplierName',
                  header: 'Supplier',
                  render: (value: string, row: Purchase) => (
                    <div className="text-sm text-gray-900">{row.supplierName || 'N/A'}</div>
                  )
                },
                {
                  key: 'status',
                  header: 'Status',
                  render: (value: string, row: Purchase) => (
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(value)}`}>
                      {value.replace('_', ' ')}
                    </span>
                  )
                },
                {
                  key: 'paymentMethod',
                  header: 'Payment Method',
                  render: (value: string, row: Purchase) => (
                    <div className="text-sm text-gray-900 capitalize">{value?.replace('_', ' ') || 'N/A'}</div>
                  )
                },
                {
                  key: 'totalAmount',
                  header: 'Total Amount',
                  render: (value: number, row: Purchase) => (
                    <div className="text-sm font-medium text-gray-900">${value?.toFixed(2) || '0.00'}</div>
                  )
                },
                {
                  key: 'purchaseDate',
                  header: 'Purchase Date',
                  render: (value: string, row: Purchase) => (
                    <div className="text-sm text-gray-900">
                      {value ? new Date(value).toLocaleDateString() : 'N/A'}
                    </div>
                  )
                },
                {
                  key: 'createdAt',
                  header: 'Created',
                  render: (value: string, row: Purchase) => (
                    <div className="text-sm text-gray-500">
                      {value ? new Date(value).toLocaleDateString() : 'N/A'}
                    </div>
                  )
                },
                {
                  key: 'actions',
                  header: 'Actions',
                  headerClassName: 'text-center',
                  cellClassName: 'text-center align-middle',
                  render: (_: any, row: Purchase) => (
                    <div className="flex items-center justify-center gap-2">
                      <button
                        onClick={() => onViewDetails(row)}
                        className="p-2 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="View Details"
                        aria-label="View Details"
                        disabled={isAnyActionLoading}
                      >
                        {isLoadingViewDetails ? (
                          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <FiEye className="w-4 h-4 text-blue-600" />
                        )}
                      </button>

                      <button
                        onClick={() => onEdit(row)}
                        className="p-2 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-400 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Edit Purchase"
                        aria-label="Edit Purchase"
                        disabled={isAnyActionLoading}
                      >
                        {isLoadingEditPurchase ? (
                          <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <FiEdit className="w-4 h-4 text-green-600" />
                        )}
                      </button>

                      <button
                        onClick={() => onPrintInvoice(row)}
                        className="p-2 rounded-full hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-400 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Print Invoice"
                        aria-label="Print Invoice"
                        disabled={isAnyActionLoading}
                      >
                        {isLoadingPrintInvoice ? (
                          <div className="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <FiPrinter className="w-4 h-4 text-purple-600" />
                        )}
                      </button>

                      {row.status !== 'cancelled' && (
                        <button
                          onClick={() => onCancel(row)}
                          className="p-2 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Cancel Purchase"
                          aria-label="Cancel Purchase"
                          disabled={isAnyActionLoading}
                        >
                          {isLoadingCancel ? (
                            <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <FiX className="w-4 h-4 text-red-600" />
                          )}
                        </button>
                      )}
                    </div>
                  )
                },
              ]}
              data={purchases}
              isLoading={isLoading}
              noDataText={
                <div className="text-center py-8">
                  <span className="text-gray-400">
                    {Object.values(filter).some(value => value !== undefined && value !== '')
                      ? 'No purchases match your filters.'
                      : 'No purchases found.'}
                  </span>
                  {Object.values(filter).some(value => value !== undefined && value !== '') && (
                    <button
                      onClick={onClearFilters}
                      className="ml-2 text-blue-600 hover:text-blue-700 underline"
                    >
                      Clear filters
                    </button>
                  )}
                </div>
              }
              containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
              pagination={{
                currentPage: currentPage,
                totalPages: totalPages,
                onPageChange: onPageChange,
                totalItems: purchases.length,
                itemsPerPage: 10,
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
