import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import type { Product } from '../types';

const API_BASE_URL = '/api';

interface ProductsResponse {
  data: Product[];
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export function usePOSProducts() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;

  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 13, // Match the POS config
    total: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [currentCategory, setCurrentCategory] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);

  const loadProducts = useCallback(async (page: number = 1, searchTerm: string = '') => {
    if (!warehouseUuid) return;

    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        warehouseUuid,
      });

      if (searchTerm.trim()) {
        params.append('search', searchTerm.trim());
      }

      if (currentCategory) {
        params.append('category', currentCategory);
      }

      const response = await fetch(`${API_BASE_URL}/products/filter?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to load products: ${response.statusText}`);
      }

      const data: ProductsResponse = await response.json();

      setProducts(data.data || []);
      setPagination({
        page: data.page,
        limit: data.limit,
        total: data.total,
        hasNext: data.hasNext,
        hasPrev: data.hasPrev,
      });
    } catch (error) {
      console.error('Error loading products:', error);
      setProducts([]);
      setPagination(prev => ({
        ...prev,
        page: 1,
        total: 0,
        hasNext: false,
        hasPrev: false,
      }));
    } finally {
      setIsLoading(false);
    }
  }, [warehouseUuid, pagination.limit, currentCategory]);

  const loadPage = useCallback(async (page: number, searchTerm: string = '') => {
    await loadProducts(page, searchTerm);
  }, [loadProducts]);

  const updateCategory = useCallback((category: string | null) => {
    setCurrentCategory(category);
  }, []);

  // Load categories on mount
  useEffect(() => {
    const loadCategories = async () => {
      if (!warehouseUuid) return;

      try {
        const response = await fetch(`${API_BASE_URL}/products/filter?warehouseUuid=${warehouseUuid}&returnCategoriesOnly=true`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        });

        if (response.ok) {
          const data = await response.json();
          setCategories(data.categories || []);
        }
      } catch (error) {
        console.error('Error loading categories:', error);
        setCategories([]);
      }
    };

    loadCategories();
  }, [warehouseUuid]);

  // Load products when category changes
  useEffect(() => {
    if (warehouseUuid) {
      loadProducts(1, '');
    }
  }, [currentCategory, warehouseUuid, loadProducts]);

  return {
    products,
    isLoading,
    pagination,
    currentCategory,
    categories,
    loadProducts,
    loadPage,
    updateCategory,
    // Computed values
    currentPage: pagination.page,
    totalPages: Math.ceil(pagination.total / pagination.limit),
    hasNext: pagination.hasNext,
    hasPrev: pagination.hasPrev,
  };
}
