import React from 'react';
import { Purchase, FilterPurchaseDto } from '../purchaseApi';
import { PurchaseListView } from './PurchaseListView';

interface ListComponentProps {
  purchases: Purchase[];
  totalPages: number;
  currentPage: number;
  isLoading: boolean;
  isLoadingInvoice: boolean;
  isLoadingEditPurchase: boolean;
  isLoadingViewDetails: boolean;
  isLoadingPrintInvoice: boolean;
  isLoadingCancel: boolean;
  showFilters: boolean;
  filter: FilterPurchaseDto;
  onPageChange: (page: number) => void;
  onViewDetails: (purchase: Purchase) => void;
  onEdit: (purchase: Purchase) => void;
  onPrintInvoice: (purchase: Purchase) => void;
  onCancel: (purchase: Purchase) => void;
  onFilterChange: (filter: Partial<FilterPurchaseDto>) => void;
  onClearFilters: () => void;
  onToggleFilters: () => void;
}

export const ListComponent: React.FC<ListComponentProps> = ({
  purchases,
  totalPages,
  currentPage,
  isLoading,
  isLoadingInvoice,
  isLoadingEditPurchase,
  isLoadingViewDetails,
  isLoadingPrintInvoice,
  isLoadingCancel,
  showFilters,
  filter,
  onPageChange,
  onViewDetails,
  onEdit,
  onPrintInvoice,
  onCancel,
  onFilterChange,
  onClearFilters,
  onToggleFilters,
}) => {
  return (
    <PurchaseListView
      purchases={purchases}
      totalPages={totalPages}
      currentPage={currentPage}
      isLoading={isLoading}
      isLoadingInvoice={isLoadingInvoice}
      isLoadingEditPurchase={isLoadingEditPurchase}
      isLoadingViewDetails={isLoadingViewDetails}
      isLoadingPrintInvoice={isLoadingPrintInvoice}
      isLoadingCancel={isLoadingCancel}
      showFilters={showFilters}
      filter={filter}
      onPageChange={onPageChange}
      onViewDetails={onViewDetails}
      onEdit={onEdit}
      onPrintInvoice={onPrintInvoice}
      onCancel={onCancel}
      onFilterChange={onFilterChange}
      onClearFilters={onClearFilters}
      onToggleFilters={onToggleFilters}
    />
  );
};
