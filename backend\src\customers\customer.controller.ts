import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from "@nestjs/common";
import { CustomerService } from "./customer.service.typeorm";
import { CreateCustomerDto } from "./dto/create-customer.dto";
import { UpdateCustomerDto } from "./dto/update-customer.dto";
import { FilterCustomerDto } from "./dto/filter-customer.dto";
import {
  CustomerResponseDto,
  toCustomerResponseDto,
} from "./dto/customer-response.dto";
import {
  PaginationQueryDto,
  PaginatedResponseDto,
} from "../dto/pagination.dto";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from "@nestjs/swagger";

@ApiTags("customers")
@Controller("customers")
export class CustomerController {
  constructor(
    private readonly customerService: CustomerService,
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new customer" })
  @ApiBody({ type: CreateCustomerDto, description: "Customer data to create" })
  @ApiResponse({
    status: 201,
    description: "Customer created successfully",
    type: CustomerResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  create(
    @Body() createCustomerDto: CreateCustomerDto,
  ): Promise<CustomerResponseDto> {
    console.log('[CustomerController] create called with:', JSON.stringify(createCustomerDto, null, 2));
    return this.customerService.create(createCustomerDto);
  }



  @Get()
  @ApiOperation({ summary: "Get all customers with pagination and filtering" })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number (default: 1)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page (default: 10)",
  })
  @ApiQuery({
    name: "name",
    required: false,
    type: String,
    description: "Filter by customer name (partial match)",
  })
  @ApiQuery({
    name: "email",
    required: false,
    type: String,
    description: "Filter by email (partial match)",
  })
  @ApiQuery({
    name: "customerType",
    required: false,
    enum: ["retail", "wholesale", "mid-wholesale", "institutional"],
    description: "Filter by customer type",
  })
  @ApiQuery({
    name: "warehouseUuid",
    required: false,
    type: String,
    description: "Filter by warehouse UUID",
  })
  @ApiQuery({
    name: "regionUuid",
    required: false,
    type: String,
    description: "Filter by region UUID",
  })
  @ApiQuery({
    name: "currentCredit",
    required: false,
    type: Number,
    description: "Filter by exact credit balance",
  })
  @ApiQuery({
    name: "minCredit",
    required: false,
    type: Number,
    description: "Filter by minimum credit balance (inclusive)",
  })
  @ApiQuery({
    name: "maxCredit",
    required: false,
    type: Number,
    description: "Filter by maximum credit balance (inclusive)",
  })
  @ApiQuery({
    name: "sortBy",
    required: false,
    enum: [
      "name",
      "email",
      "customerType",
      "currentCredit",
      "createdAt",
      "updatedAt",
    ],
    description: "Sort by field",
  })
  @ApiQuery({
    name: "sortOrder",
    required: false,
    enum: ["asc", "desc"],
    description: "Sort order (default: desc)",
  })
  async findAll(
    @Query() paginationQuery: PaginationQueryDto,
    @Query() filter: FilterCustomerDto,
  ): Promise<PaginatedResponseDto<CustomerResponseDto>> {
    console.log('[CustomerController] findAll called');
    console.log('[CustomerController] paginationQuery:', JSON.stringify(paginationQuery, null, 2));
    console.log('[CustomerController] filter:', JSON.stringify(filter, null, 2));
    console.log('[CustomerController] Raw query params:', JSON.stringify({ paginationQuery, filter }, null, 2));
    
    try {
      const { page = 1, limit = 10 } = paginationQuery;
      console.log('[CustomerController] Parsed page:', page, 'limit:', limit);
      console.log('[CustomerController] Calling customerService.findAll with filter:', JSON.stringify(filter, null, 2));
      
      const result = await this.customerService.findAll(filter, page, limit);
      console.log('[CustomerController] Service returned result with', result.data.length, 'customers');
      
      const customerDtos = result.data.map(toCustomerResponseDto);
      return new PaginatedResponseDto(
        customerDtos,
        result.total,
        result.page,
        result.limit,
      );
    } catch (error) {
      console.error('[CustomerController] Error in findAll:', error);
      throw error;
    }
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get a customer by UUID" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Customer UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Customer retrieved successfully",
    type: CustomerResponseDto,
  })
  @ApiResponse({ status: 404, description: "Customer not found" })
  findOne(
    @Param("uuid", ParseUUIDPipe) uuid: string,
  ): Promise<CustomerResponseDto> {
    return this.customerService.findOne(uuid);
  }

  @Get(":uuid/sales")
  @ApiOperation({ summary: "Get customer purchase history" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Customer UUID",
  })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number (default: 1)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page (default: 10)",
  })
  @ApiResponse({
    status: 200,
    description: "Customer sales history retrieved successfully",
  })
  @ApiResponse({ status: 404, description: "Customer not found" })
  async getCustomerSales(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ) {
    return this.customerService.getCustomerSales(uuid, page || 1, limit || 10);
  }

  @Post("filter")
  @ApiOperation({ summary: "Filter customers with advanced criteria" })
  @ApiBody({ type: FilterCustomerDto })
  @ApiResponse({
    status: 200,
    description: "Filtered customers retrieved successfully",
    type: PaginatedResponseDto<CustomerResponseDto>,
  })
  @ApiResponse({ status: 400, description: "Invalid filter parameters" })
  async filterCustomers(
    @Body() filter: FilterCustomerDto,
    @Query() paginationQuery: PaginationQueryDto,
  ): Promise<PaginatedResponseDto<CustomerResponseDto>> {
    console.log('[CustomerController] filterCustomers called');
    console.log('[CustomerController] filter:', JSON.stringify(filter, null, 2));
    console.log('[CustomerController] paginationQuery:', JSON.stringify(paginationQuery, null, 2));
    
    try {
      const { page = 1, limit = 10 } = paginationQuery;
      console.log('[CustomerController] Parsed page:', page, 'limit:', limit);
      console.log('[CustomerController] Calling customerService.findAll with filter:', JSON.stringify(filter, null, 2));
      
      const result = await this.customerService.findAll(filter, page, limit);
      console.log('[CustomerController] Service returned result with', result.data.length, 'customers');
      
      const customerDtos = result.data.map(toCustomerResponseDto);
      return new PaginatedResponseDto(
        customerDtos,
        result.total,
        result.page,
        result.limit,
      );
    } catch (error) {
      console.error('[CustomerController] Error in filterCustomers:', error);
      throw error;
    }
  }

  @Patch(":uuid")
  @ApiOperation({
    summary: "Update a customer by UUID",
    description:
      "Update customer information. Name is mandatory. Warehouse UUID cannot be changed and will be ignored if provided.",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Customer UUID",
  })
  @ApiBody({
    type: UpdateCustomerDto,
    description:
      "Customer data to update. Name is required. Warehouse UUID changes are ignored.",
    examples: {
      basic: {
        summary: "Basic customer update",
        value: {
          name: "Updated Customer Name",
          email: "<EMAIL>",
          phone: "+1234567890",
        },
      },
      full: {
        summary: "Full customer update",
        value: {
          name: "Updated Customer Name",
          fiscalId: "12345678901",
          email: "<EMAIL>",
          phone: "+1234567890",
          address: "123 Updated St, City, Country",
          rc: "RC123456",
          articleNumber: "ART001",
          customerType: "retail",
          latitude: 40.7128,
          longitude: -74.006,
          regionUuid: "uuid-v7-string",
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "Customer updated successfully",
    type: CustomerResponseDto,
  })
  @ApiResponse({ status: 404, description: "Customer not found" })
  @ApiResponse({
    status: 400,
    description: "Invalid input data or missing required fields",
  })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateDto: UpdateCustomerDto,
  ): Promise<CustomerResponseDto> {
    console.log("CustomerController: Update request for UUID:", uuid);
    console.log("CustomerController: Update DTO:", updateDto);
    try {
      return await this.customerService.update(uuid, updateDto);
    } catch (error) {
      console.error("CustomerController: Update error:", error);
      throw error;
    }
  }

  @Delete(":uuid")
  @ApiOperation({
    summary: "Soft delete a customer by UUID (sets isDeleted=true)",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Customer UUID",
  })
  @ApiResponse({ status: 200, description: "Customer deleted successfully" })
  @ApiResponse({ status: 404, description: "Customer not found" })
  remove(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<void> {
    return this.customerService.remove(uuid);
  }
}
