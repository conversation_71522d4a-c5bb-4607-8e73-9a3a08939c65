import React from 'react';
import { Eye, Edit, Trash2 } from 'lucide-react';
import { Supplier } from '../suppliersApi';

interface SupplierTableActionsProps {
  supplier: Supplier;
  onViewDetails: (supplier: Supplier) => void;
  onEdit: (supplier: Supplier) => void;
  onDelete: (supplier: Supplier) => void;
  isUpdating: boolean;
  isDeleting: boolean;
}

export const SupplierTableActions: React.FC<SupplierTableActionsProps> = ({
  supplier,
  onViewDetails,
  onEdit,
  onDelete,
  isUpdating,
  isDeleting,
}) => {
  return (
    <div className="flex items-center justify-center space-x-2">
      <button
        onClick={() => onViewDetails(supplier)}
        className="p-1.5 text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200"
        title="View Details"
        disabled={isUpdating || isDeleting}
      >
        <Eye className="h-4 w-4" />
      </button>
      
      <button
        onClick={() => onEdit(supplier)}
        className="p-1.5 text-green-600 hover:bg-green-50 rounded-md transition-colors duration-200"
        title="Edit Supplier"
        disabled={isUpdating || isDeleting}
      >
        <Edit className="h-4 w-4" />
      </button>
      
      <button
        onClick={() => onDelete(supplier)}
        className="p-1.5 text-red-600 hover:bg-red-50 rounded-md transition-colors duration-200"
        title="Delete Supplier"
        disabled={isUpdating || isDeleting}
      >
        <Trash2 className="h-4 w-4" />
      </button>
    </div>
  );
}; 