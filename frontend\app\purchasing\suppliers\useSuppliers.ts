// useSuppliers.ts - React Query hooks for suppliers endpoints
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getSuppliers,
  getSupplierByUuid,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  Supplier,
  CreateSupplierDto,
  UpdateSupplierDto,
  FilterSupplierDto,
  PaginationQueryDto,
  PaginatedResponseDto
} from './suppliersApi';

// Hook for getting paginated suppliers with filtering and sorting
export function useSuppliers(
  paginationQuery?: PaginationQueryDto,
  filter?: FilterSupplierDto
) {
  return useQuery({
    queryKey: ['suppliers', paginationQuery, filter],
    queryFn: () => getSuppliers(paginationQuery, filter),
  });
}

// Hook for getting a single supplier
export function useSupplier(uuid?: string) {
  return useQuery({
    queryKey: ['suppliers', uuid],
    queryFn: () => getSupplierByUuid(uuid!),
    enabled: !!uuid,
  });
}

// Mutation hooks
export function useCreateSupplier() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: createSupplier,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suppliers'] });
    },
  });
}

export function useUpdateSupplier() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: UpdateSupplierDto }) =>
      updateSupplier(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: ['suppliers'] });
      queryClient.invalidateQueries({ queryKey: ['suppliers', uuid] });
    },
  });
}

export function useDeleteSupplier() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: deleteSupplier,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suppliers'] });
    },
  });
} 