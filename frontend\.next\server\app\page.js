/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21haGxsJTVDJTVDRG9jdW1lbnRzJTVDJTVDd29ya3NwYWNlJTVDJTVDcHJvamVjdHMlNUMlNUNkaWRvLWRpc3RyaWJ1dGlvbiU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUFpSSIsInNvdXJjZXMiOlsid2VicGFjazovL2RpZG8tZGlzdHJpYnV0aW9uLWZyb250ZW5kLz9iZjA1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWFobGxcXFxcRG9jdW1lbnRzXFxcXHdvcmtzcGFjZVxcXFxwcm9qZWN0c1xcXFxkaWRvLWRpc3RyaWJ1dGlvblxcXFxmcm9udGVuZFxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_SideTaskBar_SideTaskBar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/SideTaskBar/SideTaskBar */ \"(ssr)/./components/SideTaskBar/SideTaskBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SideTaskBar_SideTaskBar__WEBPACK_IMPORTED_MODULE_1__.SideTaskBar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-8 overflow-auto ml-20 transition-all duration-200 \",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-6\",\n                            children: \"Welcome to abcd\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-8\",\n                            children: \"Your distribution management system\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-gray-800 mb-4\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Select an option from the sidebar to get started.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: (failureCount, error)=>{\n                // Don't retry on 401 errors\n                if (error?.response?.status === 401) {\n                    return false;\n                }\n                // Retry up to 2 times for other errors\n                return failureCount < 2;\n            },\n            // Global error handler for queries\n            onError: (error)=>{\n                if (error?.response?.status === 401) {\n                    console.log(\"Query failed with 401, clearing tokens\");\n                    // Clear tokens on 401 errors\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    // Dispatch event to notify components\n                    if (false) {}\n                }\n            }\n        },\n        mutations: {\n            retry: (failureCount, error)=>{\n                // Don't retry on 401 errors\n                if (error?.response?.status === 401) {\n                    return false;\n                }\n                // Retry up to 1 time for other errors\n                return failureCount < 1;\n            },\n            // Global error handler for mutations\n            onError: (error)=>{\n                if (error?.response?.status === 401) {\n                    console.log(\"Mutation failed with 401, clearing tokens\");\n                    // Clear tokens on 401 errors\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    // Dispatch event to notify components\n                    if (false) {}\n                }\n            }\n        }\n    }\n});\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\providers.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\providers.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\providers.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/SideTaskBar/SideTaskBar.tsx":
/*!************************************************!*\
  !*** ./components/SideTaskBar/SideTaskBar.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SideTaskBar: () => (/* binding */ SideTaskBar),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SideTaskBar.module.css */ \"(ssr)/./components/SideTaskBar/SideTaskBar.module.css\");\n/* harmony import */ var _SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertCircle,FiChevronRight,FiDollarSign,FiFileText,FiGrid,FiHome,FiLogOut,FiMap,FiPackage,FiPieChart,FiSettings,FiShoppingCart,FiTruck,FiUser,FiUsers!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ SideTaskBar,default auto */ \n\n\n\n\n\n// Helper function to find active primary and sub-items based on current path\nconst findActivePaths = (pathname, items)=>{\n    let activePrimary = null;\n    let activeSub = null;\n    // Function to recursively search for a path\n    const searchItems = (currentItems, parentPrimaryId)=>{\n        for (const item of currentItems){\n            if (item.path && pathname.startsWith(item.path)) {\n                activePrimary = parentPrimaryId || item.id; // If it's a top-level item, its own ID, otherwise its parent\n                activeSub = item.id; // Mark the specific item as active in the second bar\n                return true;\n            }\n            if (item.subItems && searchItems(item.subItems, parentPrimaryId || item.id)) {\n                activePrimary = parentPrimaryId || item.id; // Ensure primary is set\n                return true;\n            }\n        }\n        return false;\n    };\n    searchItems(items, null); // Start searching from top-level items\n    // If a sub-item is active, the primary item should be its top-level parent\n    if (activeSub) {\n        for (const item of items){\n            if (item.id === activeSub) {\n                activePrimary = item.id;\n                break;\n            }\n            if (item.subItems) {\n                const foundParent = item.subItems.find((sub)=>sub.id === activeSub || sub.subItems && sub.subItems.some((s)=>s.id === activeSub));\n                if (foundParent) {\n                    activePrimary = item.id;\n                    break;\n                }\n            }\n        }\n    }\n    // Refine for initial load: if a sub-sub-item is active, ensure its direct sub-item parent is also 'activeSub'\n    // Example: /inventory/stock/transfers active, 'Stock Levels' should be activeSub\n    if (activePrimary) {\n        const currentPrimaryItem = items.find((item)=>item.id === activePrimary);\n        if (currentPrimaryItem?.subItems) {\n            for (const sub of currentPrimaryItem.subItems){\n                if (sub.path === pathname || sub.subItems && sub.subItems.some((ss)=>ss.path === pathname)) {\n                    activeSub = sub.id;\n                    break;\n                }\n            }\n        }\n    }\n    return {\n        activePrimary,\n        activeSub\n    };\n};\nconst defaultItems = [\n    {\n        id: \"dashboard\",\n        label: \"Dashboard\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiHome, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n            lineNumber: 109,\n            columnNumber: 11\n        }, undefined),\n        path: \"/dashboard\"\n    },\n    {\n        id: \"sales\",\n        label: \"Sales\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiDollarSign, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n            lineNumber: 116,\n            columnNumber: 11\n        }, undefined),\n        path: \"/sales/sales\",\n        subItems: [\n            {\n                id: \"sales-pos\",\n                label: \"Sales\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiShoppingCart, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 48\n                }, undefined),\n                path: \"/sales/sales\"\n            },\n            {\n                id: \"customers\",\n                label: \"Customers\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 52\n                }, undefined),\n                path: \"/sales/customers\"\n            },\n            {\n                id: \"customer-payments\",\n                label: \"Customer Payments\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiDollarSign, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 68\n                }, undefined),\n                path: \"/sales/customers/payments\"\n            },\n            {\n                id: \"customer-locations\",\n                label: \"Customer Locations\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 70\n                }, undefined),\n                path: \"/sales/customers/locations\"\n            }\n        ]\n    },\n    {\n        id: \"purchasing\",\n        label: \"Purchasing\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiShoppingCart, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n            lineNumber: 128,\n            columnNumber: 11\n        }, undefined),\n        subItems: [\n            {\n                id: \"purchase\",\n                label: \"Purchase\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiShoppingCart, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 50\n                }, undefined),\n                path: \"/purchase\"\n            },\n            {\n                id: \"suppliers\",\n                label: \"Suppliers\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 52\n                }, undefined),\n                path: \"/purchasing/suppliers\"\n            },\n            {\n                id: \"purchase-orders\",\n                label: \"Purchase Orders\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiFileText, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 64\n                }, undefined),\n                path: \"/purchasing/orders\"\n            },\n            {\n                id: \"goods-receipt\",\n                label: \"Goods Receipt\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPackage, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 60\n                }, undefined),\n                path: \"/purchasing/goods-receipt\"\n            },\n            {\n                id: \"returns\",\n                label: \"Returns\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiShoppingCart, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 48\n                }, undefined),\n                path: \"/purchasing/returns\"\n            }\n        ]\n    },\n    {\n        id: \"inventory\",\n        label: \"Inventory\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPackage, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n            lineNumber: 140,\n            columnNumber: 11\n        }, undefined),\n        subItems: [\n            {\n                id: \"products\",\n                label: \"Products\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPackage, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 50\n                }, undefined),\n                path: \"/inventory/products\"\n            },\n            {\n                id: \"categories\",\n                label: \"Categories\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPackage, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 54\n                }, undefined),\n                path: \"/inventory/products/categories\"\n            },\n            {\n                id: \"stock-levels\",\n                label: \"Stock Levels\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPackage, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 58\n                }, undefined),\n                path: \"/inventory/stock-levels\"\n            },\n            {\n                id: \"stock-transfers\",\n                label: \"Stock Transfers\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTruck, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 64\n                }, undefined),\n                path: \"/inventory/stock/transfers\"\n            },\n            {\n                id: \"stock-adjustments\",\n                label: \"Stock Adjustments\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 68\n                }, undefined),\n                path: \"/inventory/stock/adjustments\"\n            },\n            {\n                id: \"stock-movements\",\n                label: \"Stock Movements\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTruck, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 64\n                }, undefined),\n                path: \"/inventory/stock/movements\"\n            },\n            {\n                id: \"low-stock-alerts\",\n                label: \"Low Stock Alerts\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiAlertCircle, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 66\n                }, undefined),\n                path: \"/inventory/stock/alerts\"\n            }\n        ]\n    },\n    {\n        id: \"logistics\",\n        label: \"Logistics\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTruck, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n            lineNumber: 154,\n            columnNumber: 11\n        }, undefined),\n        subItems: [\n            {\n                id: \"warehouses\",\n                label: \"Warehouses\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiGrid, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 54\n                }, undefined),\n                path: \"/logistics/warehouses\"\n            },\n            {\n                id: \"regions\",\n                label: \"Regions\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiMap, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 48\n                }, undefined),\n                path: \"/logistics/regions\"\n            },\n            {\n                id: \"vans\",\n                label: \"Vans\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTruck, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 42\n                }, undefined),\n                path: \"/logistics/vans\"\n            },\n            {\n                id: \"van-stock\",\n                label: \"Van Stock\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPackage, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 52\n                }, undefined),\n                path: \"/logistics/van-stock\"\n            },\n            {\n                id: \"van-loading\",\n                label: \"Van Loading\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTruck, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 56\n                }, undefined),\n                path: \"/logistics/van-loading\"\n            },\n            {\n                id: \"routes\",\n                label: \"Routes\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTruck, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 46\n                }, undefined),\n                path: \"/logistics/routes\"\n            }\n        ]\n    },\n    {\n        id: \"reports\",\n        label: \"Reports\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPieChart, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n            lineNumber: 167,\n            columnNumber: 11\n        }, undefined),\n        subItems: [\n            {\n                id: \"sales-reports\",\n                label: \"Sales\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiDollarSign, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 52\n                }, undefined),\n                path: \"/reports/sales\"\n            },\n            {\n                id: \"inventory-reports\",\n                label: \"Inventory\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPackage, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 60\n                }, undefined),\n                path: \"/reports/inventory\"\n            },\n            {\n                id: \"van-performance\",\n                label: \"Van Performance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTruck, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 64\n                }, undefined),\n                path: \"/reports/van-performance\"\n            },\n            {\n                id: \"financial-reports\",\n                label: \"Financial\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiDollarSign, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 60\n                }, undefined),\n                path: \"/reports/financial\"\n            }\n        ]\n    },\n    {\n        id: \"settings\",\n        label: \"Settings\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSettings, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n            lineNumber: 178,\n            columnNumber: 11\n        }, undefined),\n        subItems: [\n            {\n                id: \"profile\",\n                label: \"Profile\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUser, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 48\n                }, undefined),\n                path: \"/settings/profile\"\n            },\n            {\n                id: \"users\",\n                label: \"Users\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUser, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 44\n                }, undefined),\n                path: \"/settings/users\"\n            },\n            {\n                id: \"roles\",\n                label: \"Roles\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 44\n                }, undefined),\n                path: \"/settings/roles\"\n            },\n            {\n                id: \"system\",\n                label: \"System\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 46\n                }, undefined),\n                path: \"/settings/system\"\n            },\n            {\n                id: \"data\",\n                label: \"Data\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSettings, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 42\n                }, undefined),\n                path: \"/settings/data\"\n            },\n            {\n                id: \"logs\",\n                label: \"Logs\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiFileText, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 42\n                }, undefined),\n                path: \"/logs\"\n            }\n        ]\n    }\n];\nconst SideTaskBar = ({ items = defaultItems, className = \"\" })=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)(); // Get current path\n    const [activePrimaryItem, setActivePrimaryItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeSubItem, setActiveSubItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user, checkUserExists } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    // Effect to set active items based on current URL on mount or path change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const { activePrimary, activeSub } = findActivePaths(pathname, items);\n        setActivePrimaryItem(activePrimary);\n        setActiveSubItem(activeSub);\n    }, [\n        pathname,\n        items\n    ]);\n    // Helper function to validate user existence before navigation\n    const validateUserAndNavigate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (navigationAction)=>{\n        // Check if user exists locally\n        if (!user?.uuid) {\n            router.push(\"/auth\");\n            return;\n        }\n        try {\n            // Check if user exists in backend\n            const userExists = await checkUserExists(user.uuid);\n            if (!userExists) {\n                // User not found in backend, redirect to auth\n                router.push(\"/auth\");\n                return;\n            }\n            // User exists, proceed with navigation\n            navigationAction();\n        } catch (error) {\n            console.error(\"Error checking user existence:\", error);\n            // On error, redirect to auth for safety\n            router.push(\"/auth\");\n        }\n    }, [\n        user,\n        checkUserExists,\n        router\n    ]);\n    const handlePrimaryItemClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((item)=>{\n        validateUserAndNavigate(()=>{\n            if (item.subItems && item.subItems.length > 0) {\n                // If item has sub-items, navigate to the main path AND show sub-items\n                if (item.path) {\n                    router.push(item.path);\n                }\n                setActivePrimaryItem(activePrimaryItem === item.id ? null : item.id);\n                setActiveSubItem(null); // Reset sub-item when primary is toggled\n            } else if (item.path) {\n                // If primary item has no sub-items but has a path, navigate directly\n                router.push(item.path);\n                setActivePrimaryItem(item.id);\n                setActiveSubItem(null);\n            }\n        });\n    }, [\n        activePrimaryItem,\n        router,\n        validateUserAndNavigate\n    ]);\n    const handleSubItemClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((subItem)=>{\n        validateUserAndNavigate(()=>{\n            if (subItem.subItems && subItem.subItems.length > 0) {\n                setActiveSubItem(activeSubItem === subItem.id ? null : subItem.id);\n            } else if (subItem.onClick) {\n                subItem.onClick();\n                setActiveSubItem(subItem.id);\n            } else if (subItem.path) {\n                router.push(subItem.path);\n                setActiveSubItem(subItem.id);\n            }\n        });\n    }, [\n        activeSubItem,\n        router,\n        validateUserAndNavigate\n    ]);\n    // Find the currently active primary item to display its sub-items\n    const currentActivePrimaryItem = activePrimaryItem ? items.find((item)=>item.id === activePrimaryItem) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${(_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().taskBar)} ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryNavContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().logo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().logoIcon),\n                                children: \"D\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().logoText),\n                                children: \"Dido\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryNav),\n                        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().navItem),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `${(_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().navButton)} ${activePrimaryItem === item.id ? (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : \"\"}`,\n                                    onClick: ()=>handlePrimaryItemClick(item),\n                                    \"aria-expanded\": activePrimaryItem === item.id,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().icon),\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().label),\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" \",\n                                        item.subItems && item.subItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiChevronRight, {\n                                            className: `${(_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().chevron)} ${activePrimaryItem === item.id ? (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().rotated) : \"\"}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, item.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SideTaskBarFooter, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, undefined),\n            currentActivePrimaryItem && currentActivePrimaryItem.subItems && currentActivePrimaryItem.subItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryNavContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryHeader),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().activeItemName),\n                                children: currentActivePrimaryItem.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerDivider)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryNav),\n                        children: currentActivePrimaryItem.subItems.map((subItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().subNavItem),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: `${(_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().subNavLink)} ${activeSubItem === subItem.id ? (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : \"\"}`,\n                                        onClick: ()=>handleSubItemClick(subItem),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().subIcon),\n                                                children: subItem.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: subItem.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            subItem.subItems && subItem.subItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiChevronRight, {\n                                                className: `${(_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().chevron)} ${activeSubItem === subItem.id ? (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().rotated) : \"\"}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    activeSubItem === subItem.id && subItem.subItems && subItem.subItems.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().nestedSubMenu),\n                                        children: subItem.subItems.map((subSubItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().subSubMenuItem),\n                                                onClick: ()=>handleSubItemClick(subSubItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().subSubIcon),\n                                                        children: subSubItem.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: subSubItem.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, subSubItem.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 23\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, subItem.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                lineNumber: 298,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n        lineNumber: 266,\n        columnNumber: 5\n    }, undefined);\n};\n// Footer component for user info and logout\nconst SideTaskBarFooter = ()=>{\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    // Helper function to get display name\n    const getDisplayName = ()=>{\n        if (!user) return \"Not logged in\";\n        // Try to construct name from firstName and lastName\n        if (user.firstName || user.lastName) {\n            const firstName = user.firstName || \"\";\n            const lastName = user.lastName || \"\";\n            return `${firstName} ${lastName}`.trim();\n        }\n        // Fallback to legacy name field\n        if (user.name) {\n            return user.name;\n        }\n        // Fallback to email\n        if (user.email) {\n            return user.email;\n        }\n        return \"User\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().footer),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().profile),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().avatar),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUser, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 40\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().profileInfo),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().userName),\n                            children: getDisplayName()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_SideTaskBar_module_css__WEBPACK_IMPORTED_MODULE_3___default().logoutButton),\n                            onClick: logout,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertCircle_FiChevronRight_FiDollarSign_FiFileText_FiGrid_FiHome_FiLogOut_FiMap_FiPackage_FiPieChart_FiSettings_FiShoppingCart_FiTruck_FiUser_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLogOut, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Logout\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n            lineNumber: 371,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\SideTaskBar\\\\SideTaskBar.tsx\",\n        lineNumber: 370,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SideTaskBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/SideTaskBar/SideTaskBar.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshToken, setRefreshToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [fetchingWarehouse, setFetchingWarehouse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshingToken, setRefreshingToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Use refs to avoid dependency issues\n    const fetchingWarehouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const tokenRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const userRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Update refs when state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchingWarehouseRef.current = fetchingWarehouse;\n    }, [\n        fetchingWarehouse\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        tokenRef.current = token;\n    }, [\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        userRef.current = user;\n    }, [\n        user\n    ]);\n    // Helper to fetch warehouse info in background\n    const fetchAndPersistWarehouseInfo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (userObjParam, tokenParam)=>{\n        const tokenToUse = tokenParam !== undefined ? tokenParam : tokenRef.current;\n        const userToFetch = userObjParam || userRef.current;\n        console.log(\"fetchAndPersistWarehouseInfo called with:\", {\n            tokenToUse: !!tokenToUse,\n            userToFetch: userToFetch?.uuid,\n            userToFetchWarehouseUuid: userToFetch?.warehouseUuidString || userToFetch?.warehouseUuid,\n            fetchingWarehouse: fetchingWarehouseRef.current,\n            currentUser: userRef.current?.uuid,\n            currentUserWarehouseUuid: userRef.current?.warehouseUuidString || userRef.current?.warehouseUuid\n        });\n        if (!tokenToUse || !userToFetch?.uuid || fetchingWarehouseRef.current) {\n            console.log(\"fetchAndPersistWarehouseInfo early return:\", {\n                noToken: !tokenToUse,\n                noUserUuid: !userToFetch?.uuid,\n                alreadyFetching: fetchingWarehouseRef.current\n            });\n            return;\n        }\n        // Additional safety check: ensure UUID is a valid string\n        if (typeof userToFetch.uuid !== \"string\" || userToFetch.uuid.trim() === \"\") {\n            console.log(\"fetchAndPersistWarehouseInfo early return: invalid UUID:\", userToFetch.uuid);\n            return;\n        }\n        // Additional check: if we don't have a valid token in localStorage, don't proceed\n        const storedToken = localStorage.getItem(\"dido_token\");\n        if (!storedToken) {\n            console.log(\"fetchAndPersistWarehouseInfo early return: no stored token\");\n            return;\n        }\n        setFetchingWarehouse(true);\n        try {\n            console.log(\"Fetching latest user info for UUID:\", userToFetch.uuid);\n            // Always fetch the latest user info from backend to ensure we have current data\n            const userRes = await fetch(`/api/users/${userToFetch.uuid}`, {\n                headers: {\n                    Authorization: `Bearer ${tokenToUse}`\n                }\n            });\n            if (!userRes.ok) {\n                console.error(\"Failed to fetch user info:\", userRes.status, userRes.statusText);\n                return;\n            }\n            const latestUser = await userRes.json();\n            console.log(\"Latest user info from API:\", latestUser);\n            console.log(\"Warehouse UUID from backend:\", {\n                warehouseUuidString: latestUser.warehouseUuidString,\n                warehouseUuid: latestUser.warehouseUuid,\n                finalWarehouseUuid: latestUser.warehouseUuidString || latestUser.warehouseUuid\n            });\n            // Check if user has a warehouse assigned\n            const warehouseUuid = latestUser.warehouseUuidString || latestUser.warehouseUuid;\n            if (!warehouseUuid) {\n                console.log(\"No warehouse assigned to user, setting user without warehouse info\");\n                const updatedUser = {\n                    ...latestUser,\n                    warehouseUuid: null,\n                    warehouseName: null\n                };\n                setUser(updatedUser);\n                localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                return;\n            }\n            console.log(\"Fetching warehouse info for UUID:\", warehouseUuid);\n            const warehouseRes = await fetch(`/api/warehouses/${warehouseUuid}`, {\n                headers: {\n                    Authorization: `Bearer ${tokenToUse}`\n                }\n            });\n            if (!warehouseRes.ok) {\n                console.error(\"Failed to fetch warehouse info:\", warehouseRes.status, warehouseRes.statusText);\n                // If warehouse fetch fails, still update user with current data but without warehouse name\n                const updatedUser = {\n                    ...latestUser,\n                    warehouseUuid: warehouseUuid,\n                    warehouseName: null\n                };\n                setUser(updatedUser);\n                localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                return;\n            }\n            const warehouse = await warehouseRes.json();\n            console.log(\"Warehouse info from API:\", warehouse);\n            const updatedUser = {\n                ...latestUser,\n                warehouseUuid: warehouseUuid,\n                warehouseName: warehouse.name\n            };\n            console.log(\"Setting updated user with warehouse info:\", updatedUser);\n            setUser(updatedUser);\n            localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n        } catch (err) {\n            console.error(\"Error fetching warehouse info:\", err);\n            // On error, still try to update user with current data from backend\n            try {\n                const userRes = await fetch(`/api/users/${userToFetch.uuid}`, {\n                    headers: {\n                        Authorization: `Bearer ${tokenToUse}`\n                    }\n                });\n                if (userRes.ok) {\n                    const latestUser = await userRes.json();\n                    const updatedUser = {\n                        ...latestUser,\n                        warehouseUuid: latestUser.warehouseUuidString || latestUser.warehouseUuid,\n                        warehouseName: null\n                    };\n                    setUser(updatedUser);\n                    localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                }\n            } catch (fallbackErr) {\n                console.error(\"Error in fallback user update:\", fallbackErr);\n            }\n        } finally{\n            setFetchingWarehouse(false);\n        }\n    }, []); // Remove all dependencies since we're using refs\n    // Refresh access token using refresh token\n    const refreshAccessToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!refreshToken || refreshingToken) return false;\n        setRefreshingToken(true);\n        try {\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refreshToken,\n                    clientIp: \"frontend\",\n                    userAgent: navigator.userAgent\n                })\n            });\n            if (!response.ok) {\n                console.error(\"Token refresh failed:\", response.status);\n                return false;\n            }\n            const data = await response.json();\n            setToken(data.accessToken);\n            setRefreshToken(data.refreshToken);\n            localStorage.setItem(\"dido_token\", data.accessToken);\n            localStorage.setItem(\"dido_refresh_token\", data.refreshToken);\n            console.log(\"Token refreshed successfully\");\n            return true;\n        } catch (error) {\n            console.error(\"Error refreshing token:\", error);\n            return false;\n        } finally{\n            setRefreshingToken(false);\n        }\n    }, [\n        refreshToken,\n        refreshingToken\n    ]);\n    // On initial mount: restore session from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"AuthContext initial mount - checking localStorage\");\n        const storedUser = localStorage.getItem(\"dido_user\");\n        const storedToken = localStorage.getItem(\"dido_token\");\n        const storedRefreshToken = localStorage.getItem(\"dido_refresh_token\");\n        console.log(\"Stored data from localStorage:\", {\n            hasStoredUser: !!storedUser,\n            hasStoredToken: !!storedToken,\n            hasStoredRefreshToken: !!storedRefreshToken\n        });\n        if (storedUser && storedToken) {\n            const userObj = JSON.parse(storedUser);\n            console.log(\"Restored user from localStorage:\", {\n                userUuid: userObj.uuid,\n                userEmail: userObj.email,\n                userWarehouseUuid: userObj.warehouseUuidString || userObj.warehouseUuid,\n                userWarehouseName: userObj.warehouseName\n            });\n            // Validate that the user UUID is still valid by fetching from backend\n            const validateUserExists = async ()=>{\n                try {\n                    console.log(\"Validating user UUID from localStorage:\", userObj.uuid);\n                    const response = await fetch(`/api/users/${userObj.uuid}`, {\n                        headers: {\n                            Authorization: `Bearer ${storedToken}`\n                        }\n                    });\n                    if (response.status === 404) {\n                        console.log(\"User UUID not found in backend (404), clearing localStorage and redirecting to login\");\n                        // User was deleted/recreated, clear localStorage and redirect to login\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                        return;\n                    }\n                    if (!response.ok) {\n                        console.log(\"Failed to validate user UUID:\", response.status, response.statusText);\n                        // If we can't validate the user, clear localStorage and redirect to login\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                        return;\n                    }\n                    // User exists, proceed with normal flow\n                    console.log(\"User UUID validated successfully\");\n                    setUser(userObj);\n                    setToken(storedToken);\n                    if (storedRefreshToken) {\n                        setRefreshToken(storedRefreshToken);\n                    }\n                    // Always trigger background fetch to ensure we have the latest warehouse info\n                    // This is especially important after database clearing when warehouse UUIDs may have changed\n                    console.log(\"Triggering background warehouse info fetch to ensure latest data from localStorage\");\n                    setTimeout(()=>{\n                        fetchAndPersistWarehouseInfo(userObj, storedToken);\n                    }, 0);\n                } catch (error) {\n                    console.error(\"Error validating user UUID:\", error);\n                    // If there's a network error, clear localStorage and redirect to login\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    router.replace(\"/auth\");\n                }\n            };\n            // Validate user exists before setting state\n            validateUserExists();\n        } else {\n            console.log(\"No stored user/token found in localStorage\");\n        }\n        // If we have no valid authentication and we're not on the auth page, redirect\n        if (!storedToken && !window.location.pathname.includes(\"/auth\")) {\n            console.log(\"No valid authentication found, redirecting to login\");\n            router.replace(\"/auth\");\n        }\n        setLoading(false);\n    }, []); // Remove fetchAndPersistWarehouseInfo from dependencies to prevent infinite loop\n    // Periodically validate session and refresh token if needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!token || !refreshToken) {\n            console.log(\"No token or refresh token available, skipping periodic validation\");\n            return;\n        }\n        const interval = setInterval(async ()=>{\n            try {\n                console.log(\"Periodic token validation - checking token validity\");\n                // Try to validate current token\n                const res = await fetch(\"/api/auth/validate\", {\n                    headers: {\n                        Authorization: `Bearer ${token}`\n                    }\n                });\n                if (!res.ok) {\n                    console.log(\"Token validation failed, attempting refresh\");\n                    // Token is invalid, try to refresh\n                    const refreshSuccess = await refreshAccessToken();\n                    if (!refreshSuccess) {\n                        console.log(\"Token refresh failed, logging out user\");\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                    } else {\n                        console.log(\"Token refresh successful\");\n                    }\n                } else {\n                    console.log(\"Token validation successful\");\n                }\n            } catch (error) {\n                console.log(\"Network error during token validation, attempting refresh\");\n                // Network error, try to refresh token\n                const refreshSuccess = await refreshAccessToken();\n                if (!refreshSuccess) {\n                    console.log(\"Token refresh failed after network error, logging out user\");\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    router.replace(\"/auth\");\n                } else {\n                    console.log(\"Token refresh successful after network error\");\n                }\n            }\n        }, 30 * 60 * 1000); // 30 minutes\n        return ()=>clearInterval(interval);\n    }, [\n        token,\n        refreshToken,\n        refreshAccessToken,\n        router\n    ]);\n    const login = (user, token, refreshTokenValue)=>{\n        console.log(\"AuthContext login called with:\", {\n            userUuid: user.uuid,\n            userEmail: user.email,\n            userWarehouseUuid: user.warehouseUuidString || user.warehouseUuid,\n            userWarehouseUuidString: user.warehouseUuidString,\n            userWarehouseUuidLegacy: user.warehouseUuid,\n            userWarehouseName: user.warehouseName,\n            hasToken: !!token,\n            hasRefreshToken: !!refreshTokenValue\n        });\n        setUser(user);\n        setToken(token);\n        setRefreshToken(refreshTokenValue);\n        localStorage.setItem(\"dido_user\", JSON.stringify(user));\n        localStorage.setItem(\"dido_token\", token);\n        localStorage.setItem(\"dido_refresh_token\", refreshTokenValue);\n        // Always trigger background fetch to ensure we have the latest warehouse info\n        // This is especially important after database clearing when warehouse UUIDs may have changed\n        console.log(\"Triggering background warehouse info fetch to ensure latest data\");\n        setTimeout(()=>{\n            fetchAndPersistWarehouseInfo(user, token);\n        }, 0);\n    };\n    const logout = async ()=>{\n        console.log(\"AuthContext logout called - clearing session and redirecting to /auth\");\n        // Revoke tokens on backend if we have them\n        if (token && refreshToken) {\n            try {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${token}`\n                    },\n                    body: JSON.stringify({\n                        refreshToken,\n                        revokeAll: false\n                    })\n                });\n            } catch (error) {\n                console.error(\"Error during logout:\", error);\n            // Continue with logout even if backend call fails\n            }\n        }\n        setUser(null);\n        setToken(null);\n        setRefreshToken(null);\n        localStorage.removeItem(\"dido_token\");\n        localStorage.removeItem(\"dido_refresh_token\");\n        localStorage.removeItem(\"dido_user\");\n        console.log(\"Session cleared, redirecting to /auth\");\n        router.replace(\"/auth\");\n    };\n    const checkUserExists = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (uuid)=>{\n        if (!token) return false;\n        // Safety check: ensure UUID is valid\n        if (!uuid || typeof uuid !== \"string\" || uuid.trim() === \"\") {\n            console.log(\"checkUserExists: invalid UUID provided:\", uuid);\n            return false;\n        }\n        try {\n            const response = await fetch(`/api/users/${uuid}`, {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            if (response.status === 404) return false;\n            if (!response.ok) throw new Error(\"Failed to check user existence\");\n            return true;\n        } catch (error) {\n            console.error(\"Error checking user existence:\", error);\n            return false;\n        }\n    }, [\n        token\n    ]);\n    const switchWarehouse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (warehouseUuid, warehouseName)=>{\n        if (!user?.uuid || !token) {\n            throw new Error(\"User not authenticated\");\n        }\n        // Safety check: ensure user UUID is valid\n        if (typeof user.uuid !== \"string\" || user.uuid.trim() === \"\") {\n            throw new Error(\"Invalid user UUID\");\n        }\n        try {\n            // Update user's warehouse on backend\n            const response = await fetch(`/api/users/${user.uuid}/warehouse`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: `Bearer ${token}`\n                },\n                body: JSON.stringify({\n                    warehouseUuid\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update warehouse on backend\");\n            }\n            // Update user context with new warehouse info\n            const updatedUser = {\n                ...user,\n                warehouseUuid: warehouseUuid,\n                warehouseName: warehouseName\n            };\n            // Update localStorage\n            localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n            // Update context state\n            setUser(updatedUser);\n            console.log(\"Warehouse switched successfully:\", {\n                warehouseUuid,\n                warehouseName\n            });\n        } catch (error) {\n            console.error(\"Error switching warehouse:\", error);\n            throw error;\n        }\n    }, [\n        user,\n        token\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            token,\n            refreshToken,\n            loading,\n            login,\n            logout,\n            refreshAccessToken,\n            fetchAndPersistWarehouseInfo,\n            checkUserExists,\n            switchWarehouse\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 520,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/themes */ \"(ssr)/./styles/themes.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ThemeProvider = ({ children, initialTheme = \"white\" })=>{\n    const [themeName, setThemeName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTheme);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_styles_themes__WEBPACK_IMPORTED_MODULE_2__.getThemeByName)(initialTheme));\n    // Load saved theme from localStorage on initial render (client-side only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, []);\n    // Update CSS variables when theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run on client side\n        if (true) return;\n        const root = document.documentElement;\n        // Set color variables\n        Object.entries(theme.colors).forEach(([key, value])=>{\n            root.style.setProperty(`--color-${key}`, value);\n        });\n        // Set shadow variables\n        Object.entries(theme.shadows).forEach(([key, value])=>{\n            root.style.setProperty(`--shadow-${key}`, value);\n        });\n        // Set border radius variables\n        Object.entries(theme.borderRadius).forEach(([key, value])=>{\n            root.style.setProperty(`--radius-${key}`, value);\n        });\n        // Update the data-theme attribute for potential CSS selectors\n        root.setAttribute(\"data-theme\", theme.name);\n    }, [\n        theme\n    ]);\n    const changeTheme = (newThemeName)=>{\n        const newTheme = (0,_styles_themes__WEBPACK_IMPORTED_MODULE_2__.getThemeByName)(newThemeName);\n        setThemeName(newTheme.name);\n        setTheme(newTheme);\n        // Save to localStorage\n        localStorage.setItem(\"theme\", newTheme.name);\n    };\n    const availableThemes = _styles_themes__WEBPACK_IMPORTED_MODULE_2__.allThemes.map((t)=>({\n            name: t.name,\n            label: t.name.charAt(0).toUpperCase() + t.name.slice(1)\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            themeName,\n            setTheme: changeTheme,\n            availableThemes\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./styles/themes.ts":
/*!**************************!*\
  !*** ./styles/themes.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allThemes: () => (/* binding */ allThemes),\n/* harmony export */   blueTheme: () => (/* binding */ blueTheme),\n/* harmony export */   darkTheme: () => (/* binding */ darkTheme),\n/* harmony export */   defaultTheme: () => (/* binding */ defaultTheme),\n/* harmony export */   getThemeByName: () => (/* binding */ getThemeByName),\n/* harmony export */   lightTheme: () => (/* binding */ lightTheme)\n/* harmony export */ });\nconst lightTheme = {\n    name: \"light\",\n    colors: {\n        primary: \"#2563eb\",\n        primaryLight: \"#3b82f6\",\n        primaryDark: \"#1d4ed8\",\n        background: \"#f9fafb\",\n        surface: \"#ffffff\",\n        textPrimary: \"#111827\",\n        textSecondary: \"#4b5563\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\",\n        info: \"#3b82f6\",\n        border: \"#e5e7eb\",\n        hover: \"#f3f4f6\",\n        active: \"#e5e7eb\"\n    },\n    shadows: {\n        small: \"0 1px 2px 0 rgb(0 0 0 / 0.05)\",\n        medium: \"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)\",\n        large: \"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)\"\n    },\n    borderRadius: {\n        small: \"0.25rem\",\n        medium: \"0.5rem\",\n        large: \"1rem\"\n    }\n};\nconst darkTheme = {\n    name: \"dark\",\n    colors: {\n        primary: \"#3b82f6\",\n        primaryLight: \"#60a5fa\",\n        primaryDark: \"#2563eb\",\n        background: \"#111827\",\n        surface: \"#1f2937\",\n        textPrimary: \"#f9fafb\",\n        textSecondary: \"#d1d5db\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\",\n        info: \"#3b82f6\",\n        border: \"#374151\",\n        hover: \"#1f2937\",\n        active: \"#374151\"\n    },\n    shadows: {\n        small: \"0 1px 2px 0 rgb(0 0 0 / 0.25)\",\n        medium: \"0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)\",\n        large: \"0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3)\"\n    },\n    borderRadius: {\n        small: \"0.25rem\",\n        medium: \"0.5rem\",\n        large: \"1rem\"\n    }\n};\nconst blueTheme = {\n    name: \"blue\",\n    colors: {\n        primary: \"#1d4ed8\",\n        primaryLight: \"#2563eb\",\n        primaryDark: \"#1e40af\",\n        background: \"#eff6ff\",\n        surface: \"#ffffff\",\n        textPrimary: \"#1e3a8a\",\n        textSecondary: \"#1e40af\",\n        success: \"#047857\",\n        warning: \"#b45309\",\n        error: \"#b91c1c\",\n        info: \"#1d4ed8\",\n        border: \"#bfdbfe\",\n        hover: \"#dbeafe\",\n        active: \"#bfdbfe\"\n    },\n    shadows: {\n        small: \"0 1px 3px 0 rgb(29 78 216 / 0.1), 0 1px 2px -1px rgb(29 78 216 / 0.1)\",\n        medium: \"0 4px 6px -1px rgb(29 78 216 / 0.1), 0 2px 4px -2px rgb(29 78 216 / 0.1)\",\n        large: \"0 10px 15px -3px rgb(29 78 216 / 0.1), 0 4px 6px -4px rgb(29 78 216 / 0.1)\"\n    },\n    borderRadius: {\n        small: \"0.25rem\",\n        medium: \"0.5rem\",\n        large: \"1rem\"\n    }\n};\n// Export a default theme (can be changed based on user preference)\nconst defaultTheme = lightTheme;\n// Export all themes as an array\nconst allThemes = [\n    lightTheme,\n    darkTheme,\n    blueTheme\n];\n// Helper function to get a theme by name\nconst getThemeByName = (name)=>{\n    return allThemes.find((theme)=>theme.name === name) || defaultTheme;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./styles/themes.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f63b265f9b07\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kaWRvLWRpc3RyaWJ1dGlvbi1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz8xZDIzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjYzYjI2NWY5YjA3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(ssr)/./components/SideTaskBar/SideTaskBar.module.css":
/*!*******************************************************!*\
  !*** ./components/SideTaskBar/SideTaskBar.module.css ***!
  \*******************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"taskBar\": \"SideTaskBar_taskBar__ZuXqF\",\n\t\"primaryNavContainer\": \"SideTaskBar_primaryNavContainer__Qowr7\",\n\t\"secondaryNavContainer\": \"SideTaskBar_secondaryNavContainer___jRyu\",\n\t\"secondaryHeader\": \"SideTaskBar_secondaryHeader__VpQ4T\",\n\t\"activeItemName\": \"SideTaskBar_activeItemName__UiHBG\",\n\t\"headerDivider\": \"SideTaskBar_headerDivider__QZqDx\",\n\t\"logoText\": \"SideTaskBar_logoText__LjLra\",\n\t\"label\": \"SideTaskBar_label__CpV92\",\n\t\"chevron\": \"SideTaskBar_chevron__n2F5R\",\n\t\"profileInfo\": \"SideTaskBar_profileInfo__8Iu_V\",\n\t\"logoutButton\": \"SideTaskBar_logoutButton__Lid2R\",\n\t\"logo\": \"SideTaskBar_logo__mi68V\",\n\t\"logoIcon\": \"SideTaskBar_logoIcon__Qf1TA\",\n\t\"primaryNav\": \"SideTaskBar_primaryNav__0gyIV\",\n\t\"secondaryNav\": \"SideTaskBar_secondaryNav__ZCNaL\",\n\t\"navItem\": \"SideTaskBar_navItem__3TQDi\",\n\t\"navButton\": \"SideTaskBar_navButton__OqbEj\",\n\t\"active\": \"SideTaskBar_active__osF8h\",\n\t\"icon\": \"SideTaskBar_icon__geV4U\",\n\t\"rotated\": \"SideTaskBar_rotated__bXAyV\",\n\t\"subNavItem\": \"SideTaskBar_subNavItem__CSQh1\",\n\t\"subNavLink\": \"SideTaskBar_subNavLink__xOTPC\",\n\t\"subIcon\": \"SideTaskBar_subIcon__x1GtK\",\n\t\"footer\": \"SideTaskBar_footer__rwkcH\",\n\t\"profile\": \"SideTaskBar_profile__Gt5jk\",\n\t\"avatar\": \"SideTaskBar_avatar__fxU37\",\n\t\"userName\": \"SideTaskBar_userName__Px1id\",\n\t\"fadeIn\": \"SideTaskBar_fadeIn__kFMdU\"\n};\n\nmodule.exports.__checksum = \"fad6177692a0\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/SideTaskBar/SideTaskBar.module.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Dido Distribution\",\n    description: \"Distribution management system\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        richColors: true,\n                        closeButton: true,\n                        duration: 4000,\n                        position: \"top-center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBTU1BO0FBSmlCO0FBQ2lCO0FBQ1A7QUFJMUIsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1gsMkpBQWU7c0JBQzlCLDRFQUFDQyxpREFBU0E7O29CQUNQTTtrQ0FDRCw4REFBQ0wsMkNBQU9BO3dCQUFDVSxVQUFVO3dCQUFDQyxXQUFXO3dCQUFDQyxVQUFVO3dCQUFNQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS25FIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGlkby1kaXN0cmlidXRpb24tZnJvbnRlbmQvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XHJcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcclxuaW1wb3J0IHsgUHJvdmlkZXJzIH0gZnJvbSAnLi9wcm92aWRlcnMnO1xyXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAnc29ubmVyJztcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdEaWRvIERpc3RyaWJ1dGlvbicsXHJcbiAgZGVzY3JpcHRpb246ICdEaXN0cmlidXRpb24gbWFuYWdlbWVudCBzeXN0ZW0nLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XHJcbiAgICAgICAgPFByb3ZpZGVycz5cclxuICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgIDxUb2FzdGVyIHJpY2hDb2xvcnMgY2xvc2VCdXR0b24gZHVyYXRpb249ezQwMDB9IHBvc2l0aW9uPVwidG9wLWNlbnRlclwiIC8+XHJcbiAgICAgICAgPC9Qcm92aWRlcnM+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwiVG9hc3RlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInJpY2hDb2xvcnMiLCJjbG9zZUJ1dHRvbiIsImR1cmF0aW9uIiwicG9zaXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\workspace\projects\dido-distribution\frontend\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\workspace\projects\dido-distribution\frontend\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@tanstack","vendor-chunks/sonner","vendor-chunks/@swc","vendor-chunks/react-icons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();