import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsUUID } from "class-validator";

export class FilterSupplierDto {
  @ApiProperty({
    description: "Partial supplier name, case-insensitive",
    required: false,
    example: "ABC Company",
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: "Partial email address, case-insensitive",
    required: false,
    example: "<EMAIL>",
  })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({
    description: "Partial phone number, case-insensitive",
    required: false,
    example: "+1234567890",
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: "Partial supplier code, case-insensitive",
    required: false,
    example: "SUP001",
  })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({
    description: "Partial fiscal ID, case-insensitive",
    required: false,
    example: "12345678901",
  })
  @IsOptional()
  @IsString()
  fiscalId?: string;

  @ApiProperty({
    description: "UUIDv7 string of the warehouse",
    required: false,
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @IsOptional()
  @IsUUID()
  warehouseUuid?: string;

  @ApiProperty({
    description: "UUIDv7 string of the user",
    required: false,
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @IsOptional()
  @IsUUID()
  userUuid?: string;
} 