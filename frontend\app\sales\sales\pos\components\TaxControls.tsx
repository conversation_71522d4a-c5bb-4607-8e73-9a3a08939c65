import React, { useState, useEffect } from 'react';
import type { TaxControlsProps } from '../types';

export function TaxControls({ taxEnabled, taxRate, onTaxEnabledChange, onTaxRateChange, disabled = false }: TaxControlsProps) {
  // Ensure taxRate is always a valid number with fallback to 0
  const safeTaxRate = typeof taxRate === 'number' && !isNaN(taxRate) ? taxRate : 0;
  // Ensure taxEnabled is always a boolean
  const safeTaxEnabled = typeof taxEnabled === 'boolean' ? taxEnabled : false;
  
  const [taxRateInput, setTaxRateInput] = useState((safeTaxRate * 100).toFixed(2));

  // Update input when taxRate prop changes
  useEffect(() => {
    const newSafeTaxRate = typeof taxRate === 'number' && !isNaN(taxRate) ? taxRate : 0;
    setTaxRateInput((newSafeTaxRate * 100).toFixed(2));
  }, [taxRate]);

  const handleTaxRateChange = (value: string) => {
    setTaxRateInput(value);
    const rate = parseFloat(value) || 0;
    onTaxRateChange(rate / 100);
  };

  return (
    <div className="mb-4 space-y-3">
      {/* Tax Toggle and Rate Input - Same horizontal line */}
      <div className="flex items-center justify-between">
        <label className="flex items-center space-x-2 cursor-pointer">
          <input
            type="checkbox"
            checked={safeTaxEnabled}
            onChange={(e) => onTaxEnabledChange(e.target.checked)}
            disabled={disabled}
            className={`w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 ${
              disabled ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          />
          <span className="text-sm font-medium text-gray-700">Apply Tax</span>
        </label>

        {/* Tax Rate Input - Only show when tax is enabled */}
        {safeTaxEnabled && (
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">
              Tax Rate (%)
            </label>
            <input
              type="number"
              min="0"
              max="100"
              step="0.5"
              value={taxRateInput}
              onChange={(e) => handleTaxRateChange(e.target.value)}
              disabled={disabled}
              className={`w-20 py-1 px-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm ${
                disabled ? 'bg-gray-100 cursor-not-allowed opacity-50' : ''
              }`}
              placeholder="10.00"
            />
            <span className="text-sm text-gray-500">%</span>
          </div>
        )}
      </div>
    </div>
  );
} 