import React, { useState, useEffect } from 'react';
import { Purchase } from '../purchaseApi';

interface PurchaseCancelModalProps {
  isOpen: boolean;
  purchaseToCancel: Purchase | null;
  isCancelling: boolean;
  onConfirm: (reason?: string) => void;
  onClose: () => void;
}

export const PurchaseCancelModal: React.FC<PurchaseCancelModalProps> = ({
  isOpen,
  purchaseToCancel,
  isCancelling,
  onConfirm,
  onClose,
}) => {
  const [reason, setReason] = useState('');

  // Reset reason when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setReason('');
    }
  }, [isOpen]);

  const handleConfirm = () => {
    onConfirm(reason.trim() || undefined);
  };
  if (!isOpen || !purchaseToCancel) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-red-600">Cancel Purchase Order</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isCancelling}
          >
            ✕
          </button>
        </div>
        
        <div className="mb-6">
          <p className="text-gray-700 mb-2">
            Are you sure you want to cancel this purchase order?
          </p>
          <div className="bg-gray-50 p-3 rounded mb-4">
            <p className="text-sm"><strong>Purchase Order:</strong> {purchaseToCancel.purchaseOrderNumber}</p>
            <p className="text-sm"><strong>Supplier:</strong> {purchaseToCancel.supplierName || 'Unknown Supplier'}</p>
            <p className="text-sm"><strong>Amount:</strong> ${purchaseToCancel.totalAmount.toFixed(2)}</p>
          </div>

          <div className="mb-4">
            <label htmlFor="cancelReason" className="block text-sm font-medium text-gray-700 mb-2">
              Reason for cancellation (optional)
            </label>
            <textarea
              id="cancelReason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              rows={3}
              placeholder="Enter reason for cancelling this purchase order..."
              disabled={isCancelling}
            />
          </div>

          <p className="text-red-600 text-sm">
            This action cannot be undone.
          </p>
        </div>
        
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
            disabled={isCancelling}
          >
            Keep Purchase
          </button>
          <button
            onClick={handleConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
            disabled={isCancelling}
          >
            {isCancelling ? 'Cancelling...' : 'Cancel Purchase'}
          </button>
        </div>
      </div>
    </div>
  );
};
