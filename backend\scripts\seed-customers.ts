/**
 * Customer Seeding Script using Customer Controller API
 * 
 * Usage: npx ts-node scripts/seed-customers.ts
 * 
 * This script creates customers using the customer controller API endpoints
 * instead of direct database access for better consistency with the application.
 */

import axios from 'axios';
const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');
const fs = require('fs');

// Node.js global declarations
declare const process: any;
declare const require: any;
declare const module: any;
declare const __dirname: string;

// Configuration
const API_BASE_URL = 'http://localhost:8000';
const TOTAL_CUSTOMERS = 1000;
const TOTAL_REGIONS = 15;
const BATCH_SIZE = 50;

// Algiers coordinates (center of the city)
const ALGIERS_CENTER = {
  latitude: 36.7538,
  longitude: 3.0588
};

// Algiers regions with realistic names and approximate coordinates
const ALGIERS_REGIONS = [
  { name: 'Alger Centre', description: 'Central business district and administrative center', latitude: 36.7538, longitude: 3.0588 },
  { name: '<PERSON>b <PERSON>', description: 'Historic coastal district with traditional markets', latitude: 36.7900, longitude: 3.0500 },
  { name: 'Casbah', description: 'UNESCO World Heritage site, historic old town', latitude: 36.7850, longitude: 3.0600 },
  { name: 'El Madania', description: 'Residential area with modern housing', latitude: 36.7500, longitude: 3.0400 },
  { name: 'Sidi M\'Hamed', description: 'Government and embassy district', latitude: 36.7600, longitude: 3.0500 },
  { name: 'El Mouradia', description: 'Presidential palace and diplomatic area', latitude: 36.7700, longitude: 3.0400 },
  { name: 'Bologhine', description: 'Coastal residential area with beaches', latitude: 36.8000, longitude: 3.0200 },
  { name: 'Oued Koriche', description: 'Port area and industrial zone', latitude: 36.7800, longitude: 3.0800 },
  { name: 'Bir Mourad Rais', description: 'University district and student area', latitude: 36.7400, longitude: 3.0300 },
  { name: 'El Biar', description: 'Upscale residential neighborhood', latitude: 36.7600, longitude: 3.0200 },
  { name: 'Bouzareah', description: 'Hilly residential area with panoramic views', latitude: 36.7800, longitude: 3.0100 },
  { name: 'Cheraga', description: 'Western suburbs with modern developments', latitude: 36.7700, longitude: 2.9500 },
  { name: 'Draria', description: 'Eastern residential area', latitude: 36.7200, longitude: 3.0800 },
  { name: 'Zeralda', description: 'Coastal resort area west of Algiers', latitude: 36.7500, longitude: 2.8500 },
  { name: 'Staoueli', description: 'Beach resort and residential area', latitude: 36.7600, longitude: 2.9000 }
];

// Generate random coordinates within Algiers metropolitan area (approximately 20km radius)
function generateAlgiersCoordinates() {
  const radiusInDegrees = 0.18; // Approximately 20km
  const angle = Math.random() * 2 * Math.PI;
  const radius = Math.random() * radiusInDegrees;
  
  return {
    latitude: ALGIERS_CENTER.latitude + radius * Math.cos(angle),
    longitude: ALGIERS_CENTER.longitude + radius * Math.sin(angle)
  };
}

// Sample data for generating realistic Algerian customers
const algerianFirstNames = [
  'Ahmed', 'Mohamed', 'Fatima', 'Aicha', 'Omar', 'Khadija', 'Youssef', 'Amina',
  'Ali', 'Nadia', 'Karim', 'Zohra', 'Rachid', 'Samira', 'Hamid', 'Leila',
  'Abderrahmane', 'Malika', 'Mustapha', 'Yamina', 'Salim', 'Djamila', 'Tarek', 'Souad',
  'Abdelkader', 'Farida', 'Brahim', 'Naima', 'Djamel', 'Zineb', 'Nordine', 'Warda',
  'Farid', 'Karima', 'Hocine', 'Houria', 'Samir', 'Dalila', 'Abdelaziz', 'Hayat'
];

const algerianLastNames = [
  'Benali', 'Belaidi', 'Bensalem', 'Boumediene', 'Cherif', 'Djelloul', 'Ferhat', 'Ghali',
  'Hadj', 'Ikhlef', 'Kaci', 'Larbi', 'Meddour', 'Naceur', 'Ouali', 'Ramdane',
  'Sahraoui', 'Tebboune', 'Yahiaoui', 'Zidane', 'Amara', 'Boucherit', 'Chabane', 'Derradji',
  'Essaid', 'Fares', 'Guechi', 'Hamza', 'Idir', 'Khelifa', 'Lakhdar', 'Mammeri',
  'Nouri', 'Ould', 'Rebai', 'Slimani', 'Tounsi', 'Yacine', 'Zerrouki', 'Ait'
];

const algiersDistricts = [
  'Alger Centre', 'Bab El Oued', 'Casbah', 'El Madania', 'Sidi M\'Hamed',
  'El Mouradia', 'Bologhine', 'Oued Koriche', 'Bir Mourad Rais', 'El Biar',
  'Bouzareah', 'Cheraga', 'Draria', 'Zeralda', 'Staoueli', 'Ain Benian',
  'Bordj El Kiffan', 'El Marsa', 'Rouiba', 'Reghaia', 'Dar El Beida',
  'Bab Ezzouar', 'Boumerdes', 'Khraicia', 'Ain Taya', 'Heraoua',
  'Souidania', 'Tessala El Merdja', 'Ouled Chebel', 'Saoula', 'Birtouta'
];

const streetTypes = ['Rue', 'Avenue', 'Boulevard', 'Impasse', 'Place', 'Cité'];
const streetNames = [
  'des Martyrs', 'de l\'Indépendance', 'Didouche Mourad', 'Hassiba Ben Bouali',
  'Colonel Amirouche', 'Larbi Ben M\'hidi', 'Abane Ramdane', 'Krim Belkacem',
  '1er Novembre', 'Ben Bella', 'Houari Boumediene', 'Mohamed Khemisti',
  'Frantz Fanon', 'Emir Abdelkader', 'Ibn Khaldoun', 'El Mokrani',
  'Cheikh Bouamama', 'Si El Haouès', 'Mohamed Belouizdad', 'Bachir Attar'
];

const customerTypes: ('retail' | 'wholesale' | 'mid-wholesale' | 'institutional')[] = [
  'retail', 'wholesale', 'mid-wholesale', 'institutional'
];

// Interfaces matching the API DTOs
interface Warehouse {
  uuid: string;
  name: string;
  description?: string;
  userUuidString: string;
  mainStorageUuidString?: string;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Region {
  uuid: string;
  name: string;
  description?: string;
  latitude?: number;
  longitude?: number;
  warehouseUuidString?: string;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface CreateRegionDto {
  name: string;
  description?: string;
  latitude?: number;
  longitude?: number;
  warehouseUuid?: string;
}

interface CreateCustomerDto {
  name: string;
  fiscalId?: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  warehouseUuid?: string;
  regionUuid?: string;
  currentCredit?: number;
}

// Utility functions
function generateAlgerianPhone(): string {
  const prefixes = ['05', '06', '07'];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const number = Math.floor(Math.random() * 90000000) + 10000000; // 8 digits
  return `+213${prefix}${number}`;
}

function generateFiscalId(): string {
  return Math.floor(Math.random() * 90000000000) + 10000000000 + ''; // 11 digits
}

function findClosestRegion(latitude: number, longitude: number, regions: Region[]): Region | null {
  if (regions.length === 0) return null;
  
  let closestRegion = regions[0];
  let minDistance = Number.MAX_VALUE;
  
  for (const region of regions) {
    if (region.latitude && region.longitude) {
      const distance = Math.sqrt(
        Math.pow(latitude - region.latitude, 2) + 
        Math.pow(longitude - region.longitude, 2)
      );
      if (distance < minDistance) {
        minDistance = distance;
        closestRegion = region;
      }
    }
  }
  
  return closestRegion;
}

function generateRandomCustomer(warehouses: Warehouse[], regions: Region[]): CreateCustomerDto {
  const firstName = algerianFirstNames[Math.floor(Math.random() * algerianFirstNames.length)];
  const lastName = algerianLastNames[Math.floor(Math.random() * algerianLastNames.length)];
  const name = `${firstName} ${lastName}`;
  
  const customerType = customerTypes[Math.floor(Math.random() * customerTypes.length)];
  const coordinates = generateAlgiersCoordinates();
  const warehouse = warehouses[Math.floor(Math.random() * warehouses.length)];
  const closestRegion = findClosestRegion(coordinates.latitude, coordinates.longitude, regions);
  
  const district = algiersDistricts[Math.floor(Math.random() * algiersDistricts.length)];
  const streetType = streetTypes[Math.floor(Math.random() * streetTypes.length)];
  const streetName = streetNames[Math.floor(Math.random() * streetNames.length)];
  const streetNumber = Math.floor(Math.random() * 200) + 1;
  
  const address = `${streetType} ${streetName}, ${streetNumber}, ${district}, Algiers, Algeria`;
  
  // Generate email based on name
  const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`;
  
  // Generate fiscal ID (only for wholesale and institutional customers)
  const fiscalId = (customerType === 'wholesale' || customerType === 'institutional') 
    ? generateFiscalId() 
    : undefined;
  
  // Generate RC (commercial register) for business customers
  const rc = (customerType === 'wholesale' || customerType === 'institutional') 
    ? `RC${Math.floor(Math.random() * 900000) + 100000}` 
    : undefined;
  
  // Generate article number for business customers
  const articleNumber = (customerType === 'wholesale' || customerType === 'institutional') 
    ? `ART${Math.floor(Math.random() * 9000) + 1000}` 
    : undefined;
  
  // Generate initial credit (higher for wholesale/institutional)
  let currentCredit = 0;
  if (customerType === 'retail') {
    currentCredit = Math.random() < 0.3 ? Math.floor(Math.random() * 1000) : 0;
  } else if (customerType === 'mid-wholesale') {
    currentCredit = Math.random() < 0.5 ? Math.floor(Math.random() * 5000) : 0;
  } else if (customerType === 'wholesale') {
    currentCredit = Math.random() < 0.7 ? Math.floor(Math.random() * 20000) : 0;
  } else { // institutional
    currentCredit = Math.random() < 0.8 ? Math.floor(Math.random() * 50000) : 0;
  }
  
  return {
    name,
    fiscalId,
    email,
    phone: generateAlgerianPhone(),
    address,
    rc,
    articleNumber,
    customerType,
    latitude: coordinates.latitude,
    longitude: coordinates.longitude,
    warehouseUuid: warehouse.uuid,
    regionUuid: closestRegion?.uuid,
    currentCredit
  };
}

// API functions
async function fetchWarehouses(): Promise<Warehouse[]> {
  try {
    console.log('Fetching warehouses...');
    const response = await axios.get(`${API_BASE_URL}/warehouses/list?limit=100`);
    const warehouses = response.data.data || response.data;
    console.log(`Found ${warehouses.length} warehouses`);
    return warehouses;
  } catch (error) {
    console.error('Error fetching warehouses:', error.response?.data || error.message);
    throw error;
  }
}

async function createRegion(region: CreateRegionDto, retries = 3): Promise<Region | null> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await axios.post(`${API_BASE_URL}/regions`, region);
      return response.data;
    } catch (error) {
      if (error.response?.status === 409) {
        console.log(`Region "${region.name}" already exists, skipping...`);
        return null; // Region already exists
      }
      console.error(`Attempt ${attempt} failed to create region "${region.name}":`, error.response?.data || error.message);
      if (attempt === retries) {
        console.error(`Failed to create region "${region.name}" after ${retries} attempts`);
        return null;
      }
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
  return null;
}

async function createRegions(warehouses: Warehouse[]): Promise<Region[]> {
  console.log('Creating regions...');
  const regions: Region[] = [];
  
  for (const warehouse of warehouses) {
    for (let i = 0; i < TOTAL_REGIONS; i++) {
      const regionData = ALGIERS_REGIONS[i % ALGIERS_REGIONS.length];
      const region: CreateRegionDto = {
        name: `${regionData.name} - ${warehouse.name}`,
        description: regionData.description,
        latitude: regionData.latitude,
        longitude: regionData.longitude,
        warehouseUuid: warehouse.uuid
      };
      
      const createdRegion = await createRegion(region);
      if (createdRegion) {
        regions.push(createdRegion);
        console.log(`Created region: ${createdRegion.name}`);
      }
    }
  }
  
  console.log(`Created ${regions.length} regions`);
  return regions;
}

async function createCustomer(customer: CreateCustomerDto, retries = 3): Promise<boolean> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await axios.post(`${API_BASE_URL}/customers`, customer);
      return true;
    } catch (error) {
      console.error(`Attempt ${attempt} failed to create customer "${customer.name}":`, error.response?.data || error.message);
      if (attempt === retries) {
        console.error(`Failed to create customer "${customer.name}" after ${retries} attempts`);
        return false;
      }
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
  return false;
}

async function createCustomersBatch(customers: CreateCustomerDto[]): Promise<number> {
  const promises = customers.map(customer => createCustomer(customer));
  const results = await Promise.all(promises);
  return results.filter(result => result).length;
}

async function seedCustomers(warehouses: Warehouse[], regions: Region[]): Promise<void> {
  console.log(`Starting to seed ${TOTAL_CUSTOMERS} customers...`);
  
  let successCount = 0;
  let totalBatches = Math.ceil(TOTAL_CUSTOMERS / BATCH_SIZE);
  
  for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
    const batchStart = batchIndex * BATCH_SIZE;
    const batchEnd = Math.min(batchStart + BATCH_SIZE, TOTAL_CUSTOMERS);
    const batchSize = batchEnd - batchStart;
    
    console.log(`Processing batch ${batchIndex + 1}/${totalBatches} (${batchSize} customers)...`);
    
    const customers = [];
    for (let i = 0; i < batchSize; i++) {
      customers.push(generateRandomCustomer(warehouses, regions));
    }
    
    const batchSuccessCount = await createCustomersBatch(customers);
    successCount += batchSuccessCount;
    
    console.log(`Batch ${batchIndex + 1} completed: ${batchSuccessCount}/${batchSize} customers created`);
    console.log(`Total progress: ${successCount}/${TOTAL_CUSTOMERS} customers created`);
    
    // Add a small delay between batches to avoid overwhelming the server
    if (batchIndex < totalBatches - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log(`Customer seeding completed: ${successCount}/${TOTAL_CUSTOMERS} customers created successfully`);
}

function generateStats(warehouses: Warehouse[], regions: Region[]): void {
  console.log('\n=== SEEDING STATISTICS ===');
  console.log(`Warehouses: ${warehouses.length}`);
  console.log(`Regions: ${regions.length}`);
  console.log(`Total customers to create: ${TOTAL_CUSTOMERS}`);
  console.log(`Batch size: ${BATCH_SIZE}`);
  console.log('========================\n');
}

async function performFullDatabaseBackup(): Promise<void> {
  console.log('⚠️  Performing full database backup before seeding...');
  
  try {
    const backupScriptPath = path.join(__dirname, 'create_backup.py');
    const execAsync = promisify(exec);
    
    console.log('Running backup script...');
    const { stdout, stderr } = await execAsync(`python "${backupScriptPath}"`);
    
    if (stderr) {
      console.warn('Backup script warnings:', stderr);
    }
    
    console.log('Backup completed successfully');
    console.log('Backup output:', stdout);
  } catch (error) {
    console.error('❌ Backup failed:', error);
    console.error('⚠️  Proceeding without backup (use at your own risk)');
  }
}

async function main() {
  console.log('🚀 Starting Customer Seeding Script');
  console.log('=====================================');
  
  try {
    // Check if API is available
    console.log('Checking API availability...');
    await axios.get(`${API_BASE_URL}/health`).catch(() => {
      throw new Error(`API not available at ${API_BASE_URL}. Please ensure the backend server is running.`);
    });
    console.log('✅ API is available');
    
    // Perform backup
    await performFullDatabaseBackup();
    
    // Fetch existing warehouses
    const warehouses = await fetchWarehouses();
    if (warehouses.length === 0) {
      throw new Error('No warehouses found. Please create warehouses first.');
    }
    
    // Create regions
    const regions = await createRegions(warehouses);
    
    // Generate and display statistics
    generateStats(warehouses, regions);
    
    // Seed customers
    await seedCustomers(warehouses, regions);
    
    console.log('\n✅ Customer seeding completed successfully!');
    console.log('=====================================');
    
  } catch (error) {
    console.error('\n❌ Customer seeding failed:', error.message);
    console.error('=====================================');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

export { main as seedCustomers }; 