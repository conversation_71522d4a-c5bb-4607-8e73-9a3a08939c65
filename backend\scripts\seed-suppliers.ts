/**
 * Supplier Seeding Script using Supplier Controller API
 * 
 * Usage: npx ts-node scripts/seed-suppliers.ts
 * 
 * This script creates suppliers using the supplier controller API endpoints
 * instead of direct database access for better consistency with the application.
 */

import axios from 'axios';
const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');
const fs = require('fs');

// Node.js global declarations
declare const process: any;
declare const require: any;
declare const module: any;
declare const __dirname: string;

// Configuration
const API_BASE_URL = 'http://localhost:8000';
const TOTAL_SUPPLIERS = 500;
const BATCH_SIZE = 50;

// Algiers coordinates (center of the city)
const ALGIERS_CENTER = {
  latitude: 36.7538,
  longitude: 3.0588
};

// Generate random coordinates within Algiers metropolitan area (approximately 20km radius)
function generateAlgiersCoordinates() {
  const radiusInDegrees = 0.18; // Approximately 20km
  const angle = Math.random() * 2 * Math.PI;
  const radius = Math.random() * radiusInDegrees;
  
  return {
    latitude: ALGIERS_CENTER.latitude + radius * Math.cos(angle),
    longitude: ALGIERS_CENTER.longitude + radius * Math.sin(angle)
  };
}

// Sample data for generating realistic Algerian suppliers
const algerianCompanyNames = [
  'SARL', 'EURL', 'SPA', 'SNC', 'GIE', 'Coopérative', 'Association'
];

const algerianCompanyPrefixes = [
  'Algérie', 'Maghreb', 'Mediterranée', 'Sahara', 'Atlas', 'Kabylie', 'Oran', 'Constantine',
  'Annaba', 'Tlemcen', 'Tamanrasset', 'Ghardaia', 'Bejaia', 'Setif', 'Batna', 'Tebessa',
  'El Oued', 'Biskra', 'Laghouat', 'Djelfa', 'M\'Sila', 'Tiaret', 'Saida', 'Relizane'
];

const algerianCompanySuffixes = [
  'Industries', 'Commerce', 'Distribution', 'Import-Export', 'Production', 'Services',
  'Fournitures', 'Équipements', 'Matériaux', 'Produits', 'Solutions', 'Technologies',
  'Agroalimentaire', 'Textile', 'Métallurgie', 'Chimie', 'Construction', 'Transport',
  'Logistique', 'Informatique', 'Électronique', 'Mécanique', 'Électrique', 'Plastique'
];

const algerianFirstNames = [
  'Ahmed', 'Mohamed', 'Fatima', 'Aicha', 'Omar', 'Khadija', 'Youssef', 'Amina',
  'Ali', 'Nadia', 'Karim', 'Zohra', 'Rachid', 'Samira', 'Hamid', 'Leila',
  'Abderrahmane', 'Malika', 'Mustapha', 'Yamina', 'Salim', 'Djamila', 'Tarek', 'Souad',
  'Abdelkader', 'Farida', 'Brahim', 'Naima', 'Djamel', 'Zineb', 'Nordine', 'Warda',
  'Farid', 'Karima', 'Hocine', 'Houria', 'Samir', 'Dalila', 'Abdelaziz', 'Hayat'
];

const algerianLastNames = [
  'Benali', 'Belaidi', 'Bensalem', 'Boumediene', 'Cherif', 'Djelloul', 'Ferhat', 'Ghali',
  'Hadj', 'Ikhlef', 'Kaci', 'Larbi', 'Meddour', 'Naceur', 'Ouali', 'Ramdane',
  'Sahraoui', 'Tebboune', 'Yahiaoui', 'Zidane', 'Amara', 'Boucherit', 'Chabane', 'Derradji',
  'Essaid', 'Fares', 'Guechi', 'Hamza', 'Idir', 'Khelifa', 'Lakhdar', 'Mammeri',
  'Nouri', 'Ould', 'Rebai', 'Slimani', 'Tounsi', 'Yacine', 'Zerrouki', 'Ait'
];

const algiersDistricts = [
  'Alger Centre', 'Bab El Oued', 'Casbah', 'El Madania', 'Sidi M\'Hamed',
  'El Mouradia', 'Bologhine', 'Oued Koriche', 'Bir Mourad Rais', 'El Biar',
  'Bouzareah', 'Cheraga', 'Draria', 'Zeralda', 'Staoueli', 'Ain Benian',
  'Bordj El Kiffan', 'El Marsa', 'Rouiba', 'Reghaia', 'Dar El Beida',
  'Bab Ezzouar', 'Boumerdes', 'Khraicia', 'Ain Taya', 'Heraoua',
  'Souidania', 'Tessala El Merdja', 'Ouled Chebel', 'Saoula', 'Birtouta'
];

const streetTypes = ['Rue', 'Avenue', 'Boulevard', 'Impasse', 'Place', 'Cité', 'Zone Industrielle'];
const streetNames = [
  'des Martyrs', 'de l\'Indépendance', 'Didouche Mourad', 'Hassiba Ben Bouali',
  'Colonel Amirouche', 'Larbi Ben M\'hidi', 'Abane Ramdane', 'Krim Belkacem',
  '1er Novembre', 'Ben Bella', 'Houari Boumediene', 'Mohamed Khemisti',
  'Frantz Fanon', 'Emir Abdelkader', 'Ibn Khaldoun', 'El Mokrani',
  'Cheikh Bouamama', 'Si El Haouès', 'Mohamed Belouizdad', 'Bachir Attar',
  'de la Production', 'Industrielle', 'Commerciale', 'des Affaires'
];

const supplierTypes = [
  'Electronics', 'Textile', 'Food & Beverages', 'Construction Materials', 'Automotive Parts',
  'Medical Supplies', 'Office Supplies', 'Agricultural Products', 'Chemical Products',
  'Machinery & Equipment', 'Plastic Products', 'Metal Products', 'Paper Products',
  'Pharmaceuticals', 'Cosmetics', 'Tools & Hardware', 'Furniture', 'Lighting',
  'Security Equipment', 'Telecommunications', 'Energy Products', 'Waste Management',
  'Packaging Materials', 'Raw Materials', 'Finished Goods'
];

interface Warehouse {
  uuid: string;
  name: string;
  description?: string;
  userUuidString: string;
  mainStorageUuidString?: string;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface CreateSupplierDto {
  name: string;
  fiscalId?: string;
  rc?: string;
  code?: string;
  articleNumber?: string;
  description?: string;
  email?: string;
  phone?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  notes?: string;
  warehouseUuid?: string;
  userUuid: string;
}

function generateAlgerianPhone(): string {
  const prefixes = ['+213', '00213'];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const mobilePrefixes = ['5', '6', '7'];
  const mobilePrefix = mobilePrefixes[Math.floor(Math.random() * mobilePrefixes.length)];
  const number = Math.floor(Math.random() * 90000000) + 10000000; // 8 digits
  return `${prefix}${mobilePrefix}${number}`;
}

function generateFiscalId(): string {
  return `FISC${Math.floor(Math.random() * 900000000) + 100000000}`;
}

function generateRC(): string {
  return `RC${Math.floor(Math.random() * 900000000) + 100000000}`;
}

function generateSupplierCode(): string {
  return `SUP${Math.floor(Math.random() * 9000) + 1000}`;
}

function generateArticleNumber(): string {
  const year = new Date().getFullYear();
  return `ART${Math.floor(Math.random() * 9000) + 1000}-${year}`;
}

function generateRandomSupplier(warehouses: Warehouse[]): CreateSupplierDto {
  const warehouse = warehouses[Math.floor(Math.random() * warehouses.length)];
  const coordinates = generateAlgiersCoordinates();
  const district = algiersDistricts[Math.floor(Math.random() * algiersDistricts.length)];
  const streetType = streetTypes[Math.floor(Math.random() * streetTypes.length)];
  const streetName = streetNames[Math.floor(Math.random() * streetNames.length)];
  const streetNumber = Math.floor(Math.random() * 200) + 1;
  
  const companyPrefix = algerianCompanyPrefixes[Math.floor(Math.random() * algerianCompanyPrefixes.length)];
  const companySuffix = algerianCompanySuffixes[Math.floor(Math.random() * algerianCompanySuffixes.length)];
  const companyType = algerianCompanyNames[Math.floor(Math.random() * algerianCompanyNames.length)];
  const supplierType = supplierTypes[Math.floor(Math.random() * supplierTypes.length)];
  
  const firstName = algerianFirstNames[Math.floor(Math.random() * algerianFirstNames.length)];
  const lastName = algerianLastNames[Math.floor(Math.random() * algerianLastNames.length)];
  
  const name = `${companyPrefix} ${companySuffix} ${companyType}`;
  const description = `Fournisseur spécialisé dans ${supplierType.toLowerCase()}. Entreprise établie et fiable.`;
  const address = `${streetType} ${streetName}, N° ${streetNumber}, ${district}, Alger, Algérie`;
  const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${companyPrefix.toLowerCase()}.dz`;
  
  return {
    name,
    fiscalId: generateFiscalId(),
    rc: generateRC(),
    code: generateSupplierCode(),
    articleNumber: generateArticleNumber(),
    description,
    email,
    phone: generateAlgerianPhone(),
    address,
    latitude: coordinates.latitude,
    longitude: coordinates.longitude,
    notes: `Contact principal: ${firstName} ${lastName}. Spécialisé en ${supplierType.toLowerCase()}.`,
    warehouseUuid: warehouse.uuid,
    userUuid: warehouse.userUuidString
  };
}

async function fetchWarehouses(): Promise<Warehouse[]> {
  console.log('🔍 Fetching warehouses...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/warehouses/list?limit=100`);
    const warehouses = response.data.data || response.data;
    
    if (!warehouses || warehouses.length === 0) {
      throw new Error('No warehouses found. Please create at least one warehouse before running this script.');
    }
    
    console.log(`✅ Found ${warehouses.length} warehouse(s)`);
    return warehouses;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(`Failed to fetch warehouses: ${error.response?.data?.message || error.message}`);
    }
    throw error;
  }
}

async function createSupplier(supplier: CreateSupplierDto, retries = 3): Promise<boolean> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      await axios.post(`${API_BASE_URL}/suppliers`, supplier);
      return true;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || error.message;
        
        // If it's a duplicate error, we can skip
        if (errorMessage.includes('duplicate') || errorMessage.includes('already exists')) {
          console.log(`⚠️  Supplier already exists: ${supplier.name}`);
          return true;
        }
        
        if (attempt === retries) {
          console.error(`❌ Failed to create supplier after ${retries} attempts: ${supplier.name}`);
          console.error(`   Error: ${errorMessage}`);
          return false;
        }
        
        // Wait before retrying with exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        console.log(`⚠️  Attempt ${attempt} failed for ${supplier.name}, retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error;
      }
    }
  }
  return false;
}

async function createSuppliersBatch(suppliers: CreateSupplierDto[]): Promise<number> {
  const results = await Promise.all(suppliers.map(supplier => createSupplier(supplier)));
  return results.filter(result => result).length;
}

async function seedSuppliers(warehouses: Warehouse[]): Promise<void> {
  console.log(`🚀 Starting supplier seeding for ${TOTAL_SUPPLIERS} suppliers...`);
  console.log(`📦 Suppliers will be distributed across ${warehouses.length} warehouse(s)`);
  
  const startTime = Date.now();
  let totalCreated = 0;
  let totalBatches = Math.ceil(TOTAL_SUPPLIERS / BATCH_SIZE);
  
  for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
    const batchStart = (batchIndex * BATCH_SIZE);
    const batchEnd = Math.min(batchStart + BATCH_SIZE, TOTAL_SUPPLIERS);
    const batchSize = batchEnd - batchStart;
    
    console.log(`\n📦 Processing batch ${batchIndex + 1}/${totalBatches} (suppliers ${batchStart + 1}-${batchEnd})...`);
    
    const suppliers = Array.from({ length: batchSize }, () => generateRandomSupplier(warehouses));
    const created = await createSuppliersBatch(suppliers);
    
    totalCreated += created;
    const progress = ((batchIndex + 1) / totalBatches * 100).toFixed(1);
    const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
    
    console.log(`✅ Batch ${batchIndex + 1} completed: ${created}/${batchSize} suppliers created`);
    console.log(`📊 Progress: ${progress}% (${totalCreated}/${TOTAL_SUPPLIERS} total, ${elapsed}s elapsed)`);
    
    // Small delay between batches to avoid overwhelming the server
    if (batchIndex < totalBatches - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  const totalTime = ((Date.now() - startTime) / 1000).toFixed(1);
  console.log(`\n🎉 Supplier seeding completed!`);
  console.log(`📊 Summary:`);
  console.log(`   • Total suppliers created: ${totalCreated}/${TOTAL_SUPPLIERS}`);
  console.log(`   • Total time: ${totalTime} seconds`);
  console.log(`   • Average rate: ${(totalCreated / parseFloat(totalTime)).toFixed(1)} suppliers/second`);
}

function generateStats(warehouses: Warehouse[]): void {
  console.log(`\n📈 Supplier Seeding Statistics:`);
  console.log(`   • Target suppliers per warehouse: ${Math.ceil(TOTAL_SUPPLIERS / warehouses.length)}`);
  console.log(`   • Batch size: ${BATCH_SIZE}`);
  console.log(`   • Estimated time: 3-5 minutes`);
  console.log(`   • Geographic coverage: Algiers metropolitan area`);
  console.log(`   • Supplier types: ${supplierTypes.length} different categories`);
}

async function performFullDatabaseBackup(): Promise<void> {
  console.log('⚠️  Performing full database backup before seeding...');
  
  try {
    const backupScriptPath = path.join(__dirname, 'create_backup.py');
    const execAsync = promisify(exec);
    
    console.log('Running backup script...');
    const { stdout, stderr } = await execAsync(`python "${backupScriptPath}"`);
    
    if (stderr) {
      console.warn('Backup script warnings:', stderr);
    }
    
    console.log('✅ Database backup completed successfully');
  } catch (error) {
    console.warn('⚠️  Database backup failed, continuing with seeding...');
    console.warn(`   Error: ${error}`);
  }
}

async function main() {
  console.log('🏭 Supplier Seeding Script');
  console.log('========================');
  
  try {
    // Perform database backup
    await performFullDatabaseBackup();
    
    // Fetch warehouses
    const warehouses = await fetchWarehouses();
    
    // Display statistics
    generateStats(warehouses);
    
    // Seed suppliers
    await seedSuppliers(warehouses);
    
    console.log('\n✅ Supplier seeding script completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Supplier seeding script failed:');
    console.error(error);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

export { seedSuppliers, fetchWarehouses }; 