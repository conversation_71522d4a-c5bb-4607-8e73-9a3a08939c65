import { useState } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { getPurchase, cancelPurchase, Purchase } from '../purchaseApi';
import { getCompaniesByUser } from '../../../settings/companiesApi';
import { getSupplier as getSupplierByUuid } from '../../../purchasing/suppliers/suppliersApi';
import { updateUrlParams } from '../purchaseHelpers';
import { filterSuppliers } from '../../../purchasing/suppliers/suppliersApi';

// Interface definitions for invoice printing
interface CompanyInfo {
  uuid: string;
  name: string;
  nif: string;
  rc: string;
  articleNumber: string;
  address: string;
  phoneNumber: string;
  website?: string;
}

interface SupplierInfo {
  uuid: string;
  name: string;
  fiscalId: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  supplierType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}

export const usePurchaseActions = (refetchPurchases?: () => void) => {
  const { user } = useAuth();
  
  // Modal states
  const [selectedPurchase, setSelectedPurchase] = useState<Purchase | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isInvoiceModalOpen, setIsInvoiceModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [purchaseToCancel, setPurchaseToCancel] = useState<Purchase | null>(null);
  
  // Loading states
  const [isLoadingInvoice, setIsLoadingInvoice] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [isLoadingEditPurchase, setIsLoadingEditPurchase] = useState(false);
  const [isLoadingViewDetails, setIsLoadingViewDetails] = useState(false);
  const [isLoadingPrintInvoice, setIsLoadingPrintInvoice] = useState(false);
  const [isLoadingCancel, setIsLoadingCancel] = useState(false);
  
  // Invoice data
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
  const [supplierInfo, setSupplierInfo] = useState<SupplierInfo | null>(null);

  // POS edit purchase data - fetched at list level
  const [editPurchaseData, setEditPurchaseData] = useState<Purchase | null>(null);

  // View purchase details - with loading state
  const handleViewDetails = async (purchase: Purchase) => {
    
    setIsLoadingViewDetails(true);
    try {
      
      const detailedPurchase = await getPurchase(purchase.uuid);
      
      setSelectedPurchase(detailedPurchase);
      setIsDetailsModalOpen(true);
      toast.success('Purchase details loaded');
      
    } catch (error) {
      toast.error('Failed to load purchase details');
    } finally {
      setIsLoadingViewDetails(false);
    }
  };

  // Edit purchase - fetch data first, then switch to POS view
  const handleEdit = async (purchase: Purchase) => {
    
    setIsLoadingEditPurchase(true);
    try {
      // Fetch the complete purchase data first
      
      const detailedPurchase = await getPurchase(purchase.uuid);
      
      // Load supplier data if needed (similar to POS loadPurchase function)
      if (detailedPurchase.supplierUuidString && user?.warehouseUuid) {
        try {
          
          const suppliersResponse = await filterSuppliers({ warehouseUuid: user.warehouseUuid });
          const supplier = suppliersResponse.data.find((s: any) => s.uuid === detailedPurchase.supplierUuidString);
          if (supplier) {
            (detailedPurchase as any).supplier = supplier;
          } else {
            console.warn('[usePurchaseActions] Supplier not found:', detailedPurchase.supplierUuidString);
          }
        } catch (error) {
          console.error('[usePurchaseActions] Failed to load supplier data:', error);
        }
      }
      
      // Store the fetched data for POS view
      setEditPurchaseData(detailedPurchase);
      
      // Update URL parameters to switch to POS view (no need for purchase UUID in URL)
      updateUrlParams({ 
        view: 'pos' 
      });
      
      toast.success('Purchase loaded for editing');
      
    } catch (error) {
      toast.error('Failed to load purchase for editing');
    } finally {
      setIsLoadingEditPurchase(false);
    }
  };
    
  // Clear edit purchase data when switching away from POS
  const clearEditPurchaseData = () => {
    setEditPurchaseData(null);
  };

  // Print invoice
  const handlePrintInvoice = async (purchase: Purchase) => {
    if (!user?.uuid) {
      toast.error('User information missing');
      return;
    }

    setIsLoadingPrintInvoice(true);
    try {
      
      // Fetch the complete purchase data to ensure we have all tax information
      const detailedPurchase = await getPurchase(purchase.uuid);
      
      // Fetch company information
      
      const companies = await getCompaniesByUser(user.uuid);
      if (companies.length === 0) {
        toast.error('No company information found for this user');
        return;
      }
      const company = companies[0];
      
      // Transform company data to match CompanyInfo interface
      const companyInfo: CompanyInfo = {
        uuid: company.uuid,
        name: company.name,
        nif: company.nif,
        rc: company.rc,
        articleNumber: company.articleNumber,
        address: company.address,
        phoneNumber: company.phoneNumber,
        website: company.website
      };
      
      // Fetch supplier information
      
      const supplier = await getSupplierByUuid(detailedPurchase.supplierUuidString);
      
      // Transform supplier data to match SupplierInfo interface
      const supplierInfo: SupplierInfo = {
        uuid: supplier.uuid,
        name: supplier.name,
        fiscalId: supplier.fiscalId || 'N/A',
        email: supplier.email,
        phone: supplier.phone,
        address: supplier.address,
        rc: supplier.rc,
        articleNumber: supplier.articleNumber,
        supplierType: supplier.supplierType
      };
      
      setCompanyInfo(companyInfo);
      setSupplierInfo(supplierInfo);
      setSelectedPurchase(detailedPurchase);
      setIsInvoiceModalOpen(true);
      
    } catch (error: any) {
      if (error.response?.status === 401) {
        toast.error('Authentication error. Please log in again.');
      } else {
        toast.error('Error loading invoice data');
      }
    } finally {
      setIsLoadingPrintInvoice(false);
    }
  };

  // Cancel purchase - with loading state
  const handleCancel = async (purchase: Purchase) => {
    if (purchase.status === 'cancelled') {
      toast.error('Purchase is already cancelled');
      return;
    }
    
    
    setIsLoadingCancel(true);
    try {
      // Set the purchase to cancel and open modal
    setPurchaseToCancel(purchase);
    setIsCancelModalOpen(true);
      
    } catch (error) {
      toast.error('Failed to open cancel dialog');
    } finally {
      setIsLoadingCancel(false);
    }
  };

  const handleConfirmCancel = async (reason?: string) => {
    if (!purchaseToCancel || !user?.uuid) return;

    setIsCancelling(true);
    try {
      await cancelPurchase(purchaseToCancel.uuid, user.uuid, reason);
      toast.success('Purchase cancelled successfully');
      if (refetchPurchases) {
        refetchPurchases();
      }
      setIsCancelModalOpen(false);
      setPurchaseToCancel(null);
    } catch (error) {
      toast.error('Failed to cancel purchase');
    } finally {
      setIsCancelling(false);
    }
  };

  const handleCancelModalClose = () => {
    setIsCancelModalOpen(false);
    setPurchaseToCancel(null);
  };

  // Close modals
  const closeDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPurchase(null);
  };

  const closeInvoiceModal = () => {
    setIsInvoiceModalOpen(false);
    setSelectedPurchase(null);
    setCompanyInfo(null);
    setSupplierInfo(null);
  };

  return {
    // State
    selectedPurchase,
    isDetailsModalOpen,
    isInvoiceModalOpen,
    isCancelModalOpen,
    purchaseToCancel,
    isLoadingInvoice,
    isCancelling,
    companyInfo,
    supplierInfo,
    editPurchaseData,
    isLoadingEditPurchase,
    isLoadingViewDetails,
    isLoadingPrintInvoice,
    isLoadingCancel,
    
    // Actions
    handleViewDetails,
    handleEdit,
    handlePrintInvoice,
    handleCancel,
    handleConfirmCancel,
    handleCancelModalClose,
    clearEditPurchaseData,
    closeDetailsModal,
    closeInvoiceModal
  };
};
