[{"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\authApi.ts": "1", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\callback\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\DashboardCard.tsx": "4", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\QuickActionButton.tsx": "5", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\RecentActivity.tsx": "6", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\dashboardApi.ts": "7", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\hooks\\useDashboardData.ts": "8", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\layout.tsx": "9", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\page.tsx": "10", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\layout.tsx": "11", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\categories\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\create.tsx": "13", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\page.tsx": "14", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productCategoriesApi.ts": "15", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductForm.tsx": "16", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductModal.tsx": "17", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productsApi.ts": "18", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\[uuid]\\edit.tsx": "19", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\api.ts": "20", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\components\\ProductSelectionModal.tsx": "21", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\page.tsx": "22", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\alerts\\page.tsx": "23", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\levels\\page.tsx": "24", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\movements\\page.tsx": "25", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\transfers\\page.tsx": "26", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\vans\\page.tsx": "27", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\api.ts": "28", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\hooks\\useStockLevelsData.ts": "29", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\layout.tsx": "31", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\layout.tsx": "32", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\api.ts": "33", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\page.tsx": "34", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\RegionForm.tsx": "35", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\api.ts": "36", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\ComputeRouteModal.tsx": "37", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteList.tsx": "38", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteMap.tsx": "39", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\page.tsx": "40", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-loading\\page.tsx": "41", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-stock\\page.tsx": "42", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\page.tsx": "43", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanForm.tsx": "44", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanModal.tsx": "45", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\vansApi.ts": "46", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\warehouses\\page.tsx": "47", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\page.tsx": "48", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\providers.tsx": "49", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\page.tsx": "50", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\goods-receipt\\page.tsx": "51", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\layout.tsx": "52", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\orders\\page.tsx": "53", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\returns\\page.tsx": "54", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierModal.tsx": "55", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\page.tsx": "56", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\suppliersApi.ts": "57", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\financial\\page.tsx": "58", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\inventory\\page.tsx": "59", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\layout.tsx": "60", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\sales\\page.tsx": "61", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\van-performance\\page.tsx": "62", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\index.ts": "63", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\InvoicePrint.tsx": "64", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\CustomerTableActions.tsx": "65", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\index.ts": "66", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\SearchAndFilters.tsx": "67", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerDetailsModal.tsx": "68", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerModal.tsx": "69", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customerPaymentsApi.ts": "70", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customersApi.ts": "71", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\index.ts": "72", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerActions.ts": "73", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerFilters.ts": "74", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\CustomerMap.tsx": "75", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\page.tsx": "76", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\page.tsx": "77", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentDetailsModal.tsx": "78", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentModal.tsx": "79", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\page.tsx": "80", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomerPayments.ts": "81", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomers.ts": "82", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\layout.tsx": "83", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\index.ts": "84", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\ListComponent.tsx": "85", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSComponent.tsx": "86", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSView.tsx": "87", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SaleDetailsModal.tsx": "88", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesCancelModal.tsx": "89", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesFilters.tsx": "90", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesHeader.tsx": "91", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesListView.tsx": "92", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\index.ts": "93", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesActions.ts": "94", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesData.ts": "95", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesPOSState.ts": "96", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\page.tsx": "97", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\CategoryFilter.tsx": "98", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\index.ts": "99", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\NotesControls.tsx": "100", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\Pagination.tsx": "101", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\PaymentSelector.tsx": "102", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\ProductList.tsx": "103", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\QuantityModal.tsx": "104", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SalesCart.tsx": "105", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SearchBar.tsx": "106", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\TaxControls.tsx": "107", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\config\\posConfig.ts": "108", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\index.ts": "109", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\useKeyboardNavigation.ts": "110", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSOperations.ts": "111", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSProducts.ts": "112", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSState.ts": "113", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\posApi.ts": "114", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\styles\\posStyles.ts": "115", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\types\\index.ts": "116", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\utils\\posHelpers.ts": "117", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesApi.ts": "118", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesHelpers.ts": "119", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\accountSettingsApi.ts": "120", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\companiesApi.ts": "121", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\data\\page.tsx": "122", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\layout.tsx": "123", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\profile\\page.tsx": "124", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\page.tsx": "125", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\rolesApi.ts": "126", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\system\\page.tsx": "127", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\AddUserModal.tsx": "128", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\DeleteUserDialog.tsx": "129", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\EditUserModal.tsx": "130", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserDetailsModal.tsx": "131", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserTable.tsx": "132", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\page.tsx": "133", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\usersApi.ts": "134", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\warehousesApi.ts": "135", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\BackendStatusChecker.tsx": "136", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\api\\customerApi.ts": "137", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\CustomerModal.tsx": "138", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\hooks\\useCustomerData.ts": "139", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\index.ts": "140", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\styles\\customerModalStyles.ts": "141", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\types\\index.ts": "142", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\utils\\customerHelpers.ts": "143", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTable.tsx": "144", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableLogic.ts": "145", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableStyles.tsx": "146", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ErrorToast.tsx": "147", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\ItemsTable.tsx": "148", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\LoadingDemo.tsx": "149", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\TableActionButtons.tsx": "150", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\NetworkStatusChecker.tsx": "151", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ProtectedRoute.tsx": "152", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SideTaskBar\\SideTaskBar.tsx": "153", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\TopTaskBar\\TopTaskBar.tsx": "154", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\LogDetailsModal.tsx": "155", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\layout.tsx": "156", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\logsApi.ts": "157", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\page.tsx": "158", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\index.ts": "159", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\UserModal.tsx": "160", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\test-diff\\page.tsx": "161", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\entityDisplayConfig.ts": "162", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\examples\\newEntityExample.ts": "163", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\components\\InvoicePrint.tsx": "164", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\layout.tsx": "165", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\index.ts": "166", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\ListComponent.tsx": "167", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\POSComponent.tsx": "168", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\POSView.tsx": "169", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\PurchaseCancelModal.tsx": "170", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\PurchaseDetailsModal.tsx": "171", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\PurchaseFilters.tsx": "172", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\PurchaseHeader.tsx": "173", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\PurchaseListView.tsx": "174", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\hooks\\index.ts": "175", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\hooks\\usePurchaseActions.ts": "176", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\hooks\\usePurchaseData.ts": "177", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\hooks\\usePurchasePOSState.ts": "178", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\CategoryFilter.tsx": "179", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\index.ts": "180", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\NotesControls.tsx": "181", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\Pagination.tsx": "182", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\PaymentSelector.tsx": "183", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\ProductList.tsx": "184", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\PurchaseCart.tsx": "185", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\QuantityModal.tsx": "186", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\SearchBar.tsx": "187", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\TaxControls.tsx": "188", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\config\\posConfig.ts": "189", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\hooks\\index.ts": "190", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\hooks\\useKeyboardNavigation.ts": "191", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\hooks\\usePOSOperations.ts": "192", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\hooks\\usePOSProducts.ts": "193", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\hooks\\usePOSState.ts": "194", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\posApi.ts": "195", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\styles\\posStyles.ts": "196", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\types\\index.ts": "197", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\utils\\posHelpers.ts": "198", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\purchaseApi.ts": "199", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\purchaseHelpers.ts": "200", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\index.ts": "201", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SearchAndFilters.tsx": "202", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierTableActions.tsx": "203", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\hooks\\index.ts": "204", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\hooks\\useSupplierActions.ts": "205", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\hooks\\useSupplierFilters.ts": "206", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\useSuppliers.ts": "207", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\api\\supplierApi.ts": "208", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\hooks\\useSupplierData.ts": "209", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\index.ts": "210", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\styles\\supplierModalStyles.ts": "211", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\SupplierModal.tsx": "212", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\types.ts": "213", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\utils\\supplierHelpers.ts": "214"}, {"size": 5455, "mtime": 1753726786583, "results": "215", "hashOfConfig": "216"}, {"size": 17268, "mtime": 1753559813872, "results": "217", "hashOfConfig": "216"}, {"size": 16195, "mtime": 1753559619509, "results": "218", "hashOfConfig": "216"}, {"size": 2255, "mtime": 1753197132803, "results": "219", "hashOfConfig": "216"}, {"size": 1442, "mtime": 1753197132803, "results": "220", "hashOfConfig": "216"}, {"size": 5289, "mtime": 1753197132804, "results": "221", "hashOfConfig": "216"}, {"size": 4403, "mtime": 1753726771130, "results": "222", "hashOfConfig": "216"}, {"size": 6103, "mtime": 1753724209627, "results": "223", "hashOfConfig": "216"}, {"size": 515, "mtime": 1753197132805, "results": "224", "hashOfConfig": "216"}, {"size": 8445, "mtime": 1753724102931, "results": "225", "hashOfConfig": "216"}, {"size": 627, "mtime": 1753197132805, "results": "226", "hashOfConfig": "216"}, {"size": 12628, "mtime": 1753436167332, "results": "227", "hashOfConfig": "216"}, {"size": 3128, "mtime": 1753437290519, "results": "228", "hashOfConfig": "216"}, {"size": 21177, "mtime": 1753437289410, "results": "229", "hashOfConfig": "216"}, {"size": 4912, "mtime": 1753197132807, "results": "230", "hashOfConfig": "216"}, {"size": 29276, "mtime": 1753437289458, "results": "231", "hashOfConfig": "216"}, {"size": 1765, "mtime": 1753197132806, "results": "232", "hashOfConfig": "216"}, {"size": 11257, "mtime": 1753437290514, "results": "233", "hashOfConfig": "216"}, {"size": 4763, "mtime": 1753437291482, "results": "234", "hashOfConfig": "216"}, {"size": 4048, "mtime": 1753436167336, "results": "235", "hashOfConfig": "216"}, {"size": 14144, "mtime": 1753436167337, "results": "236", "hashOfConfig": "216"}, {"size": 17067, "mtime": 1753436167337, "results": "237", "hashOfConfig": "216"}, {"size": 265, "mtime": 1753197132809, "results": "238", "hashOfConfig": "216"}, {"size": 254, "mtime": 1753197132809, "results": "239", "hashOfConfig": "216"}, {"size": 261, "mtime": 1753197132810, "results": "240", "hashOfConfig": "216"}, {"size": 263, "mtime": 1753197132810, "results": "241", "hashOfConfig": "216"}, {"size": 246, "mtime": 1753197132810, "results": "242", "hashOfConfig": "216"}, {"size": 7448, "mtime": 1753436167333, "results": "243", "hashOfConfig": "216"}, {"size": 9735, "mtime": 1753436167334, "results": "244", "hashOfConfig": "216"}, {"size": 10114, "mtime": 1753436167335, "results": "245", "hashOfConfig": "216"}, {"size": 711, "mtime": 1753205970784, "results": "246", "hashOfConfig": "216"}, {"size": 627, "mtime": 1753197132811, "results": "247", "hashOfConfig": "216"}, {"size": 2396, "mtime": 1753197132811, "results": "248", "hashOfConfig": "216"}, {"size": 13981, "mtime": 1753197132812, "results": "249", "hashOfConfig": "216"}, {"size": 9041, "mtime": 1753197132811, "results": "250", "hashOfConfig": "216"}, {"size": 3636, "mtime": 1753197132812, "results": "251", "hashOfConfig": "216"}, {"size": 9653, "mtime": 1753197132812, "results": "252", "hashOfConfig": "216"}, {"size": 5407, "mtime": 1753197132813, "results": "253", "hashOfConfig": "216"}, {"size": 17173, "mtime": 1753197132813, "results": "254", "hashOfConfig": "216"}, {"size": 12871, "mtime": 1753197132813, "results": "255", "hashOfConfig": "216"}, {"size": 213, "mtime": 1753197132814, "results": "256", "hashOfConfig": "216"}, {"size": 205, "mtime": 1753197132814, "results": "257", "hashOfConfig": "216"}, {"size": 10247, "mtime": 1753197132815, "results": "258", "hashOfConfig": "216"}, {"size": 7985, "mtime": 1753197132815, "results": "259", "hashOfConfig": "216"}, {"size": 3666, "mtime": 1753197132815, "results": "260", "hashOfConfig": "216"}, {"size": 1953, "mtime": 1753197132815, "results": "261", "hashOfConfig": "216"}, {"size": 12119, "mtime": 1753205023060, "results": "262", "hashOfConfig": "216"}, {"size": 911, "mtime": 1753197132816, "results": "263", "hashOfConfig": "216"}, {"size": 2318, "mtime": 1753205023085, "results": "264", "hashOfConfig": "216"}, {"size": 7607, "mtime": 1753778241763, "results": "265", "hashOfConfig": "216"}, {"size": 258, "mtime": 1753197132822, "results": "266", "hashOfConfig": "216"}, {"size": 628, "mtime": 1753197132822, "results": "267", "hashOfConfig": "216"}, {"size": 504, "mtime": 1753197132822, "results": "268", "hashOfConfig": "216"}, {"size": 267, "mtime": 1753197132823, "results": "269", "hashOfConfig": "216"}, {"size": 8914, "mtime": 1753794849342, "results": "270", "hashOfConfig": "216"}, {"size": 7658, "mtime": 1753794803672, "results": "271", "hashOfConfig": "216"}, {"size": 7112, "mtime": 1753794644445, "results": "272", "hashOfConfig": "216"}, {"size": 237, "mtime": 1753197132825, "results": "273", "hashOfConfig": "216"}, {"size": 237, "mtime": 1753197132825, "results": "274", "hashOfConfig": "216"}, {"size": 625, "mtime": 1753197132825, "results": "275", "hashOfConfig": "216"}, {"size": 231, "mtime": 1753197132825, "results": "276", "hashOfConfig": "216"}, {"size": 229, "mtime": 1753197132826, "results": "277", "hashOfConfig": "216"}, {"size": 58, "mtime": 1753775800357, "results": "278", "hashOfConfig": "216"}, {"size": 14454, "mtime": 1753436167338, "results": "279", "hashOfConfig": "216"}, {"size": 2234, "mtime": 1753197132828, "results": "280", "hashOfConfig": "216"}, {"size": 119, "mtime": 1753197132829, "results": "281", "hashOfConfig": "216"}, {"size": 9699, "mtime": 1753197132829, "results": "282", "hashOfConfig": "216"}, {"size": 13821, "mtime": 1753436167339, "results": "283", "hashOfConfig": "216"}, {"size": 17006, "mtime": 1753436167339, "results": "284", "hashOfConfig": "216"}, {"size": 13816, "mtime": 1753775720323, "results": "285", "hashOfConfig": "216"}, {"size": 12722, "mtime": 1753436167341, "results": "286", "hashOfConfig": "216"}, {"size": 119, "mtime": 1753197132830, "results": "287", "hashOfConfig": "216"}, {"size": 4522, "mtime": 1753197132830, "results": "288", "hashOfConfig": "216"}, {"size": 4625, "mtime": 1753197132831, "results": "289", "hashOfConfig": "216"}, {"size": 11346, "mtime": 1753777606004, "results": "290", "hashOfConfig": "216"}, {"size": 13038, "mtime": 1753436167342, "results": "291", "hashOfConfig": "216"}, {"size": 7315, "mtime": 1753436167343, "results": "292", "hashOfConfig": "216"}, {"size": 16383, "mtime": 1753436167343, "results": "293", "hashOfConfig": "216"}, {"size": 17660, "mtime": 1753197132833, "results": "294", "hashOfConfig": "216"}, {"size": 25184, "mtime": 1753436167344, "results": "295", "hashOfConfig": "216"}, {"size": 10457, "mtime": 1753197132834, "results": "296", "hashOfConfig": "216"}, {"size": 4980, "mtime": 1753197132834, "results": "297", "hashOfConfig": "216"}, {"size": 623, "mtime": 1753197132835, "results": "298", "hashOfConfig": "216"}, {"size": 549, "mtime": 1753777605944, "results": "299", "hashOfConfig": "216"}, {"size": 2466, "mtime": 1753197406252, "results": "300", "hashOfConfig": "216"}, {"size": 803, "mtime": 1753197406253, "results": "301", "hashOfConfig": "216"}, {"size": 22992, "mtime": 1753615162570, "results": "302", "hashOfConfig": "216"}, {"size": 5923, "mtime": 1753775638391, "results": "303", "hashOfConfig": "216"}, {"size": 1655, "mtime": 1753197132838, "results": "304", "hashOfConfig": "216"}, {"size": 12948, "mtime": 1753197132839, "results": "305", "hashOfConfig": "216"}, {"size": 3487, "mtime": 1753197132839, "results": "306", "hashOfConfig": "216"}, {"size": 7391, "mtime": 1753777606959, "results": "307", "hashOfConfig": "216"}, {"size": 182, "mtime": 1753197406254, "results": "308", "hashOfConfig": "216"}, {"size": 8011, "mtime": 1753778026154, "results": "309", "hashOfConfig": "216"}, {"size": 2884, "mtime": 1753436167346, "results": "310", "hashOfConfig": "216"}, {"size": 4058, "mtime": 1753436167347, "results": "311", "hashOfConfig": "216"}, {"size": 7434, "mtime": 1753436167348, "results": "312", "hashOfConfig": "216"}, {"size": 3956, "mtime": 1753197406256, "results": "313", "hashOfConfig": "216"}, {"size": 423, "mtime": 1753197132844, "results": "314", "hashOfConfig": "216"}, {"size": 1733, "mtime": 1753197132842, "results": "315", "hashOfConfig": "216"}, {"size": 5085, "mtime": 1753197406256, "results": "316", "hashOfConfig": "216"}, {"size": 4743, "mtime": 1753777605975, "results": "317", "hashOfConfig": "216"}, {"size": 10866, "mtime": 1753777606993, "results": "318", "hashOfConfig": "216"}, {"size": 5677, "mtime": 1753197132843, "results": "319", "hashOfConfig": "216"}, {"size": 16880, "mtime": 1753777606097, "results": "320", "hashOfConfig": "216"}, {"size": 2813, "mtime": 1753436167350, "results": "321", "hashOfConfig": "216"}, {"size": 2710, "mtime": 1753197406258, "results": "322", "hashOfConfig": "216"}, {"size": 2938, "mtime": 1753197132845, "results": "323", "hashOfConfig": "216"}, {"size": 552, "mtime": 1753197132846, "results": "324", "hashOfConfig": "216"}, {"size": 2266, "mtime": 1753197132846, "results": "325", "hashOfConfig": "216"}, {"size": 8418, "mtime": 1753714639903, "results": "326", "hashOfConfig": "216"}, {"size": 11960, "mtime": 1753197406259, "results": "327", "hashOfConfig": "216"}, {"size": 20473, "mtime": 1753714766175, "results": "328", "hashOfConfig": "216"}, {"size": 19159, "mtime": 1753768203409, "results": "329", "hashOfConfig": "216"}, {"size": 7958, "mtime": 1753197132847, "results": "330", "hashOfConfig": "216"}, {"size": 6849, "mtime": 1753714614257, "results": "331", "hashOfConfig": "216"}, {"size": 7124, "mtime": 1753331631171, "results": "332", "hashOfConfig": "216"}, {"size": 5780, "mtime": 1753777604902, "results": "333", "hashOfConfig": "216"}, {"size": 1374, "mtime": 1753777605914, "results": "334", "hashOfConfig": "216"}, {"size": 3432, "mtime": 1753197132849, "results": "335", "hashOfConfig": "216"}, {"size": 2140, "mtime": 1753777606004, "results": "336", "hashOfConfig": "216"}, {"size": 229, "mtime": 1753197132850, "results": "337", "hashOfConfig": "216"}, {"size": 626, "mtime": 1753197132850, "results": "338", "hashOfConfig": "216"}, {"size": 34646, "mtime": 1753197132851, "results": "339", "hashOfConfig": "216"}, {"size": 9490, "mtime": 1753197132851, "results": "340", "hashOfConfig": "216"}, {"size": 3004, "mtime": 1753197132851, "results": "341", "hashOfConfig": "216"}, {"size": 229, "mtime": 1753197132852, "results": "342", "hashOfConfig": "216"}, {"size": 6132, "mtime": 1753197132852, "results": "343", "hashOfConfig": "216"}, {"size": 1430, "mtime": 1753197132852, "results": "344", "hashOfConfig": "216"}, {"size": 4365, "mtime": 1753197132853, "results": "345", "hashOfConfig": "216"}, {"size": 6114, "mtime": 1753197132853, "results": "346", "hashOfConfig": "216"}, {"size": 7407, "mtime": 1753197132853, "results": "347", "hashOfConfig": "216"}, {"size": 9567, "mtime": 1753197132853, "results": "348", "hashOfConfig": "216"}, {"size": 8420, "mtime": 1753197132854, "results": "349", "hashOfConfig": "216"}, {"size": 5033, "mtime": 1753205022871, "results": "350", "hashOfConfig": "216"}, {"size": 5847, "mtime": 1753197132854, "results": "351", "hashOfConfig": "216"}, {"size": 2615, "mtime": 1753436167355, "results": "352", "hashOfConfig": "216"}, {"size": 17541, "mtime": 1753436167354, "results": "353", "hashOfConfig": "216"}, {"size": 5831, "mtime": 1753197132855, "results": "354", "hashOfConfig": "216"}, {"size": 642, "mtime": 1753197132856, "results": "355", "hashOfConfig": "216"}, {"size": 2513, "mtime": 1753197132856, "results": "356", "hashOfConfig": "216"}, {"size": 1810, "mtime": 1753197406261, "results": "357", "hashOfConfig": "216"}, {"size": 2468, "mtime": 1753197132856, "results": "358", "hashOfConfig": "216"}, {"size": 10624, "mtime": 1753197132859, "results": "359", "hashOfConfig": "216"}, {"size": 3420, "mtime": 1753197132859, "results": "360", "hashOfConfig": "216"}, {"size": 5127, "mtime": 1753197132860, "results": "361", "hashOfConfig": "216"}, {"size": 454, "mtime": 1753197132857, "results": "362", "hashOfConfig": "216"}, {"size": 14634, "mtime": 1753467887894, "results": "363", "hashOfConfig": "216"}, {"size": 11861, "mtime": 1753197132860, "results": "364", "hashOfConfig": "216"}, {"size": 2680, "mtime": 1753197132861, "results": "365", "hashOfConfig": "216"}, {"size": 9245, "mtime": 1753197132857, "results": "366", "hashOfConfig": "216"}, {"size": 737, "mtime": 1753201780688, "results": "367", "hashOfConfig": "216"}, {"size": 14707, "mtime": 1753775728450, "results": "368", "hashOfConfig": "216"}, {"size": 10177, "mtime": 1753436167356, "results": "369", "hashOfConfig": "216"}, {"size": 20657, "mtime": 1753723913759, "results": "370", "hashOfConfig": "216"}, {"size": 519, "mtime": 1753467890059, "results": "371", "hashOfConfig": "216"}, {"size": 5510, "mtime": 1753651530696, "results": "372", "hashOfConfig": "216"}, {"size": 17153, "mtime": 1753723938717, "results": "373", "hashOfConfig": "216"}, {"size": 93, "mtime": 1753467890146, "results": "374", "hashOfConfig": "216"}, {"size": 11130, "mtime": 1753467890115, "results": "375", "hashOfConfig": "216"}, {"size": 6495, "mtime": 1753651321823, "results": "376", "hashOfConfig": "216"}, {"size": 15518, "mtime": 1753723983031, "results": "377", "hashOfConfig": "216"}, {"size": 5736, "mtime": 1753724050562, "results": "378", "hashOfConfig": "216"}, {"size": 6577, "mtime": 1753778742763, "results": "379", "hashOfConfig": "216"}, {"size": 607, "mtime": 1753778974082, "results": "380", "hashOfConfig": "216"}, {"size": 584, "mtime": 1753778537182, "results": "381", "hashOfConfig": "216"}, {"size": 7524, "mtime": 1753778566449, "results": "382", "hashOfConfig": "216"}, {"size": 695, "mtime": 1753782068811, "results": "383", "hashOfConfig": "216"}, {"size": 17261, "mtime": 1753780597274, "results": "384", "hashOfConfig": "216"}, {"size": 3257, "mtime": 1753794625024, "results": "385", "hashOfConfig": "216"}, {"size": 4362, "mtime": 1753778590264, "results": "386", "hashOfConfig": "216"}, {"size": 2598, "mtime": 1753778621389, "results": "387", "hashOfConfig": "216"}, {"size": 3458, "mtime": 1753778430320, "results": "388", "hashOfConfig": "216"}, {"size": 7848, "mtime": 1753778661762, "results": "389", "hashOfConfig": "216"}, {"size": 200, "mtime": 1753778402992, "results": "390", "hashOfConfig": "216"}, {"size": 8431, "mtime": 1753794639151, "results": "391", "hashOfConfig": "216"}, {"size": 2849, "mtime": 1753778315753, "results": "392", "hashOfConfig": "216"}, {"size": 3894, "mtime": 1753782106453, "results": "393", "hashOfConfig": "216"}, {"size": 1692, "mtime": 1753781193155, "results": "394", "hashOfConfig": "216"}, {"size": 472, "mtime": 1753780224203, "results": "395", "hashOfConfig": "216"}, {"size": 2040, "mtime": 1753780204614, "results": "396", "hashOfConfig": "216"}, {"size": 6088, "mtime": 1753780146283, "results": "397", "hashOfConfig": "216"}, {"size": 4630, "mtime": 1753780171733, "results": "398", "hashOfConfig": "216"}, {"size": 6815, "mtime": 1753780721515, "results": "399", "hashOfConfig": "216"}, {"size": 13235, "mtime": 1753780081635, "results": "400", "hashOfConfig": "216"}, {"size": 5996, "mtime": 1753780116795, "results": "401", "hashOfConfig": "216"}, {"size": 2107, "mtime": 1753779987424, "results": "402", "hashOfConfig": "216"}, {"size": 2953, "mtime": 1753780189578, "results": "403", "hashOfConfig": "216"}, {"size": 2891, "mtime": 1753781761560, "results": "404", "hashOfConfig": "216"}, {"size": 267, "mtime": 1753780656038, "results": "405", "hashOfConfig": "216"}, {"size": 3582, "mtime": 1753780636897, "results": "406", "hashOfConfig": "216"}, {"size": 7202, "mtime": 1753794718711, "results": "407", "hashOfConfig": "216"}, {"size": 3844, "mtime": 1753781088899, "results": "408", "hashOfConfig": "216"}, {"size": 12291, "mtime": 1753781869493, "results": "409", "hashOfConfig": "216"}, {"size": 5786, "mtime": 1753778527148, "results": "410", "hashOfConfig": "216"}, {"size": 5262, "mtime": 1753779942091, "results": "411", "hashOfConfig": "216"}, {"size": 4740, "mtime": 1753779910356, "results": "412", "hashOfConfig": "216"}, {"size": 6208, "mtime": 1753779973390, "results": "413", "hashOfConfig": "216"}, {"size": 8223, "mtime": 1753794538193, "results": "414", "hashOfConfig": "216"}, {"size": 1379, "mtime": 1753778291320, "results": "415", "hashOfConfig": "216"}, {"size": 119, "mtime": 1753794767957, "results": "416", "hashOfConfig": "216"}, {"size": 8195, "mtime": 1753794741719, "results": "417", "hashOfConfig": "216"}, {"size": 1561, "mtime": 1753794760912, "results": "418", "hashOfConfig": "216"}, {"size": 119, "mtime": 1753794706175, "results": "419", "hashOfConfig": "216"}, {"size": 4239, "mtime": 1753794700566, "results": "420", "hashOfConfig": "216"}, {"size": 4069, "mtime": 1753794683968, "results": "421", "hashOfConfig": "216"}, {"size": 1886, "mtime": 1753794658723, "results": "422", "hashOfConfig": "216"}, {"size": 2877, "mtime": 1753780363911, "results": "423", "hashOfConfig": "216"}, {"size": 2549, "mtime": 1753780348580, "results": "424", "hashOfConfig": "216"}, {"size": 617, "mtime": 1753780441221, "results": "425", "hashOfConfig": "216"}, {"size": 3915, "mtime": 1753780416899, "results": "426", "hashOfConfig": "216"}, {"size": 15690, "mtime": 1753780329173, "results": "427", "hashOfConfig": "216"}, {"size": 1533, "mtime": 1753780426801, "results": "428", "hashOfConfig": "216"}, {"size": 4401, "mtime": 1753780390671, "results": "429", "hashOfConfig": "216"}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0kge6", {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\authApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\callback\\page.tsx", ["1072"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\page.tsx", [], ["1073"], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\DashboardCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\QuickActionButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\dashboardApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\hooks\\useDashboardData.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\page.tsx", ["1074"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\categories\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\create.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\page.tsx", ["1075"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productCategoriesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductForm.tsx", ["1076", "1077"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\[uuid]\\edit.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\components\\ProductSelectionModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\page.tsx", [], ["1078", "1079"], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\alerts\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\levels\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\movements\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\transfers\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\vans\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\hooks\\useStockLevelsData.ts", ["1080", "1081"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\RegionForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\ComputeRouteModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteMap.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\page.tsx", ["1082"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-loading\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-stock\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\vansApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\warehouses\\page.tsx", ["1083", "1084", "1085"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\providers.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\goods-receipt\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\returns\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\page.tsx", ["1086", "1087"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\suppliersApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\financial\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\inventory\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\sales\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\van-performance\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\InvoicePrint.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\CustomerTableActions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\SearchAndFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customerPaymentsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customersApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerActions.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerFilters.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\CustomerMap.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\page.tsx", ["1088", "1089", "1090", "1091"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomerPayments.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\ListComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSView.tsx", ["1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SaleDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesCancelModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesHeader.tsx", ["1105"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesListView.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesActions.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesData.ts", ["1106", "1107", "1108", "1109"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesPOSState.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\CategoryFilter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\NotesControls.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\PaymentSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\ProductList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\QuantityModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SalesCart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SearchBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\TaxControls.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\config\\posConfig.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\useKeyboardNavigation.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSOperations.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSProducts.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSState.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\posApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\styles\\posStyles.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\utils\\posHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\accountSettingsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\companiesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\data\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\rolesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\system\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\AddUserModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\DeleteUserDialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\EditUserModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserDetailsModal.tsx", ["1110"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\usersApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\warehousesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\BackendStatusChecker.tsx", ["1111"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\api\\customerApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\CustomerModal.tsx", ["1112", "1113", "1114", "1115"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\hooks\\useCustomerData.ts", ["1116", "1117"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\styles\\customerModalStyles.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\utils\\customerHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableLogic.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableStyles.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ErrorToast.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\ItemsTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\LoadingDemo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\TableActionButtons.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\NetworkStatusChecker.tsx", ["1118"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SideTaskBar\\SideTaskBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\TopTaskBar\\TopTaskBar.tsx", ["1119"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\LogDetailsModal.tsx", ["1120", "1121"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\logsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\UserModal.tsx", ["1122", "1123", "1124"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\test-diff\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\entityDisplayConfig.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\examples\\newEntityExample.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\components\\InvoicePrint.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\ListComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\POSComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\POSView.tsx", ["1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\PurchaseCancelModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\PurchaseDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\PurchaseFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\PurchaseHeader.tsx", ["1138"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\components\\PurchaseListView.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\hooks\\usePurchaseActions.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\hooks\\usePurchaseData.ts", ["1139", "1140", "1141", "1142"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\hooks\\usePurchasePOSState.ts", ["1143"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\CategoryFilter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\NotesControls.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\PaymentSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\ProductList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\PurchaseCart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\QuantityModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\SearchBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\components\\TaxControls.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\config\\posConfig.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\hooks\\useKeyboardNavigation.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\hooks\\usePOSOperations.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\hooks\\usePOSProducts.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\hooks\\usePOSState.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\posApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\styles\\posStyles.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\pos\\utils\\posHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\purchaseApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\purchase\\purchaseHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SearchAndFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierTableActions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\hooks\\useSupplierActions.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\hooks\\useSupplierFilters.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\useSuppliers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\api\\supplierApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\hooks\\useSupplierData.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\styles\\supplierModalStyles.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\SupplierModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SupplierModal\\utils\\supplierHelpers.ts", [], [], {"ruleId": "1144", "severity": 1, "message": "1145", "line": 255, "column": 6, "nodeType": "1146", "endLine": 255, "endColumn": 8, "suggestions": "1147"}, {"ruleId": "1144", "severity": 1, "message": "1148", "line": 78, "column": 6, "nodeType": "1146", "endLine": 78, "endColumn": 29, "suggestions": "1149", "suppressions": "1150"}, {"ruleId": "1151", "severity": 2, "message": "1152", "line": 100, "column": 50, "nodeType": "1153", "messageId": "1154", "suggestions": "1155"}, {"ruleId": "1144", "severity": 1, "message": "1156", "line": 228, "column": 6, "nodeType": "1146", "endLine": 228, "endColumn": 55, "suggestions": "1157"}, {"ruleId": "1144", "severity": 1, "message": "1158", "line": 433, "column": 6, "nodeType": "1146", "endLine": 433, "endColumn": 54, "suggestions": "1159"}, {"ruleId": "1144", "severity": 1, "message": "1160", "line": 433, "column": 7, "nodeType": "1161", "endLine": 433, "endColumn": 21}, {"ruleId": "1144", "severity": 1, "message": "1162", "line": 152, "column": 6, "nodeType": "1146", "endLine": 152, "endColumn": 66, "suggestions": "1163", "suppressions": "1164"}, {"ruleId": "1144", "severity": 1, "message": "1165", "line": 152, "column": 20, "nodeType": "1161", "endLine": 152, "endColumn": 65, "suppressions": "1166"}, {"ruleId": "1144", "severity": 1, "message": "1167", "line": 57, "column": 48, "nodeType": "1146", "endLine": 57, "endColumn": 62, "suggestions": "1168"}, {"ruleId": "1144", "severity": 1, "message": "1169", "line": 58, "column": 56, "nodeType": "1146", "endLine": 58, "endColumn": 74, "suggestions": "1170"}, {"ruleId": "1144", "severity": 1, "message": "1171", "line": 48, "column": 6, "nodeType": "1146", "endLine": 48, "endColumn": 21, "suggestions": "1172"}, {"ruleId": "1144", "severity": 1, "message": "1173", "line": 185, "column": 6, "nodeType": "1146", "endLine": 185, "endColumn": 16, "suggestions": "1174"}, {"ruleId": "1151", "severity": 2, "message": "1175", "line": 348, "column": 42, "nodeType": "1153", "messageId": "1154", "suggestions": "1176"}, {"ruleId": "1151", "severity": 2, "message": "1175", "line": 348, "column": 56, "nodeType": "1153", "messageId": "1154", "suggestions": "1177"}, {"ruleId": "1151", "severity": 2, "message": "1175", "line": 209, "column": 47, "nodeType": "1153", "messageId": "1154", "suggestions": "1178"}, {"ruleId": "1151", "severity": 2, "message": "1175", "line": 209, "column": 79, "nodeType": "1153", "messageId": "1154", "suggestions": "1179"}, {"ruleId": "1144", "severity": 1, "message": "1180", "line": 400, "column": 6, "nodeType": "1146", "endLine": 400, "endColumn": 8, "suggestions": "1181"}, {"ruleId": "1144", "severity": 1, "message": "1182", "line": 447, "column": 6, "nodeType": "1146", "endLine": 447, "endColumn": 39, "suggestions": "1183"}, {"ruleId": "1144", "severity": 1, "message": "1182", "line": 469, "column": 6, "nodeType": "1146", "endLine": 469, "endColumn": 39, "suggestions": "1184"}, {"ruleId": "1144", "severity": 1, "message": "1182", "line": 483, "column": 6, "nodeType": "1146", "endLine": 483, "endColumn": 29, "suggestions": "1185"}, {"ruleId": "1186", "severity": 2, "message": "1187", "line": 207, "column": 24, "nodeType": "1188", "endLine": 207, "endColumn": 35}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 271, "column": 3, "nodeType": "1188", "endLine": 271, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 280, "column": 3, "nodeType": "1188", "endLine": 280, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 285, "column": 3, "nodeType": "1188", "endLine": 285, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 290, "column": 3, "nodeType": "1188", "endLine": 290, "endColumn": 12}, {"ruleId": "1144", "severity": 1, "message": "1190", "line": 359, "column": 6, "nodeType": "1146", "endLine": 359, "endColumn": 144, "suggestions": "1191"}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 362, "column": 3, "nodeType": "1188", "endLine": 362, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 399, "column": 3, "nodeType": "1188", "endLine": 399, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1187", "line": 422, "column": 25, "nodeType": "1188", "endLine": 422, "endColumn": 36}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 440, "column": 3, "nodeType": "1188", "endLine": 440, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 452, "column": 3, "nodeType": "1188", "endLine": 452, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 483, "column": 3, "nodeType": "1188", "endLine": 483, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 491, "column": 3, "nodeType": "1188", "endLine": 491, "endColumn": 12}, {"ruleId": "1144", "severity": 1, "message": "1192", "line": 51, "column": 6, "nodeType": "1146", "endLine": 51, "endColumn": 17, "suggestions": "1193"}, {"ruleId": "1144", "severity": 1, "message": "1167", "line": 16, "column": 48, "nodeType": "1146", "endLine": 16, "endColumn": 72, "suggestions": "1194"}, {"ruleId": "1144", "severity": 1, "message": "1195", "line": 16, "column": 49, "nodeType": "1161", "endLine": 16, "endColumn": 71}, {"ruleId": "1144", "severity": 1, "message": "1169", "line": 17, "column": 56, "nodeType": "1146", "endLine": 17, "endColumn": 84, "suggestions": "1196"}, {"ruleId": "1144", "severity": 1, "message": "1195", "line": 17, "column": 57, "nodeType": "1161", "endLine": 17, "endColumn": 83}, {"ruleId": "1197", "severity": 1, "message": "1198", "line": 127, "column": 22, "nodeType": "1199", "endLine": 131, "endColumn": 24}, {"ruleId": "1144", "severity": 1, "message": "1200", "line": 85, "column": 6, "nodeType": "1146", "endLine": 85, "endColumn": 8, "suggestions": "1201"}, {"ruleId": "1144", "severity": 1, "message": "1202", "line": 71, "column": 6, "nodeType": "1146", "endLine": 71, "endColumn": 14, "suggestions": "1203"}, {"ruleId": "1144", "severity": 1, "message": "1204", "line": 97, "column": 6, "nodeType": "1146", "endLine": 97, "endColumn": 44, "suggestions": "1205"}, {"ruleId": "1151", "severity": 2, "message": "1175", "line": 272, "column": 86, "nodeType": "1153", "messageId": "1154", "suggestions": "1206"}, {"ruleId": "1151", "severity": 2, "message": "1175", "line": 272, "column": 99, "nodeType": "1153", "messageId": "1154", "suggestions": "1207"}, {"ruleId": "1144", "severity": 1, "message": "1208", "line": 51, "column": 6, "nodeType": "1146", "endLine": 51, "endColumn": 8, "suggestions": "1209"}, {"ruleId": "1144", "severity": 1, "message": "1210", "line": 150, "column": 40, "nodeType": "1188", "endLine": 150, "endColumn": 47}, {"ruleId": "1144", "severity": 1, "message": "1211", "line": 136, "column": 6, "nodeType": "1146", "endLine": 136, "endColumn": 8, "suggestions": "1212"}, {"ruleId": "1144", "severity": 1, "message": "1213", "line": 83, "column": 6, "nodeType": "1146", "endLine": 83, "endColumn": 60, "suggestions": "1214"}, {"ruleId": "1151", "severity": 2, "message": "1175", "line": 245, "column": 46, "nodeType": "1153", "messageId": "1154", "suggestions": "1215"}, {"ruleId": "1151", "severity": 2, "message": "1175", "line": 245, "column": 54, "nodeType": "1153", "messageId": "1154", "suggestions": "1216"}, {"ruleId": "1144", "severity": 1, "message": "1204", "line": 108, "column": 6, "nodeType": "1146", "endLine": 108, "endColumn": 49, "suggestions": "1217"}, {"ruleId": "1151", "severity": 2, "message": "1175", "line": 265, "column": 78, "nodeType": "1153", "messageId": "1154", "suggestions": "1218"}, {"ruleId": "1151", "severity": 2, "message": "1175", "line": 265, "column": 91, "nodeType": "1153", "messageId": "1154", "suggestions": "1219"}, {"ruleId": "1144", "severity": 1, "message": "1220", "line": 94, "column": 9, "nodeType": "1221", "endLine": 94, "endColumn": 44}, {"ruleId": "1186", "severity": 2, "message": "1222", "line": 123, "column": 7, "nodeType": "1188", "endLine": 123, "endColumn": 28}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 132, "column": 3, "nodeType": "1188", "endLine": 132, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 140, "column": 3, "nodeType": "1188", "endLine": 140, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 197, "column": 3, "nodeType": "1188", "endLine": 197, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1187", "line": 206, "column": 31, "nodeType": "1188", "endLine": 206, "endColumn": 42}, {"ruleId": "1186", "severity": 2, "message": "1187", "line": 223, "column": 32, "nodeType": "1188", "endLine": 223, "endColumn": 43}, {"ruleId": "1186", "severity": 2, "message": "1187", "line": 232, "column": 32, "nodeType": "1188", "endLine": 232, "endColumn": 43}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 271, "column": 3, "nodeType": "1188", "endLine": 271, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 280, "column": 3, "nodeType": "1188", "endLine": 280, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1187", "line": 285, "column": 24, "nodeType": "1188", "endLine": 285, "endColumn": 35}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 336, "column": 3, "nodeType": "1188", "endLine": 336, "endColumn": 12}, {"ruleId": "1186", "severity": 2, "message": "1189", "line": 349, "column": 3, "nodeType": "1188", "endLine": 349, "endColumn": 12}, {"ruleId": "1144", "severity": 1, "message": "1223", "line": 51, "column": 6, "nodeType": "1146", "endLine": 51, "endColumn": 21, "suggestions": "1224"}, {"ruleId": "1144", "severity": 1, "message": "1167", "line": 16, "column": 48, "nodeType": "1146", "endLine": 16, "endColumn": 72, "suggestions": "1225"}, {"ruleId": "1144", "severity": 1, "message": "1195", "line": 16, "column": 49, "nodeType": "1161", "endLine": 16, "endColumn": 71}, {"ruleId": "1144", "severity": 1, "message": "1169", "line": 17, "column": 56, "nodeType": "1146", "endLine": 17, "endColumn": 84, "suggestions": "1226"}, {"ruleId": "1144", "severity": 1, "message": "1195", "line": 17, "column": 57, "nodeType": "1161", "endLine": 17, "endColumn": 83}, {"ruleId": "1144", "severity": 1, "message": "1227", "line": 72, "column": 7, "nodeType": "1146", "endLine": 117, "endColumn": 4, "suggestions": "1228"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'clearAllTimeouts', 'handleAuth', and 'router'. Either include them or remove the dependency array.", "ArrayExpression", ["1229"], "React Hook useEffect has missing dependencies: 'checkUserExists' and 'logout'. Either include them or remove the dependency array.", ["1230"], ["1231"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1232", "1233", "1234", "1235"], "React Hook React.useEffect has a missing dependency: 'previousPaginationData'. Either include it or remove the dependency array.", ["1236"], "React Hook React.useEffect has a missing dependency: 'watch'. Either include it or remove the dependency array.", ["1237"], "React Hook React.useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "React Hook useEffect has missing dependencies: 'adjustments' and 'setValue'. Either include them or remove the dependency array.", ["1238"], ["1239"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", ["1240"], "React Hook useMemo has a missing dependency: 'filter'. Either include it or remove the dependency array.", ["1241"], "React Hook useMemo has a missing dependency: 'pagination'. Either include it or remove the dependency array.", ["1242"], "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1243"], "React Hook useEffect has a missing dependency: 'loadWarehouses'. Either include it or remove the dependency array.", ["1244"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["1245", "1246", "1247", "1248"], ["1249", "1250", "1251", "1252"], ["1253", "1254", "1255", "1256"], ["1257", "1258", "1259", "1260"], "React Hook useEffect has a missing dependency: 'handleAddPayment'. Either include it or remove the dependency array.", ["1261"], "React Hook useCallback has a missing dependency: 'actions'. Either include it or remove the dependency array.", ["1262"], ["1263"], ["1264"], "react-hooks/rules-of-hooks", "React Hook \"useCallback\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "Identifier", "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "React Hook useEffect has a missing dependency: 'loadSale'. Either include it or remove the dependency array.", ["1265"], "React Hook useEffect has a missing dependency: 'handleNewSale'. Either include it or remove the dependency array.", ["1266"], ["1267"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", ["1268"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has missing dependencies: 'checkBackendStatus' and 'checkTimeout'. Either include them or remove the dependency array.", ["1269"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["1270"], "React Hook useEffect has a missing dependency: 'debouncedSearchTerm'. Either include it or remove the dependency array.", ["1271"], ["1272", "1273", "1274", "1275"], ["1276", "1277", "1278", "1279"], "React Hook useCallback has a missing dependency: 'CACHE_EXPIRY'. Either include it or remove the dependency array.", ["1280"], "The ref value 'requestTimeoutRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'requestTimeoutRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has missing dependencies: 'checkNetworkStatus' and 'checkTimeout'. Either include them or remove the dependency array.", ["1281"], "React Hook React.useEffect has missing dependencies: 'fetchAndPersistWarehouseInfo' and 'user'. Either include them or remove the dependency array.", ["1282"], ["1283", "1284", "1285", "1286"], ["1287", "1288", "1289", "1290"], ["1291"], ["1292", "1293", "1294", "1295"], ["1296", "1297", "1298", "1299"], "The 'products' logical expression could make the dependencies of useEffect Hook (at line 282) change on every render. To fix this, wrap the initialization of 'products' in its own useMemo() Hook.", "VariableDeclarator", "React Hook \"useKeyboardNavigation\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "React Hook useEffect has a missing dependency: 'handleNewPurchase'. Either include it or remove the dependency array.", ["1300"], ["1301"], ["1302"], "React Hook useMemo has missing dependencies: 'posProducts.categories', 'posProducts.currentPage', 'posProducts.hasNext', 'posProducts.hasPrev', and 'posProducts.totalPages'. Either include them or remove the dependency array.", ["1303"], {"desc": "1304", "fix": "1305"}, {"desc": "1306", "fix": "1307"}, {"kind": "1308", "justification": "1309"}, {"messageId": "1310", "data": "1311", "fix": "1312", "desc": "1313"}, {"messageId": "1310", "data": "1314", "fix": "1315", "desc": "1316"}, {"messageId": "1310", "data": "1317", "fix": "1318", "desc": "1319"}, {"messageId": "1310", "data": "1320", "fix": "1321", "desc": "1322"}, {"desc": "1323", "fix": "1324"}, {"desc": "1325", "fix": "1326"}, {"desc": "1327", "fix": "1328"}, {"kind": "1308", "justification": "1309"}, {"kind": "1308", "justification": "1309"}, {"desc": "1329", "fix": "1330"}, {"desc": "1331", "fix": "1332"}, {"desc": "1333", "fix": "1334"}, {"desc": "1335", "fix": "1336"}, {"messageId": "1310", "data": "1337", "fix": "1338", "desc": "1339"}, {"messageId": "1310", "data": "1340", "fix": "1341", "desc": "1342"}, {"messageId": "1310", "data": "1343", "fix": "1344", "desc": "1345"}, {"messageId": "1310", "data": "1346", "fix": "1347", "desc": "1348"}, {"messageId": "1310", "data": "1349", "fix": "1350", "desc": "1339"}, {"messageId": "1310", "data": "1351", "fix": "1352", "desc": "1342"}, {"messageId": "1310", "data": "1353", "fix": "1354", "desc": "1345"}, {"messageId": "1310", "data": "1355", "fix": "1356", "desc": "1348"}, {"messageId": "1310", "data": "1357", "fix": "1358", "desc": "1339"}, {"messageId": "1310", "data": "1359", "fix": "1360", "desc": "1342"}, {"messageId": "1310", "data": "1361", "fix": "1362", "desc": "1345"}, {"messageId": "1310", "data": "1363", "fix": "1364", "desc": "1348"}, {"messageId": "1310", "data": "1365", "fix": "1366", "desc": "1339"}, {"messageId": "1310", "data": "1367", "fix": "1368", "desc": "1342"}, {"messageId": "1310", "data": "1369", "fix": "1370", "desc": "1345"}, {"messageId": "1310", "data": "1371", "fix": "1372", "desc": "1348"}, {"desc": "1373", "fix": "1374"}, {"desc": "1375", "fix": "1376"}, {"desc": "1375", "fix": "1377"}, {"desc": "1378", "fix": "1379"}, {"desc": "1380", "fix": "1381"}, {"desc": "1382", "fix": "1383"}, {"desc": "1329", "fix": "1384"}, {"desc": "1331", "fix": "1385"}, {"desc": "1386", "fix": "1387"}, {"desc": "1388", "fix": "1389"}, {"desc": "1390", "fix": "1391"}, {"messageId": "1310", "data": "1392", "fix": "1393", "desc": "1339"}, {"messageId": "1310", "data": "1394", "fix": "1395", "desc": "1342"}, {"messageId": "1310", "data": "1396", "fix": "1397", "desc": "1345"}, {"messageId": "1310", "data": "1398", "fix": "1399", "desc": "1348"}, {"messageId": "1310", "data": "1400", "fix": "1401", "desc": "1339"}, {"messageId": "1310", "data": "1402", "fix": "1403", "desc": "1342"}, {"messageId": "1310", "data": "1404", "fix": "1405", "desc": "1345"}, {"messageId": "1310", "data": "1406", "fix": "1407", "desc": "1348"}, {"desc": "1408", "fix": "1409"}, {"desc": "1410", "fix": "1411"}, {"desc": "1412", "fix": "1413"}, {"messageId": "1310", "data": "1414", "fix": "1415", "desc": "1339"}, {"messageId": "1310", "data": "1416", "fix": "1417", "desc": "1342"}, {"messageId": "1310", "data": "1418", "fix": "1419", "desc": "1345"}, {"messageId": "1310", "data": "1420", "fix": "1421", "desc": "1348"}, {"messageId": "1310", "data": "1422", "fix": "1423", "desc": "1339"}, {"messageId": "1310", "data": "1424", "fix": "1425", "desc": "1342"}, {"messageId": "1310", "data": "1426", "fix": "1427", "desc": "1345"}, {"messageId": "1310", "data": "1428", "fix": "1429", "desc": "1348"}, {"desc": "1430", "fix": "1431"}, {"messageId": "1310", "data": "1432", "fix": "1433", "desc": "1339"}, {"messageId": "1310", "data": "1434", "fix": "1435", "desc": "1342"}, {"messageId": "1310", "data": "1436", "fix": "1437", "desc": "1345"}, {"messageId": "1310", "data": "1438", "fix": "1439", "desc": "1348"}, {"messageId": "1310", "data": "1440", "fix": "1441", "desc": "1339"}, {"messageId": "1310", "data": "1442", "fix": "1443", "desc": "1342"}, {"messageId": "1310", "data": "1444", "fix": "1445", "desc": "1345"}, {"messageId": "1310", "data": "1446", "fix": "1447", "desc": "1348"}, {"desc": "1448", "fix": "1449"}, {"desc": "1329", "fix": "1450"}, {"desc": "1331", "fix": "1451"}, {"desc": "1452", "fix": "1453"}, "Update the dependencies array to be: [clearAllTimeouts, handleAuth, router]", {"range": "1454", "text": "1455"}, "Update the dependencies array to be: [router, user, loading, checkUserExists, logout]", {"range": "1456", "text": "1457"}, "directive", "", "replaceWithAlt", {"alt": "1458"}, {"range": "1459", "text": "1460"}, "Replace with `&apos;`.", {"alt": "1461"}, {"range": "1462", "text": "1463"}, "Replace with `&lsquo;`.", {"alt": "1464"}, {"range": "1465", "text": "1466"}, "Replace with `&#39;`.", {"alt": "1467"}, {"range": "1468", "text": "1469"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [paginatedData, isLoading, currentPage, pageSize, previousPaginationData]", {"range": "1470", "text": "1471"}, "Update the dependencies array to be: [showAdditionalPrices, setValue, watch]", {"range": "1472", "text": "1473"}, "Update the dependencies array to be: [adjustments, setValue, storageUuid]", {"range": "1474", "text": "1475"}, "Update the dependencies array to be: [filter]", {"range": "1476", "text": "1477"}, "Update the dependencies array to be: [pagination]", {"range": "1478", "text": "1479"}, "Update the dependencies array to be: [loadInitialData, warehouseUuid]", {"range": "1480", "text": "1481"}, "Update the dependencies array to be: [loadWarehouses, userUuid]", {"range": "1482", "text": "1483"}, {"alt": "1484"}, {"range": "1485", "text": "1486"}, "Replace with `&quot;`.", {"alt": "1487"}, {"range": "1488", "text": "1489"}, "Replace with `&ldquo;`.", {"alt": "1490"}, {"range": "1491", "text": "1492"}, "Replace with `&#34;`.", {"alt": "1493"}, {"range": "1494", "text": "1495"}, "Replace with `&rdquo;`.", {"alt": "1484"}, {"range": "1496", "text": "1497"}, {"alt": "1487"}, {"range": "1498", "text": "1499"}, {"alt": "1490"}, {"range": "1500", "text": "1501"}, {"alt": "1493"}, {"range": "1502", "text": "1503"}, {"alt": "1484"}, {"range": "1504", "text": "1505"}, {"alt": "1487"}, {"range": "1506", "text": "1507"}, {"alt": "1490"}, {"range": "1508", "text": "1509"}, {"alt": "1493"}, {"range": "1510", "text": "1511"}, {"alt": "1484"}, {"range": "1512", "text": "1513"}, {"alt": "1487"}, {"range": "1514", "text": "1515"}, {"alt": "1490"}, {"range": "1516", "text": "1517"}, {"alt": "1493"}, {"range": "1518", "text": "1519"}, "Update the dependencies array to be: [handleAddPayment]", {"range": "1520", "text": "1521"}, "Update the dependencies array to be: [actions, userUuid]", {"range": "1522", "text": "1523"}, {"range": "1524", "text": "1523"}, "Update the dependencies array to be: [actions]", {"range": "1525", "text": "1526"}, "Update the dependencies array to be: [editSaleData, loadSaleUuid, warehouseUuid, isEditMode, isLoadMode, saleState, loadSaleForEdit, loadSaleForContinue, setError, setLoading, loadSale]", {"range": "1527", "text": "1528"}, "Update the dependencies array to be: [handleNewSale, onNewSale]", {"range": "1529", "text": "1530"}, {"range": "1531", "text": "1477"}, {"range": "1532", "text": "1479"}, "Update the dependencies array to be: [checkBackendStatus, checkTimeout]", {"range": "1533", "text": "1534"}, "Update the dependencies array to be: [handleClose, isOpen]", {"range": "1535", "text": "1536"}, "Update the dependencies array to be: [isOpen, warehouseUuid, loadCustomers, debouncedSearchTerm]", {"range": "1537", "text": "1538"}, {"alt": "1484"}, {"range": "1539", "text": "1540"}, {"alt": "1487"}, {"range": "1541", "text": "1542"}, {"alt": "1490"}, {"range": "1543", "text": "1544"}, {"alt": "1493"}, {"range": "1545", "text": "1546"}, {"alt": "1484"}, {"range": "1547", "text": "1484"}, {"alt": "1487"}, {"range": "1548", "text": "1487"}, {"alt": "1490"}, {"range": "1549", "text": "1490"}, {"alt": "1493"}, {"range": "1550", "text": "1493"}, "Update the dependencies array to be: [CACHE_EXPIRY]", {"range": "1551", "text": "1552"}, "Update the dependencies array to be: [checkNetworkStatus, checkTimeout]", {"range": "1553", "text": "1554"}, "Update the dependencies array to be: [user?.uuid, user.warehouseUuid, user.warehouseName, user, fetchAndPersistWarehouseInfo]", {"range": "1555", "text": "1556"}, {"alt": "1484"}, {"range": "1557", "text": "1484"}, {"alt": "1487"}, {"range": "1558", "text": "1487"}, {"alt": "1490"}, {"range": "1559", "text": "1490"}, {"alt": "1493"}, {"range": "1560", "text": "1493"}, {"alt": "1484"}, {"range": "1561", "text": "1484"}, {"alt": "1487"}, {"range": "1562", "text": "1487"}, {"alt": "1490"}, {"range": "1563", "text": "1490"}, {"alt": "1493"}, {"range": "1564", "text": "1493"}, "Update the dependencies array to be: [isOpen, effectiveWarehouseUuid, loadUsers, debouncedSearchTerm]", {"range": "1565", "text": "1566"}, {"alt": "1484"}, {"range": "1567", "text": "1568"}, {"alt": "1487"}, {"range": "1569", "text": "1570"}, {"alt": "1490"}, {"range": "1571", "text": "1572"}, {"alt": "1493"}, {"range": "1573", "text": "1574"}, {"alt": "1484"}, {"range": "1575", "text": "1484"}, {"alt": "1487"}, {"range": "1576", "text": "1487"}, {"alt": "1490"}, {"range": "1577", "text": "1490"}, {"alt": "1493"}, {"range": "1578", "text": "1493"}, "Update the dependencies array to be: [handleNewPurchase, onNewPurchase]", {"range": "1579", "text": "1580"}, {"range": "1581", "text": "1477"}, {"range": "1582", "text": "1479"}, "Update the dependencies array to be: [posState.purchaseState, posState.startNewPurchase, posState.loadPurchaseForEdit, posState.loadPurchaseForContinue, posState.clearPurchase, posState.setSupplier, posState.addItem, posState.removeItem, posState.updateItemQuantity, posState.updateItemPrice, posState.clearItems, posState.setTaxEnabled, posState.setTaxRate, posState.recalculateTotals, posState.setPaymentMethod, posState.setAmountPaid, posState.setNotes, posState.setNotesEnabled, posState.setError, posState.setSubmitting, posState.setLoading, posState.markClean, posState.validatePurchase, posOperations.savePurchase, posOperations.loadPurchase, posOperations.createNewSupplier, posOperations.getDefaultSupplier, posOperations.searchSuppliers, posOperations.validateStock, posProducts.products, posProducts.isLoading, posProducts.currentPage, posProducts.totalPages, posProducts.hasNext, posProducts.hasPrev, posProducts.loadPage, posProducts.loadProducts, posProducts.updateCategory, posProducts.currentCategory, posProducts.categories, warehouseUuid]", {"range": "1583", "text": "1584"}, [10498, 10500], "[clearAllTimeouts, handleAuth, router]", [3802, 3825], "[router, user, loading, checkUserExists, logout]", "&apos;", [3109, 3138], "! Here&apos;s an overview of your ", "&lsquo;", [3109, 3138], "! Here&lsquo;s an overview of your ", "&#39;", [3109, 3138], "! Here&#39;s an overview of your ", "&rsquo;", [3109, 3138], "! Here&rsquo;s an overview of your ", [9126, 9175], "[paginatedData, isLoading, currentPage, pageSize, previousPaginationData]", [17880, 17928], "[showAdditionalPrices, setValue, watch]", [5237, 5297], "[adjustments, setValue, storageUuid]", [1716, 1730], "[filter]", [1789, 1807], "[pagination]", [1692, 1707], "[loadInitialData, warehouseUuid]", [6787, 6797], "[loadWarehouses, userUuid]", "&quot;", [11603, 11705], "\n              No warehouses found. Click &quot;Add Warehouse\" to create your first warehouse.\n            ", "&ldquo;", [11603, 11705], "\n              No warehouses found. Click &ldquo;Add Warehouse\" to create your first warehouse.\n            ", "&#34;", [11603, 11705], "\n              No warehouses found. Click &#34;Add Warehouse\" to create your first warehouse.\n            ", "&rdquo;", [11603, 11705], "\n              No warehouses found. Click &rdquo;Add Warehouse\" to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&quot; to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&ldquo; to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&#34; to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&rdquo; to create your first warehouse.\n            ", [6771, 6820], "\r\n              Are you sure you want to delete &quot;", [6771, 6820], "\r\n              Are you sure you want to delete &ldquo;", [6771, 6820], "\r\n              Are you sure you want to delete &#34;", [6771, 6820], "\r\n              Are you sure you want to delete &rdquo;", [6851, 6897], "&quot;? This action cannot be undone.\r\n            ", [6851, 6897], "&ldquo;? This action cannot be undone.\r\n            ", [6851, 6897], "&#34;? This action cannot be undone.\r\n            ", [6851, 6897], "&rdquo;? This action cannot be undone.\r\n            ", [13806, 13808], "[handleAddPayment]", [15158, 15191], "[actions, userUuid]", [15855, 15888], [16405, 16428], "[actions]", [12933, 13071], "[editSaleData, loadSaleUuid, warehouseUuid, isEditMode, isLoadMode, saleState, loadSaleForEdit, loadSaleForContinue, setError, setLoading, loadSale]", [1358, 1369], "[handleNewSale, onNewSale]", [705, 729], [788, 816], [2758, 2760], "[checkBackendStatus, checkTimeout]", [2182, 2190], "[handleClose, isOpen]", [2880, 2918], "[isOpen, warehouseUuid, loadCustomers, debouncedSearchTerm]", [9605, 9634], "No customers found matching &quot;", [9605, 9634], "No customers found matching &ldquo;", [9605, 9634], "No customers found matching &#34;", [9605, 9634], "No customers found matching &rdquo;", [9646, 9647], [9646, 9647], [9646, 9647], [9646, 9647], [2007, 2009], "[CACHE_EXPIRY]", [4713, 4715], "[checkNetworkStatus, checkTimeout]", [2945, 2999], "[user?.uuid, user.warehouseUuid, user.warehouseName, user, fetchAndPersistWarehouseInfo]", [9326, 9327], [9326, 9327], [9326, 9327], [9326, 9327], [9334, 9335], [9334, 9335], [9334, 9335], [9334, 9335], [3576, 3619], "[isOpen, effectiveWarehouseUuid, loadUsers, debouncedSearchTerm]", [9420, 9445], "No users found matching &quot;", [9420, 9445], "No users found matching &ldquo;", [9420, 9445], "No users found matching &#34;", [9420, 9445], "No users found matching &rdquo;", [9457, 9458], [9457, 9458], [9457, 9458], [9457, 9458], [1348, 1363], "[handleNewPurchase, onNewPurchase]", [711, 735], [793, 821], [2547, 3889], "[posState.purchaseState, posState.startNewPurchase, posState.loadPurchaseForEdit, posState.loadPurchaseForContinue, posState.clearPurchase, posState.setSupplier, posState.addItem, posState.removeItem, posState.updateItemQuantity, posState.updateItemPrice, posState.clearItems, posState.setTaxEnabled, posState.setTaxRate, posState.recalculateTotals, posState.setPaymentMethod, posState.setAmountPaid, posState.setNotes, posState.setNotesEnabled, posState.setError, posState.setSubmitting, posState.setLoading, posState.markClean, posState.validatePurchase, posOperations.savePurchase, posOperations.loadPurchase, posOperations.createNewSupplier, posOperations.getDefaultSupplier, posOperations.searchSuppliers, posOperations.validateStock, posProducts.products, posProducts.isLoading, posProducts.currentPage, posProducts.totalPages, posProducts.hasNext, posProducts.hasPrev, posProducts.loadPage, posProducts.loadProducts, posProducts.updateCategory, posProducts.currentCategory, posProducts.categories, warehouseUuid]"]