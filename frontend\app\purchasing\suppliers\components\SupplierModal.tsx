import React, { useEffect, useRef } from 'react';
import { CreateSupplierDto, UpdateSupplierDto, Supplier } from '../suppliersApi';

interface SupplierModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (values: CreateSupplierDto | UpdateSupplierDto) => void;
  supplier: Supplier | null;
  error: string | null;
  isLoading: boolean;
  warehouseUuid?: string;
}

export default function SupplierModal({
  isOpen,
  onClose,
  onSubmit,
  supplier,
  error,
  isLoading,
  warehouseUuid
}: SupplierModalProps) {
  const [values, setValues] = React.useState<Partial<CreateSupplierDto & UpdateSupplierDto>>({});
  const firstFieldRef = useRef<HTMLInputElement>(null);
  const isEditMode = !!supplier;

  useEffect(() => {
    if (isOpen) {
      if (supplier) {
        // Edit mode - populate with existing supplier data
        setValues({
          name: supplier.name,
          email: supplier.email,
          phone: supplier.phone,
          address: supplier.address,
          fiscalId: supplier.fiscalId,
          rc: supplier.rc,
          code: supplier.code,
          articleNumber: supplier.articleNumber,
        });
      } else {
        // Add mode - start with empty values
        setValues({
          name: '',
          email: '',
          phone: '',
          address: '',
          fiscalId: '',
          rc: '',
          code: '',
          articleNumber: '',
        });
      }
      setTimeout(() => firstFieldRef.current?.focus(), 100);
    }
  }, [isOpen, supplier]);

  // Handle Escape key to close modal
  useEffect(() => {
    if (!isOpen) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, onClose]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setValues(v => ({ ...v, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isEditMode) {
      // For edit mode, only send the updated fields
      const updateData: UpdateSupplierDto = {};
      if (values.name !== supplier?.name) updateData.name = values.name;
      if (values.email !== supplier?.email) updateData.email = values.email;
      if (values.phone !== supplier?.phone) updateData.phone = values.phone;
      if (values.address !== supplier?.address) updateData.address = values.address;
      if (values.fiscalId !== supplier?.fiscalId) updateData.fiscalId = values.fiscalId;
      if (values.rc !== supplier?.rc) updateData.rc = values.rc;
      if (values.code !== supplier?.code) updateData.code = values.code;
      if (values.articleNumber !== supplier?.articleNumber) updateData.articleNumber = values.articleNumber;
      
      onSubmit(updateData);
    } else {
      // For add mode, send all required fields
      const createData: CreateSupplierDto = {
        name: values.name || '',
        email: values.email,
        phone: values.phone,
        address: values.address,
        fiscalId: values.fiscalId,
        rc: values.rc,
        code: values.code,
        articleNumber: values.articleNumber,
        warehouseUuid: warehouseUuid || '',
      };
      
      onSubmit(createData);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30" role="dialog" aria-modal="true" aria-labelledby="supplier-modal-title">
      <div className="bg-white rounded shadow-lg w-full max-w-md p-6 relative max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-bold mb-2" id="supplier-modal-title">
          {isEditMode ? 'Edit Supplier' : 'Add Supplier'}
        </h2>
        <p className="mb-4 text-gray-500">
          {isEditMode ? 'Update supplier details.' : 'Enter details for the new supplier.'}
        </p>
        
        {error && (
          <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded text-red-600 text-sm">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block font-medium mb-1" htmlFor="name">
              Name <span className="text-red-500">*</span>
            </label>
            <input 
              ref={firstFieldRef} 
              id="name" 
              name="name" 
              type="text" 
              required 
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
              value={values.name || ''} 
              onChange={handleChange} 
              placeholder="Supplier name" 
            />
          </div>
          
          <div>
            <label className="block font-medium mb-1" htmlFor="code">
              Code
            </label>
            <input 
              id="code" 
              name="code" 
              type="text" 
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
              value={values.code || ''} 
              onChange={handleChange} 
              placeholder="Supplier code" 
            />
          </div>
          
          <div>
            <label className="block font-medium mb-1" htmlFor="fiscalId">
              Fiscal ID
            </label>
            <input 
              id="fiscalId" 
              name="fiscalId" 
              type="text" 
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
              value={values.fiscalId || ''} 
              onChange={handleChange} 
              placeholder="Tax identification number" 
            />
          </div>

          <div>
            <label className="block font-medium mb-1" htmlFor="rc">
              Registration Certificate
            </label>
            <input 
              id="rc" 
              name="rc" 
              type="text" 
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
              value={values.rc || ''} 
              onChange={handleChange} 
              placeholder="RC number" 
            />
          </div>

          <div>
            <label className="block font-medium mb-1" htmlFor="articleNumber">
              Article Number
            </label>
            <input 
              id="articleNumber" 
              name="articleNumber" 
              type="text" 
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
              value={values.articleNumber || ''} 
              onChange={handleChange} 
              placeholder="Article number" 
            />
          </div>

          <div>
            <label className="block font-medium mb-1" htmlFor="email">
              Email
            </label>
            <input 
              id="email" 
              name="email" 
              type="email" 
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
              value={values.email || ''} 
              onChange={handleChange} 
              placeholder="Email address" 
            />
          </div>

          <div>
            <label className="block font-medium mb-1" htmlFor="phone">
              Phone
            </label>
            <input 
              id="phone" 
              name="phone" 
              type="text" 
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
              value={values.phone || ''} 
              onChange={handleChange} 
              placeholder="Phone number" 
            />
          </div>

          <div>
            <label className="block font-medium mb-1" htmlFor="address">
              Address
            </label>
            <textarea 
              id="address" 
              name="address" 
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" 
              value={values.address || ''} 
              onChange={handleChange} 
              placeholder="Full address" 
              rows={3}
            />
          </div>

          <div className="flex gap-2 mt-6">
            <button 
              type="submit" 
              className="flex-1 bg-blue-600 text-white rounded px-4 py-2 font-semibold disabled:opacity-60 hover:bg-blue-700 transition-colors" 
              disabled={isLoading}
            >
              {isLoading ? 'Saving...' : (isEditMode ? 'Save Changes' : 'Add Supplier')}
            </button>
            <button 
              type="button" 
              className="flex-1 bg-gray-200 text-gray-700 rounded px-4 py-2 font-semibold hover:bg-gray-300 transition-colors" 
              onClick={onClose} 
              disabled={isLoading}
            >
              Cancel
            </button>
          </div>
        </form>
        
        <button 
          className="absolute top-2 right-2 text-gray-400 hover:text-gray-700 text-2xl font-bold" 
          onClick={onClose} 
          aria-label="Close"
          disabled={isLoading}
        >
          &times;
        </button>
      </div>
    </div>
  );
}
