import type { Supplier, CreateSupplierDto, SupplierFilter, PaginatedResponse } from '../types';

const API_BASE_URL = '/api';

/**
 * Filter suppliers with pagination and search
 */
export async function filterSuppliers(params: {
  filter: SupplierFilter;
  page: number;
  limit: number;
}): Promise<PaginatedResponse<Supplier>> {
  const { filter, page, limit } = params;
  
  const response = await fetch(`${API_BASE_URL}/suppliers/filter`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({
      filter,
      paginationQuery: { page, limit }
    }),
  });

  if (!response.ok) {
    throw new Error(`Failed to filter suppliers: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Create a new supplier
 */
export async function createSupplier(supplierData: CreateSupplierDto): Promise<Supplier> {
  const response = await fetch(`${API_BASE_URL}/suppliers`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(supplierData),
  });

  if (!response.ok) {
    throw new Error(`Failed to create supplier: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Get a single supplier by UUID
 */
export async function getSupplier(uuid: string): Promise<Supplier> {
  const response = await fetch(`${API_BASE_URL}/suppliers/${uuid}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to get supplier: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Update an existing supplier
 */
export async function updateSupplier(uuid: string, supplierData: Partial<CreateSupplierDto>): Promise<Supplier> {
  const response = await fetch(`${API_BASE_URL}/suppliers/${uuid}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify(supplierData),
  });

  if (!response.ok) {
    throw new Error(`Failed to update supplier: ${response.statusText}`);
  }

  return response.json();
}

/**
 * Delete a supplier
 */
export async function deleteSupplier(uuid: string): Promise<void> {
  const response = await fetch(`${API_BASE_URL}/suppliers/${uuid}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });

  if (!response.ok) {
    throw new Error(`Failed to delete supplier: ${response.statusText}`);
  }
}
