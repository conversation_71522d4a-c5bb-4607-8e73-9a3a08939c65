import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsNumber, IsUUID, IsEnum } from "class-validator";

export enum TaxIdType {
  EIN = "ein",
  SSN = "ssn", 
  ITIN = "itin",
  OTHER = "other"
}

export enum RegionType {
  USA = "usa",
  EU = "eu",
  OTHER = "other"
}

export class CreateSupplierDto {
  @ApiProperty({
    example: "Acme Supplies Ltd.",
    description: "Supplier name (required)",
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: "*********01",
    description: "Fiscal identification number (tax ID)",
    required: false,
  })
  @IsOptional()
  @IsString()
  fiscalId?: string;

  @ApiProperty({
    example: "ein",
    description: "Type of tax identification number (USA)",
    enum: TaxIdType,
    required: false,
  })
  @IsOptional()
  @IsEnum(TaxIdType)
  taxIdType?: TaxIdType;

  @ApiProperty({
    example: "eu",
    description: "Region type for tax compliance",
    enum: RegionType,
    required: false,
  })
  @IsOptional()
  @IsEnum(RegionType)
  regionType?: RegionType;

  @ApiProperty({
    example: "DE*********",
    description: "VAT Identification Number (EU)",
    required: false,
  })
  @IsOptional()
  @IsString()
  vatNumber?: string;

  @ApiProperty({
    example: "DE",
    description: "Country code for VAT registration (EU)",
    required: false,
  })
  @IsOptional()
  @IsString()
  vatCountry?: string;

  @ApiProperty({
    example: "RC*********",
    description: "Registration Certificate number",
    required: false,
  })
  @IsOptional()
  @IsString()
  rc?: string;

  @ApiProperty({
    example: "ART-001-2024",
    description: "Article Number assigned by the supplier",
    required: false,
  })
  @IsOptional()
  @IsString()
  articleNumber?: string;

  @ApiProperty({
    example: "US-CA-1234567",
    description: "State tax ID number (USA)",
    required: false,
  })
  @IsOptional()
  @IsString()
  stateTaxId?: string;

  @ApiProperty({
    example: "CA",
    description: "State of incorporation/registration (USA)",
    required: false,
  })
  @IsOptional()
  @IsString()
  stateOfRegistration?: string;

  @ApiProperty({
    example: "*********",
    description: "Sales tax permit number (USA)",
    required: false,
  })
  @IsOptional()
  @IsString()
  salesTaxPermit?: string;

  @ApiProperty({ example: "<EMAIL>", required: false })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({ example: "+*********0", required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ example: "123 Main St, City, Country", required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    example: "SUP-001",
    description: "Supplier code",
    required: false,
  })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({
    example: "Premium supplier for electronics",
    description: "Supplier description",
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate for supplier location",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate for supplier location",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiProperty({ example: "Supplier notes or remarks.", required: false })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
    description: "UUIDv7 string of the warehouseUuid",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  warehouseUuid?: string;

  @ApiProperty({
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
    description: "UUIDv7 string of the userUuid",
  })
  @IsUUID("all")
  userUuid: string;
}
