// suppliersApi.ts - API utilities for Suppliers endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api/suppliers';

export interface Supplier {
  uuid: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  rc?: string;
  code?: string;
  articleNumber?: string;
  warehouseUuid: string;
  warehouseUuidString?: string;
  userUuid: string;
  userUuidString?: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSupplierDto {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  rc?: string;
  code?: string;
  articleNumber?: string;
  warehouseUuid: string;
}

export interface UpdateSupplierDto {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  rc?: string;
  code?: string;
  articleNumber?: string;
}

export interface FilterSupplierDto {
  warehouseUuid?: string;
  userUuid?: string;
  name?: string;
  email?: string;
  phone?: string;
  code?: string;
  fiscalId?: string;
  articleNumber?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationQueryDto {
  page?: number;
  limit?: number;
}

export interface PaginatedResponseDto<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Get all suppliers with pagination and filtering
 */
export async function getSuppliers(
  paginationQuery: PaginationQueryDto = { page: 1, limit: 10 }, 
  filter?: FilterSupplierDto
): Promise<PaginatedResponseDto<Supplier>> {

  const params = new URLSearchParams();
  // Use paginationQuery values for pagination
  const page = paginationQuery.page;
  const limit = paginationQuery.limit;
  if (page) params.set('page', String(page));
  if (limit) params.set('limit', String(limit));

  // Add filter parameters
  if (filter?.warehouseUuid) params.set('warehouseUuid', filter.warehouseUuid);
  if (filter?.userUuid) params.set('userUuid', filter.userUuid);
  if (filter?.name) params.set('name', filter.name);
  if (filter?.email) params.set('email', filter.email);
  if (filter?.phone) params.set('phone', filter.phone);
  if (filter?.code) params.set('code', filter.code);
  if (filter?.fiscalId) params.set('fiscalId', filter.fiscalId);
  if (filter?.articleNumber) params.set('articleNumber', filter.articleNumber);
  if (filter?.sortBy) params.set('sortBy', filter.sortBy);
  if (filter?.sortOrder) params.set('sortOrder', filter.sortOrder);

  const url = `${API_BASE}?${params.toString()}`;
  try {
    const response = await axios.get(url, {
      headers: getAxiosAuthHeaders(),
    });
    return response.data;
  } catch (error: any) {
    console.error('Suppliers API Error:', error.response?.data || error.message);
    console.error('Suppliers API Error Status:', error.response?.status);
    console.error('Suppliers API Error Headers:', error.response?.headers);
    
    // If GET endpoint fails with 400, try POST filter endpoint as fallback
    if (error.response?.status === 400) {
      console.log('GET endpoint failed, trying POST filter endpoint as fallback');
      try {
        return await filterSuppliers(filter, paginationQuery);
      } catch (fallbackError: any) {
        console.error('Fallback POST filter also failed:', fallbackError);
        // Continue with original error handling
      }
    }
    
    // Provide more specific error messages
    if (error.response?.status === 400) {
      const errorMessage = error.response?.data?.message || 'Invalid request parameters';
      throw new Error(`Bad Request: ${errorMessage}. Please check warehouse UUID format and other parameters.`);
    } else if (error.response?.status === 401) {
      throw new Error('Authentication failed. Please log in again.');
    } else if (error.response?.status === 403) {
      throw new Error('Access denied. You do not have permission to view suppliers.');
    } else if (error.response?.status === 404) {
      throw new Error('Suppliers endpoint not found. Please check API configuration.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error. Please try again later.');
    } else if (error.code === 'NETWORK_ERROR') {
      throw new Error('Network error. Please check your connection and try again.');
    } else {
      throw new Error(`Failed to load suppliers: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Filter suppliers using POST endpoint (for advanced filtering)
 */
export async function filterSuppliers(
  filter?: FilterSupplierDto,
  paginationQuery: PaginationQueryDto = { page: 1, limit: 10 }
): Promise<PaginatedResponseDto<Supplier>> {
  
  console.log('Filter suppliers API call:', { filter, paginationQuery });
  
  try {
    const response = await axios.post(`${API_BASE}/filter`, filter, {
      params: paginationQuery,
      headers: getAxiosAuthHeaders(),
    });
    return response.data;
  } catch (error: any) {
    console.error('Filter suppliers API Error:', error.response?.data || error.message);
    console.error('Filter suppliers API Error Status:', error.response?.status);
    
    // Provide specific error messages for filter endpoint
    if (error.response?.status === 400) {
      const errorMessage = error.response?.data?.message || 'Invalid filter parameters';
      throw new Error(`Filter failed: ${errorMessage}. Please check your search criteria.`);
    } else if (error.response?.status === 404) {
      throw new Error('Filter endpoint not available. Please use the main suppliers endpoint.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error during filtering. Please try again later.');
    } else {
      throw new Error(`Failed to filter suppliers: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Get a single supplier by UUID
 */
export async function getSupplier(uuid: string): Promise<Supplier> {
  const res = await axios.get(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Create a new supplier
 */
export async function createSupplier(data: CreateSupplierDto): Promise<Supplier> {
  const res = await axios.post(API_BASE, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Update supplier details
 */
export async function updateSupplier(uuid: string, data: UpdateSupplierDto): Promise<Supplier> {
  const res = await axios.patch(`${API_BASE}/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Delete a supplier (soft delete)
 */
export async function deleteSupplier(uuid: string): Promise<void> {
  await axios.delete(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
}

/**
 * Get a single supplier by UUID (alias for getSupplier)
 */
export async function getSupplierByUuid(uuid: string): Promise<Supplier> {
  return getSupplier(uuid);
}
