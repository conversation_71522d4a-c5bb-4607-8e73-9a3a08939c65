import { useState, useEffect, useMemo, useCallback } from 'react';
import { FilterSupplierDto } from '../suppliersApi';

// Local useDebounce implementation
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => clearTimeout(handler);
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Enhanced supplier filters hook with search and sorting
 */
export const useSupplierFilters = (warehouseUuid?: string) => {
  // Search and basic filters
  const [searchTerm, setSearchTerm] = useState("");
  const [emailFilter, setEmailFilter] = useState("");
  const [phoneFilter, setPhoneFilter] = useState("");
  const [codeFilter, setCodeFilter] = useState("");
  
  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState<'name' | 'email' | 'phone' | 'code' | 'articleNumber' | 'createdAt' | 'updatedAt'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // UI state
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Debounced values
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const debouncedEmailFilter = useDebounce(emailFilter, 300);
  const debouncedPhoneFilter = useDebounce(phoneFilter, 300);
  const debouncedCodeFilter = useDebounce(codeFilter, 300);

  // Build filter object
  const filter: FilterSupplierDto = useMemo(() => {
    const filterObj: FilterSupplierDto = {};
    
    if (warehouseUuid) {
      filterObj.warehouseUuid = warehouseUuid;
    }
    
    if (debouncedSearchTerm.trim()) {
      filterObj.name = debouncedSearchTerm.trim();
    }
    
    if (debouncedEmailFilter.trim()) {
      filterObj.email = debouncedEmailFilter.trim();
    }
    
    if (debouncedPhoneFilter.trim()) {
      filterObj.phone = debouncedPhoneFilter.trim();
    }
    
    if (debouncedCodeFilter.trim()) {
      filterObj.code = debouncedCodeFilter.trim();
    }
    
    // Add sorting parameters
    filterObj.sortBy = sortBy;
    filterObj.sortOrder = sortOrder;
    
    return filterObj;
  }, [warehouseUuid, debouncedSearchTerm, debouncedEmailFilter, debouncedPhoneFilter, debouncedCodeFilter, sortBy, sortOrder]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm, debouncedEmailFilter, debouncedPhoneFilter, debouncedCodeFilter]);

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    setSearchTerm("");
    setEmailFilter("");
    setPhoneFilter("");
    setCodeFilter("");
    setCurrentPage(1);
  }, []);

  // Check if filters are active
  const hasActiveFilters = useMemo(() => {
    return !!(
      searchTerm ||
      emailFilter ||
      phoneFilter ||
      codeFilter
    );
  }, [searchTerm, emailFilter, phoneFilter, codeFilter]);

  return {
    // Search and basic filters
    searchTerm,
    setSearchTerm,
    emailFilter,
    setEmailFilter,
    phoneFilter,
    setPhoneFilter,
    codeFilter,
    setCodeFilter,
    
    // Pagination and sorting
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    sortBy,
    setSortBy,
    sortOrder,
    setSortOrder,
    
    // UI state
    showAdvancedFilters,
    setShowAdvancedFilters,
    
    // Computed values
    filter,
    clearAllFilters,
    hasActiveFilters,
  };
}; 