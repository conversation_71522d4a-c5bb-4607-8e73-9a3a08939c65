import React, { useRef, useEffect, useState } from 'react';
import { posStyles } from '../styles/posStyles';
import { formatCurrency } from '../utils/posHelpers';
import { PaymentSelector } from './PaymentSelector';
import { TaxControls } from './TaxControls';
import { NotesControls } from './NotesControls';
import type { PurchaseCartProps } from '../types';
import { FiUser } from 'react-icons/fi';

export function PurchaseCart({
  items,
  selectedSupplier,
  notes,
  subtotal,
  tax,
  total,
  isSubmitting,
  error,
  paymentMethod,
  amountPaid,
  taxEnabled,
  taxRate,
  notesEnabled,
  onRemoveItem,
  onUpdateQuantity,
  onUpdatePrice,
  onNotesChange,
  onSubmit,
  onSupplierSelect,
  onPaymentMethodChange,
  onAmountPaidChange,
  onTaxEnabledChange,
  onTaxRateChange,
  onNotesEnabledChange,
  highlightedCartItemUuid,
  isEditMode = false,
  isLoadMode = false,
  isLoading = false,
  disabled = false,
}: PurchaseCartProps & { isEditMode?: boolean; isLoadMode?: boolean; isLoading?: boolean }) {
  return (
    <aside className="bg-gray-50 border-l border-gray-200 w-full lg:min-w-80 lg:max-w-96 h-full flex flex-col">
      {/* Error Display - Fixed */}
      {error && (
        <div className="p-3 bg-red-50 border-b border-red-200">
          <div className="flex items-center gap-2">
            <div className="text-red-500">⚠️</div>
            <div className="text-sm text-red-700">{error}</div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white flex-shrink-0">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          {isEditMode ? 'Edit Purchase' : isLoadMode ? 'Continue Purchase' : 'New Purchase'}
        </h3>
        
        {/* Supplier Selection */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Supplier
          </label>
          <button
            onClick={onSupplierSelect}
            disabled={isLoading || disabled}
            className={`w-full p-3 border rounded-lg text-left transition-colors ${
              selectedSupplier
                ? 'border-blue-500 bg-blue-50 text-blue-900'
                : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
            } ${(isLoading || disabled) ? 'opacity-50 cursor-not-allowed' : 'hover:border-blue-400'}`}
          >
            <div className="flex items-center gap-2">
              <FiUser className="h-4 w-4" />
              <div className="flex-1 min-w-0">
                {selectedSupplier ? (
                  <div>
                    <div className="font-medium text-sm truncate">
                      {selectedSupplier.name}
                    </div>
                    {selectedSupplier.email && (
                      <div className="text-xs text-gray-600 truncate">
                        {selectedSupplier.email}
                      </div>
                    )}
                  </div>
                ) : (
                  <span className="text-sm">Select supplier...</span>
                )}
              </div>
            </div>
          </button>
        </div>
      </div>

      {/* Cart Items - Scrollable */}
      <div className="flex-1 overflow-hidden p-3">
        {items.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-6">
            <div className="text-base font-medium text-gray-600 mb-1">
              No items added yet
            </div>
            <div className="text-xs text-gray-500">
              Select products to add to the purchase
            </div>
          </div>
        ) : (
          <div
            className="cart-vertical-scroll overflow-y-auto overflow-x-hidden pr-1 h-full"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: '#9ca3af #f3f4f6',
            }}
          >
            <div className="space-y-2">
              {items.map((item) => {
                const isHighlighted = highlightedCartItemUuid === item.productUuid;
                return (
                  <CartItem
                    key={item.productUuid}
                    item={item}
                    isHighlighted={isHighlighted}
                    onUpdateQuantity={onUpdateQuantity}
                    onUpdatePrice={onUpdatePrice}
                    onRemoveItem={onRemoveItem}
                    disabled={isLoading || disabled}
                  />
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Footer - Fixed at bottom */}
      <div className="border-t border-gray-200 p-3 flex-shrink-0 bg-white">
        {/* Tax Controls */}
        <TaxControls
          taxEnabled={taxEnabled}
          taxRate={taxRate}
          onTaxEnabledChange={onTaxEnabledChange}
          onTaxRateChange={onTaxRateChange}
          disabled={isLoading || disabled}
        />

        {/* Payment Method and Amount */}
        <PaymentSelector
          paymentMethod={paymentMethod}
          onPaymentMethodChange={onPaymentMethodChange}
          total={total}
          amountPaid={amountPaid}
          onAmountPaidChange={onAmountPaidChange}
          disabled={isLoading || disabled}
        />

        {/* Notes Controls */}
        <NotesControls
          notesEnabled={notesEnabled}
          notes={notes}
          onNotesEnabledChange={onNotesEnabledChange}
          onNotesChange={onNotesChange}
          disabled={isLoading || disabled}
        />

        {/* Order Summary */}
        <div className="space-y-1 mb-3">
          <div className="flex justify-between">
            <span className="text-gray-600 text-sm">Subtotal:</span>
            <span className="font-medium text-gray-900 text-sm">{formatCurrency(subtotal)}</span>
          </div>
          {taxEnabled && (
            <div className="flex justify-between">
              <span className="text-gray-600 text-sm">
                Tax ({((typeof taxRate === 'number' && !isNaN(taxRate) ? taxRate : 0) * 100).toFixed(1)}%):
              </span>
              <span className="font-medium text-gray-900 text-sm">{formatCurrency(tax)}</span>
            </div>
          )}
          <div className="flex justify-between border-t pt-1">
            <span className="text-gray-600 text-sm">Total:</span>
            <span className="text-base font-bold text-gray-900">{formatCurrency(total)}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <button
            onClick={onSubmit}
            disabled={items.length === 0 || !paymentMethod || !selectedSupplier || isSubmitting}
            className="w-full bg-green-600 text-white py-2 px-3 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 font-medium disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            {isSubmitting ? 'Processing...' : (
              isEditMode ? 'Update Purchase (F4)' :
              isLoadMode ? 'Continue Purchase' :
              'Complete Purchase (F4)'
            )}
          </button>

          {items.length > 0 && !isEditMode && !isLoadMode && (
            <button
              onClick={() => {
                // Clear cart functionality would go here
                // This would need to be passed as a prop
              }}
              className="w-full bg-gray-600 text-white py-1.5 px-3 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 text-xs"
            >
              Clear Cart
            </button>
          )}
        </div>
      </div>
    </aside>
  );
}

// CartItem component with auto-focus functionality - Compact design
interface CartItemProps {
  item: any;
  isHighlighted: boolean;
  onUpdateQuantity: (productUuid: string, quantity: number) => void;
  onUpdatePrice: (productUuid: string, unitPrice: number) => void;
  onRemoveItem: (productUuid: string) => void;
  disabled?: boolean;
}

function CartItem({ item, isHighlighted, onUpdateQuantity, onUpdatePrice, onRemoveItem, disabled = false }: CartItemProps) {
  const quantityInputRef = useRef<HTMLInputElement>(null);
  const priceInputRef = useRef<HTMLInputElement>(null);

  // Use string state for input values to maintain cursor position
  const [quantityInputValue, setQuantityInputValue] = useState('');
  const [priceInputValue, setPriceInputValue] = useState('');
  const [quantityCursorPosition, setQuantityCursorPosition] = useState(0);
  const [priceCursorPosition, setPriceCursorPosition] = useState(0);

  // Ensure quantity and unitPrice are always valid numbers
  const safeQuantity = typeof item.quantity === 'number' && !isNaN(item.quantity) ? item.quantity : 0;
  const safeUnitPrice = typeof item.unitPrice === 'number' && !isNaN(item.unitPrice) ? item.unitPrice : 0;

  // Initialize input values when item changes
  useEffect(() => {
    setQuantityInputValue(safeQuantity.toString());
    setPriceInputValue(safeUnitPrice.toString());
  }, [safeQuantity, safeUnitPrice]);

  // Auto-focus quantity input when item is highlighted
  useEffect(() => {
    if (isHighlighted && quantityInputRef.current) {
      // Add a small delay to ensure the DOM is ready and the component is fully rendered
      const timer = setTimeout(() => {
        if (quantityInputRef.current) {
          quantityInputRef.current.focus();
          quantityInputRef.current.select();

        }
      }, 100); // Small delay to ensure DOM is ready

      return () => clearTimeout(timer);
    }
  }, [isHighlighted, item.productUuid]);

  // Restore cursor position after value updates
  useEffect(() => {
    if (quantityInputRef.current && quantityCursorPosition > 0) {
      quantityInputRef.current.setSelectionRange(quantityCursorPosition, quantityCursorPosition);
    }
  }, [quantityInputValue, quantityCursorPosition]);

  useEffect(() => {
    if (priceInputRef.current && priceCursorPosition > 0) {
      priceInputRef.current.setSelectionRange(priceCursorPosition, priceCursorPosition);
    }
  }, [priceInputValue, priceCursorPosition]);

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart || 0;

    setQuantityInputValue(value);
    setQuantityCursorPosition(cursorPos);

    if (disabled) return;

    // Allow empty string for editing
    if (value === '') {
      return;
    }

    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue >= 0) {
      // Debounce the actual update to avoid too many calls
      clearTimeout((handleQuantityChange as any).timeout);
      (handleQuantityChange as any).timeout = setTimeout(() => {
        onUpdateQuantity(item.productUuid, numValue);
      }, 300);
    }
  };

  const handleQuantityBlur = () => {
    if (disabled) return;

    const numValue = parseFloat(quantityInputValue);
    if (isNaN(numValue) || numValue < 0) {
      // Reset to original value if invalid
      setQuantityInputValue(safeQuantity.toString());
    } else {
      onUpdateQuantity(item.productUuid, numValue);
    }
  };

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const cursorPos = e.target.selectionStart || 0;

    setPriceInputValue(value);
    setPriceCursorPosition(cursorPos);

    if (disabled) return;

    // Allow empty string for editing
    if (value === '') {
      return;
    }

    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue >= 0) {
      // Debounce the actual update to avoid too many calls
      clearTimeout((handlePriceChange as any).timeout);
      (handlePriceChange as any).timeout = setTimeout(() => {
        onUpdatePrice(item.productUuid, numValue);
      }, 300);
    }
  };

  const handlePriceBlur = () => {
    if (disabled) return;

    const numValue = parseFloat(priceInputValue);
    if (isNaN(numValue) || numValue < 0) {
      // Reset to original value if invalid
      setPriceInputValue(safeUnitPrice.toString());
    } else {
      onUpdatePrice(item.productUuid, numValue);
    }
  };

  return (
    <div
      className={`${isHighlighted ? 'bg-yellow-50 border-yellow-400 border-2' : 'border border-gray-200'} p-2 rounded-md transition-all duration-300`}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <div className="font-medium text-gray-900 truncate mb-0.5 text-sm">
            {typeof item.name === 'string' ? item.name :
             typeof (item as any).productName === 'string' ? (item as any).productName :
             'Unknown Product'}
          </div>
          <div className="text-xs text-gray-600">
            {formatCurrency(safeUnitPrice)} each
          </div>
          <div className="flex items-center gap-2 mt-1.5">
            <div className="flex items-center gap-1">
              <label className="text-xs text-gray-500">Qty:</label>
              <input
                ref={quantityInputRef}
                type="number"
                min="0"
                step="0.01"
                value={quantityInputValue}
                onChange={handleQuantityChange}
                onBlur={handleQuantityBlur}
                disabled={disabled}
                className={`w-16 p-1 text-center border rounded text-xs transition-all duration-200 ${
                  isHighlighted
                    ? 'border-yellow-400 border-2 bg-yellow-50 focus:ring-yellow-500 focus:border-yellow-500 shadow-sm'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                } focus:outline-none focus:ring-1 ${
                  disabled ? 'bg-gray-100 cursor-not-allowed opacity-50' : ''
                }`}
                placeholder={isHighlighted ? "Type quantity..." : ""}
              />
            </div>
            <div className="flex items-center gap-1">
              <label className="text-xs text-gray-500">Price:</label>
              <input
                ref={priceInputRef}
                type="number"
                min="0"
                step="0.01"
                value={priceInputValue}
                onChange={handlePriceChange}
                onBlur={handlePriceBlur}
                disabled={disabled}
                className={`w-20 p-1 text-center border rounded text-xs ${isHighlighted ? 'border-yellow-400 border-2 bg-yellow-50 focus:ring-yellow-500 focus:border-yellow-500' : 'border-gray-300'} focus:outline-none focus:ring-1 focus:ring-blue-500 ${
                  disabled ? 'bg-gray-100 cursor-not-allowed opacity-50' : ''
                }`}
              />
            </div>
          </div>
        </div>
        <div className="flex flex-col items-end">
          <button
            onClick={() => onRemoveItem(item.productUuid)}
            disabled={disabled}
            className={`p-0.5 rounded text-sm ${
              disabled
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-red-500 hover:text-red-700 hover:bg-red-50'
            }`}
            title="Remove item"
          >
            ×
          </button>
          <div className="text-xs font-semibold mt-1">
            {formatCurrency(item.totalPrice)}
          </div>
        </div>
      </div>
    </div>
  );
}
