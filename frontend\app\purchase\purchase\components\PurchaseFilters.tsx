import React, { useState } from 'react';
import { <PERSON><PERSON>ilter, <PERSON><PERSON>earch, FiCalendar, FiDollarSign, FiUser, FiX, FiChevronDown } from 'react-icons/fi';
import { FilterPurchaseDto, PurchaseStatus, PaymentMethods } from '../purchaseApi';

interface PurchaseFiltersProps {
  showFilters: boolean;
  filter: FilterPurchaseDto;
  onFilterChange: (newFilter: Partial<FilterPurchaseDto>) => void;
  onClearFilters: () => void;
  onToggleFilters: () => void;
}

export const PurchaseFilters: React.FC<PurchaseFiltersProps> = ({
  showFilters,
  filter,
  onFilterChange,
  onClearFilters,
  onToggleFilters
}) => {
  const [localFilters, setLocalFilters] = useState<FilterPurchaseDto>(filter);

  const handleLocalFilterChange = (key: keyof FilterPurchaseDto, value: any) => {
    let sanitizedValue = value;

    // Special handling for numeric values
    if (key === 'minAmount' || key === 'maxAmount') {
      if (value === '' || value === null || value === undefined) {
        sanitizedValue = undefined;
      } else {
        const numValue = parseFloat(value);
        if (isNaN(numValue) || numValue < 0) {
          sanitizedValue = undefined;
        } else {
          sanitizedValue = numValue;
        }
      }
    }

    const newFilters = { ...localFilters, [key]: sanitizedValue };
    setLocalFilters(newFilters);
    onFilterChange({ [key]: sanitizedValue });
  };

  const handleClearFilters = () => {
    setLocalFilters({});
    onClearFilters();
  };

  // Format date for input (YYYY-MM-DD)
  const formatDateForInput = (dateString?: string) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch {
      return '';
    }
  };

  // Format date for API (ISO string)
  const formatDateForAPI = (dateString: string) => {
    if (!dateString) return undefined;
    try {
      const date = new Date(dateString);
      return date.toISOString();
    } catch {
      return undefined;
    }
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        {/* Filter Toggle Header */}
        <div className="px-4 py-3 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <button
              onClick={onToggleFilters}
              className="flex items-center text-sm font-medium text-gray-700 hover:text-gray-900"
            >
              <FiFilter className="w-4 h-4 mr-2" />
              Filters
              <span className="ml-2 text-xs text-gray-500">
                {showFilters ? 'Hide' : 'Show'}
              </span>
            </button>

            {Object.values(localFilters).some(value => value !== undefined && value !== '') && (
              <button
                onClick={handleClearFilters}
                className="flex items-center text-sm text-red-600 hover:text-red-700"
              >
                <FiX className="w-4 h-4 mr-1" />
                Clear All
              </button>
            )}
          </div>
        </div>

        {/* Filter Content */}
        {showFilters && (
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Purchase Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiSearch className="w-4 h-4 inline mr-1" />
                  Purchase Number
                </label>
                <input
                  type="text"
                  value={localFilters.purchaseNumber || ''}
                  onChange={(e) => handleLocalFilterChange('purchaseNumber', e.target.value)}
                  placeholder="Search by purchase number..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Supplier Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiUser className="w-4 h-4 inline mr-1" />
                  Supplier Name
                </label>
                <input
                  type="text"
                  value={localFilters.supplierName || ''}
                  onChange={(e) => handleLocalFilterChange('supplierName', e.target.value)}
                  placeholder="Search by supplier..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <div className="relative">
                  <select
                    value={localFilters.status || ''}
                    onChange={(e) => handleLocalFilterChange('status', e.target.value as PurchaseStatus)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
                  >
                    <option value="">All Statuses</option>
                    <option value={PurchaseStatus.PAID}>Paid</option>
                    <option value={PurchaseStatus.PARTIALLY_PAID}>Partially Paid</option>
                    <option value={PurchaseStatus.UNPAID}>Unpaid</option>
                    <option value={PurchaseStatus.CANCELLED}>Cancelled</option>
                  </select>
                  <FiChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>

              {/* Payment Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Method
                </label>
                <div className="relative">
                  <select
                    value={localFilters.paymentMethod || ''}
                    onChange={(e) => handleLocalFilterChange('paymentMethod', e.target.value as PaymentMethods)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
                  >
                    <option value="">All Methods</option>
                    <option value={PaymentMethods.CASH}>Cash</option>
                    <option value={PaymentMethods.CREDIT_CARD}>Credit Card</option>
                    <option value={PaymentMethods.BANK_TRANSFER}>Bank Transfer</option>
                    <option value={PaymentMethods.MOBILE_PAYMENT}>Mobile Payment</option>
                    <option value={PaymentMethods.CHEQUE}>Cheque</option>
                    <option value={PaymentMethods.OTHER}>Other</option>
                  </select>
                  <FiChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>

            {/* Second Row - Date and Amount Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
              {/* Creation Date From */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiCalendar className="w-4 h-4 inline mr-1" />
                  Created From
                </label>
                <input
                  type="date"
                  value={formatDateForInput(localFilters.createdFrom)}
                  onChange={(e) => handleLocalFilterChange('createdFrom', formatDateForAPI(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Creation Date To */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiCalendar className="w-4 h-4 inline mr-1" />
                  Created To
                </label>
                <input
                  type="date"
                  value={formatDateForInput(localFilters.createdTo)}
                  onChange={(e) => handleLocalFilterChange('createdTo', formatDateForAPI(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Min Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiDollarSign className="w-4 h-4 inline mr-1" />
                  Min Amount
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={localFilters.minAmount || ''}
                  onChange={(e) => handleLocalFilterChange('minAmount', e.target.value)}
                  placeholder="0.00"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Max Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiDollarSign className="w-4 h-4 inline mr-1" />
                  Max Amount
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={localFilters.maxAmount || ''}
                  onChange={(e) => handleLocalFilterChange('maxAmount', e.target.value)}
                  placeholder="0.00"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Third Row - Purchase Date Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              {/* Purchase Date From */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiCalendar className="w-4 h-4 inline mr-1" />
                  Purchase Date From
                </label>
                <input
                  type="date"
                  value={formatDateForInput(localFilters.startDate)}
                  onChange={(e) => handleLocalFilterChange('startDate', formatDateForAPI(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Purchase Date To */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiCalendar className="w-4 h-4 inline mr-1" />
                  Purchase Date To
                </label>
                <input
                  type="date"
                  value={formatDateForInput(localFilters.endDate)}
                  onChange={(e) => handleLocalFilterChange('endDate', formatDateForAPI(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
