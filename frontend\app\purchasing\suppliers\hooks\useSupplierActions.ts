import { useState } from 'react';
import { toast } from 'sonner';
import { Supplier, CreateSupplierDto, UpdateSupplierDto } from '../suppliersApi';
import { useCreateSupplier, useUpdateSupplier, useDeleteSupplier } from '../useSuppliers';

/**
 * Hook for managing supplier actions (create, update, delete, view details)
 */
export const useSupplierActions = () => {
  // Modal states
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);

  // Mutations
  const createSupplierMutation = useCreateSupplier();
  const updateSupplierMutation = useUpdateSupplier();
  const deleteSupplierMutation = useDeleteSupplier();

  // Loading states
  const isLoading = createSupplierMutation.isPending || updateSupplierMutation.isPending || deleteSupplierMutation.isPending;

  // Add supplier
  const handleAddSupplier = () => {
    setSelectedSupplier(null);
    setIsAddModalOpen(true);
  };

  // Edit supplier
  const handleEditSupplier = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setIsEditModalOpen(true);
  };

  // View supplier details
  const handleViewSupplierDetails = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setIsDetailsModalOpen(true);
  };

  // Delete supplier
  const handleDeleteSupplier = (supplier: Supplier) => {
    setSupplierToDelete(supplier);
    setIsDeleteModalOpen(true);
  };

  // Submit supplier form (create or update)
  const handleSubmitSupplier = async (data: CreateSupplierDto | UpdateSupplierDto) => {
    try {
      if (selectedSupplier) {
        // Update existing supplier
        await updateSupplierMutation.mutateAsync({
          uuid: selectedSupplier.uuid,
          data: data as UpdateSupplierDto
        });
        toast.success('Supplier updated successfully');
      } else {
        // Create new supplier
        await createSupplierMutation.mutateAsync(data as CreateSupplierDto);
        toast.success('Supplier created successfully');
      }
      
      // Close appropriate modal
      if (selectedSupplier) {
        setIsEditModalOpen(false);
      } else {
        setIsAddModalOpen(false);
      }
      setSelectedSupplier(null);
    } catch (error) {
      console.error('Error saving supplier:', error);
      toast.error('Failed to save supplier');
      throw error; // Re-throw to let the modal handle the error
    }
  };

  // Confirm delete supplier
  const handleConfirmDelete = async () => {
    if (!supplierToDelete) return;

    try {
      await deleteSupplierMutation.mutateAsync(supplierToDelete.uuid);
      toast.success('Supplier deleted successfully');
      setIsDeleteModalOpen(false);
      setSupplierToDelete(null);
    } catch (error) {
      console.error('Error deleting supplier:', error);
      toast.error('Failed to delete supplier');
    }
  };

  // Close modals
  const closeAddModal = () => {
    setIsAddModalOpen(false);
    setSelectedSupplier(null);
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setSelectedSupplier(null);
  };

  const closeDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedSupplier(null);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setSupplierToDelete(null);
  };

  return {
    // Modal states
    isAddModalOpen,
    isEditModalOpen,
    isDetailsModalOpen,
    isDeleteModalOpen,
    selectedSupplier,
    supplierToDelete,
    
    // Loading state
    isLoading,
    
    // Actions
    handleAddSupplier,
    handleEditSupplier,
    handleViewSupplierDetails,
    handleDeleteSupplier,
    handleSubmitSupplier,
    handleConfirmDelete,
    
    // Modal close handlers
    closeAddModal,
    closeEditModal,
    closeDetailsModal,
    closeDeleteModal,
  };
}; 