{"fileNames": ["../../node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/typescript/lib/lib.es2021.d.ts", "../../node_modules/typescript/lib/lib.es2022.d.ts", "../../node_modules/typescript/lib/lib.es2023.d.ts", "../../node_modules/typescript/lib/lib.es2024.d.ts", "../../node_modules/typescript/lib/lib.esnext.d.ts", "../../node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../node_modules/typescript/lib/lib.es2017.date.d.ts", "../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/typescript/lib/lib.es2020.date.d.ts", "../../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.number.d.ts", "../../node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../node_modules/typescript/lib/lib.es2021.string.d.ts", "../../node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.array.d.ts", "../../node_modules/typescript/lib/lib.es2022.error.d.ts", "../../node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.object.d.ts", "../../node_modules/typescript/lib/lib.es2022.string.d.ts", "../../node_modules/typescript/lib/lib.es2022.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2023.array.d.ts", "../../node_modules/typescript/lib/lib.es2023.collection.d.ts", "../../node_modules/typescript/lib/lib.es2023.intl.d.ts", "../../node_modules/typescript/lib/lib.es2024.arraybuffer.d.ts", "../../node_modules/typescript/lib/lib.es2024.collection.d.ts", "../../node_modules/typescript/lib/lib.es2024.object.d.ts", "../../node_modules/typescript/lib/lib.es2024.promise.d.ts", "../../node_modules/typescript/lib/lib.es2024.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2024.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2024.string.d.ts", "../../node_modules/typescript/lib/lib.esnext.array.d.ts", "../../node_modules/typescript/lib/lib.esnext.collection.d.ts", "../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../node_modules/typescript/lib/lib.esnext.disposable.d.ts", "../../node_modules/typescript/lib/lib.esnext.promise.d.ts", "../../node_modules/typescript/lib/lib.esnext.decorators.d.ts", "../../node_modules/typescript/lib/lib.esnext.iterator.d.ts", "../../node_modules/typescript/lib/lib.esnext.float16.d.ts", "../../node_modules/typescript/lib/lib.decorators.d.ts", "../../node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../../node_modules/next/dist/styled-jsx/types/css.d.ts", "../../node_modules/@types/react/global.d.ts", "../../node_modules/csstype/index.d.ts", "../../node_modules/@types/prop-types/index.d.ts", "../../node_modules/@types/react/index.d.ts", "../../node_modules/next/dist/styled-jsx/types/index.d.ts", "../../node_modules/next/dist/styled-jsx/types/macro.d.ts", "../../node_modules/next/dist/styled-jsx/types/style.d.ts", "../../node_modules/next/dist/styled-jsx/types/global.d.ts", "../../node_modules/next/dist/shared/lib/amp.d.ts", "../../node_modules/next/amp.d.ts", "../../node_modules/@types/node/compatibility/disposable.d.ts", "../../node_modules/@types/node/compatibility/indexable.d.ts", "../../node_modules/@types/node/compatibility/iterators.d.ts", "../../node_modules/@types/node/compatibility/index.d.ts", "../../node_modules/@types/node/globals.typedarray.d.ts", "../../node_modules/@types/node/buffer.buffer.d.ts", "../../node_modules/undici-types/header.d.ts", "../../node_modules/undici-types/readable.d.ts", "../../node_modules/undici-types/file.d.ts", "../../node_modules/undici-types/fetch.d.ts", "../../node_modules/undici-types/formdata.d.ts", "../../node_modules/undici-types/connector.d.ts", "../../node_modules/undici-types/client.d.ts", "../../node_modules/undici-types/errors.d.ts", "../../node_modules/undici-types/dispatcher.d.ts", "../../node_modules/undici-types/global-dispatcher.d.ts", "../../node_modules/undici-types/global-origin.d.ts", "../../node_modules/undici-types/pool-stats.d.ts", "../../node_modules/undici-types/pool.d.ts", "../../node_modules/undici-types/handlers.d.ts", "../../node_modules/undici-types/balanced-pool.d.ts", "../../node_modules/undici-types/agent.d.ts", "../../node_modules/undici-types/mock-interceptor.d.ts", "../../node_modules/undici-types/mock-agent.d.ts", "../../node_modules/undici-types/mock-client.d.ts", "../../node_modules/undici-types/mock-pool.d.ts", "../../node_modules/undici-types/mock-errors.d.ts", "../../node_modules/undici-types/proxy-agent.d.ts", "../../node_modules/undici-types/env-http-proxy-agent.d.ts", "../../node_modules/undici-types/retry-handler.d.ts", "../../node_modules/undici-types/retry-agent.d.ts", "../../node_modules/undici-types/api.d.ts", "../../node_modules/undici-types/interceptors.d.ts", "../../node_modules/undici-types/util.d.ts", "../../node_modules/undici-types/cookies.d.ts", "../../node_modules/undici-types/patch.d.ts", "../../node_modules/undici-types/websocket.d.ts", "../../node_modules/undici-types/eventsource.d.ts", "../../node_modules/undici-types/filereader.d.ts", "../../node_modules/undici-types/diagnostics-channel.d.ts", "../../node_modules/undici-types/content-type.d.ts", "../../node_modules/undici-types/cache.d.ts", "../../node_modules/undici-types/index.d.ts", "../../node_modules/@types/node/globals.d.ts", "../../node_modules/@types/node/assert.d.ts", "../../node_modules/@types/node/assert/strict.d.ts", "../../node_modules/@types/node/async_hooks.d.ts", "../../node_modules/@types/node/buffer.d.ts", "../../node_modules/@types/node/child_process.d.ts", "../../node_modules/@types/node/cluster.d.ts", "../../node_modules/@types/node/console.d.ts", "../../node_modules/@types/node/constants.d.ts", "../../node_modules/@types/node/crypto.d.ts", "../../node_modules/@types/node/dgram.d.ts", "../../node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/@types/node/dns.d.ts", "../../node_modules/@types/node/dns/promises.d.ts", "../../node_modules/@types/node/domain.d.ts", "../../node_modules/@types/node/dom-events.d.ts", "../../node_modules/@types/node/events.d.ts", "../../node_modules/@types/node/fs.d.ts", "../../node_modules/@types/node/fs/promises.d.ts", "../../node_modules/@types/node/http.d.ts", "../../node_modules/@types/node/http2.d.ts", "../../node_modules/@types/node/https.d.ts", "../../node_modules/@types/node/inspector.d.ts", "../../node_modules/@types/node/module.d.ts", "../../node_modules/@types/node/net.d.ts", "../../node_modules/@types/node/os.d.ts", "../../node_modules/@types/node/path.d.ts", "../../node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/@types/node/process.d.ts", "../../node_modules/@types/node/punycode.d.ts", "../../node_modules/@types/node/querystring.d.ts", "../../node_modules/@types/node/readline.d.ts", "../../node_modules/@types/node/readline/promises.d.ts", "../../node_modules/@types/node/repl.d.ts", "../../node_modules/@types/node/sea.d.ts", "../../node_modules/@types/node/stream.d.ts", "../../node_modules/@types/node/stream/promises.d.ts", "../../node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/@types/node/stream/web.d.ts", "../../node_modules/@types/node/string_decoder.d.ts", "../../node_modules/@types/node/test.d.ts", "../../node_modules/@types/node/timers.d.ts", "../../node_modules/@types/node/timers/promises.d.ts", "../../node_modules/@types/node/tls.d.ts", "../../node_modules/@types/node/trace_events.d.ts", "../../node_modules/@types/node/tty.d.ts", "../../node_modules/@types/node/url.d.ts", "../../node_modules/@types/node/util.d.ts", "../../node_modules/@types/node/v8.d.ts", "../../node_modules/@types/node/vm.d.ts", "../../node_modules/@types/node/wasi.d.ts", "../../node_modules/@types/node/worker_threads.d.ts", "../../node_modules/@types/node/zlib.d.ts", "../../node_modules/@types/node/index.d.ts", "../../node_modules/next/dist/server/get-page-files.d.ts", "../../node_modules/@types/react/canary.d.ts", "../../node_modules/@types/react/experimental.d.ts", "../../node_modules/@types/react-dom/index.d.ts", "../../node_modules/@types/react-dom/canary.d.ts", "../../node_modules/@types/react-dom/experimental.d.ts", "../../node_modules/next/dist/compiled/webpack/webpack.d.ts", "../../node_modules/next/dist/server/config.d.ts", "../../node_modules/next/dist/lib/load-custom-routes.d.ts", "../../node_modules/next/dist/shared/lib/image-config.d.ts", "../../node_modules/next/dist/build/webpack/plugins/subresource-integrity-plugin.d.ts", "../../node_modules/next/dist/server/body-streams.d.ts", "../../node_modules/next/dist/server/future/route-kind.d.ts", "../../node_modules/next/dist/server/future/route-definitions/route-definition.d.ts", "../../node_modules/next/dist/server/future/route-matches/route-match.d.ts", "../../node_modules/next/dist/client/components/app-router-headers.d.ts", "../../node_modules/next/dist/server/request-meta.d.ts", "../../node_modules/next/dist/server/lib/revalidate.d.ts", "../../node_modules/next/dist/server/config-shared.d.ts", "../../node_modules/next/dist/server/base-http/index.d.ts", "../../node_modules/next/dist/server/api-utils/index.d.ts", "../../node_modules/next/dist/server/node-environment.d.ts", "../../node_modules/next/dist/server/require-hook.d.ts", "../../node_modules/next/dist/server/node-polyfill-crypto.d.ts", "../../node_modules/next/dist/lib/page-types.d.ts", "../../node_modules/next/dist/build/analysis/get-page-static-info.d.ts", "../../node_modules/next/dist/build/webpack/loaders/get-module-build-info.d.ts", "../../node_modules/next/dist/build/webpack/plugins/middleware-plugin.d.ts", "../../node_modules/next/dist/server/render-result.d.ts", "../../node_modules/next/dist/server/future/helpers/i18n-provider.d.ts", "../../node_modules/next/dist/server/web/next-url.d.ts", "../../node_modules/next/dist/compiled/@edge-runtime/cookies/index.d.ts", "../../node_modules/next/dist/server/web/spec-extension/cookies.d.ts", "../../node_modules/next/dist/server/web/spec-extension/request.d.ts", "../../node_modules/next/dist/server/web/spec-extension/fetch-event.d.ts", "../../node_modules/next/dist/server/web/spec-extension/response.d.ts", "../../node_modules/next/dist/server/web/types.d.ts", "../../node_modules/next/dist/lib/setup-exception-listeners.d.ts", "../../node_modules/next/dist/lib/constants.d.ts", "../../node_modules/next/dist/build/index.d.ts", "../../node_modules/next/dist/build/webpack/plugins/pages-manifest-plugin.d.ts", "../../node_modules/next/dist/shared/lib/router/utils/route-regex.d.ts", "../../node_modules/next/dist/shared/lib/router/utils/route-matcher.d.ts", "../../node_modules/next/dist/shared/lib/router/utils/parse-url.d.ts", "../../node_modules/next/dist/server/base-http/node.d.ts", "../../node_modules/next/dist/server/font-utils.d.ts", "../../node_modules/next/dist/build/webpack/plugins/flight-manifest-plugin.d.ts", "../../node_modules/next/dist/server/future/route-modules/route-module.d.ts", "../../node_modules/next/dist/shared/lib/deep-readonly.d.ts", "../../node_modules/next/dist/server/load-components.d.ts", "../../node_modules/next/dist/shared/lib/router/utils/middleware-route-matcher.d.ts", "../../node_modules/next/dist/build/webpack/plugins/next-font-manifest-plugin.d.ts", "../../node_modules/next/dist/server/future/route-definitions/locale-route-definition.d.ts", "../../node_modules/next/dist/server/future/route-definitions/pages-route-definition.d.ts", "../../node_modules/next/dist/shared/lib/mitt.d.ts", "../../node_modules/next/dist/client/with-router.d.ts", "../../node_modules/next/dist/client/router.d.ts", "../../node_modules/next/dist/client/route-loader.d.ts", "../../node_modules/next/dist/client/page-loader.d.ts", "../../node_modules/next/dist/shared/lib/bloom-filter.d.ts", "../../node_modules/next/dist/shared/lib/router/router.d.ts", "../../node_modules/next/dist/shared/lib/router-context.shared-runtime.d.ts", "../../node_modules/next/dist/shared/lib/loadable-context.shared-runtime.d.ts", "../../node_modules/next/dist/shared/lib/loadable.shared-runtime.d.ts", "../../node_modules/next/dist/shared/lib/image-config-context.shared-runtime.d.ts", "../../node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.d.ts", "../../node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.d.ts", "../../node_modules/next/dist/server/future/route-definitions/app-page-route-definition.d.ts", "../../node_modules/next/dist/shared/lib/modern-browserslist-target.d.ts", "../../node_modules/next/dist/shared/lib/constants.d.ts", "../../node_modules/next/dist/build/webpack/loaders/metadata/types.d.ts", "../../node_modules/next/dist/build/page-extensions-type.d.ts", "../../node_modules/next/dist/build/webpack/loaders/next-app-loader.d.ts", "../../node_modules/next/dist/server/lib/app-dir-module.d.ts", "../../node_modules/next/dist/server/response-cache/types.d.ts", "../../node_modules/next/dist/server/response-cache/index.d.ts", "../../node_modules/next/dist/server/lib/incremental-cache/index.d.ts", "../../node_modules/next/dist/client/components/hooks-server-context.d.ts", "../../node_modules/next/dist/server/app-render/dynamic-rendering.d.ts", "../../node_modules/next/dist/client/components/static-generation-async-storage-instance.d.ts", "../../node_modules/next/dist/client/components/static-generation-async-storage.external.d.ts", "../../node_modules/next/dist/server/web/spec-extension/adapters/request-cookies.d.ts", "../../node_modules/next/dist/server/async-storage/draft-mode-provider.d.ts", "../../node_modules/next/dist/server/web/spec-extension/adapters/headers.d.ts", "../../node_modules/next/dist/client/components/request-async-storage-instance.d.ts", "../../node_modules/next/dist/client/components/request-async-storage.external.d.ts", "../../node_modules/next/dist/server/app-render/create-error-handler.d.ts", "../../node_modules/next/dist/server/app-render/app-render.d.ts", "../../node_modules/next/dist/shared/lib/server-inserted-html.shared-runtime.d.ts", "../../node_modules/next/dist/shared/lib/amp-context.shared-runtime.d.ts", "../../node_modules/next/dist/server/future/route-modules/app-page/vendored/contexts/entrypoints.d.ts", "../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.d.ts", "../../node_modules/@types/react/jsx-runtime.d.ts", "../../node_modules/next/dist/client/components/error-boundary.d.ts", "../../node_modules/next/dist/client/components/router-reducer/create-initial-router-state.d.ts", "../../node_modules/next/dist/client/components/app-router.d.ts", "../../node_modules/next/dist/client/components/layout-router.d.ts", "../../node_modules/next/dist/client/components/render-from-template-context.d.ts", "../../node_modules/next/dist/client/components/action-async-storage-instance.d.ts", "../../node_modules/next/dist/client/components/action-async-storage.external.d.ts", "../../node_modules/next/dist/client/components/client-page.d.ts", "../../node_modules/next/dist/client/components/search-params.d.ts", "../../node_modules/next/dist/client/components/not-found-boundary.d.ts", "../../node_modules/next/dist/server/app-render/rsc/preloads.d.ts", "../../node_modules/next/dist/server/app-render/rsc/postpone.d.ts", "../../node_modules/next/dist/server/app-render/rsc/taint.d.ts", "../../node_modules/next/dist/server/app-render/entry-base.d.ts", "../../node_modules/next/dist/build/templates/app-page.d.ts", "../../node_modules/next/dist/server/future/route-modules/app-page/module.d.ts", "../../node_modules/next/dist/server/lib/builtin-request-context.d.ts", "../../node_modules/next/dist/server/app-render/types.d.ts", "../../node_modules/next/dist/client/components/router-reducer/fetch-server-response.d.ts", "../../node_modules/next/dist/client/components/router-reducer/router-reducer-types.d.ts", "../../node_modules/next/dist/shared/lib/app-router-context.shared-runtime.d.ts", "../../node_modules/next/dist/server/future/route-modules/pages/vendored/contexts/entrypoints.d.ts", "../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.d.ts", "../../node_modules/next/dist/build/templates/pages.d.ts", "../../node_modules/next/dist/server/future/route-modules/pages/module.d.ts", "../../node_modules/next/dist/server/render.d.ts", "../../node_modules/next/dist/server/future/route-definitions/pages-api-route-definition.d.ts", "../../node_modules/next/dist/server/future/route-matches/pages-api-route-match.d.ts", "../../node_modules/next/dist/server/future/route-matchers/route-matcher.d.ts", "../../node_modules/next/dist/server/future/route-matcher-providers/route-matcher-provider.d.ts", "../../node_modules/next/dist/server/future/route-matcher-managers/route-matcher-manager.d.ts", "../../node_modules/next/dist/server/future/normalizers/normalizer.d.ts", "../../node_modules/next/dist/server/future/normalizers/locale-route-normalizer.d.ts", "../../node_modules/next/dist/server/future/normalizers/request/pathname-normalizer.d.ts", "../../node_modules/next/dist/server/future/normalizers/request/suffix.d.ts", "../../node_modules/next/dist/server/future/normalizers/request/rsc.d.ts", "../../node_modules/next/dist/server/future/normalizers/request/prefix.d.ts", "../../node_modules/next/dist/server/future/normalizers/request/postponed.d.ts", "../../node_modules/next/dist/server/future/normalizers/request/action.d.ts", "../../node_modules/next/dist/server/future/normalizers/request/prefetch-rsc.d.ts", "../../node_modules/next/dist/server/future/normalizers/request/next-data.d.ts", "../../node_modules/next/dist/server/base-server.d.ts", "../../node_modules/next/dist/server/image-optimizer.d.ts", "../../node_modules/next/dist/server/next-server.d.ts", "../../node_modules/next/dist/lib/coalesced-function.d.ts", "../../node_modules/next/dist/server/lib/router-utils/types.d.ts", "../../node_modules/next/dist/trace/types.d.ts", "../../node_modules/next/dist/trace/trace.d.ts", "../../node_modules/next/dist/trace/shared.d.ts", "../../node_modules/next/dist/trace/index.d.ts", "../../node_modules/next/dist/build/load-jsconfig.d.ts", "../../node_modules/next/dist/build/webpack-config.d.ts", "../../node_modules/next/dist/build/webpack/plugins/define-env-plugin.d.ts", "../../node_modules/next/dist/build/swc/index.d.ts", "../../node_modules/next/dist/server/dev/parse-version-info.d.ts", "../../node_modules/next/dist/server/dev/hot-reloader-types.d.ts", "../../node_modules/next/dist/telemetry/storage.d.ts", "../../node_modules/next/dist/server/lib/types.d.ts", "../../node_modules/next/dist/server/lib/render-server.d.ts", "../../node_modules/next/dist/server/lib/router-server.d.ts", "../../node_modules/next/dist/shared/lib/router/utils/path-match.d.ts", "../../node_modules/next/dist/server/lib/router-utils/filesystem.d.ts", "../../node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.d.ts", "../../node_modules/next/dist/server/lib/dev-bundler-service.d.ts", "../../node_modules/next/dist/server/dev/static-paths-worker.d.ts", "../../node_modules/next/dist/server/dev/next-dev-server.d.ts", "../../node_modules/next/dist/server/next.d.ts", "../../node_modules/next/dist/lib/metadata/types/alternative-urls-types.d.ts", "../../node_modules/next/dist/lib/metadata/types/extra-types.d.ts", "../../node_modules/next/dist/lib/metadata/types/metadata-types.d.ts", "../../node_modules/next/dist/lib/metadata/types/manifest-types.d.ts", "../../node_modules/next/dist/lib/metadata/types/opengraph-types.d.ts", "../../node_modules/next/dist/lib/metadata/types/twitter-types.d.ts", "../../node_modules/next/dist/lib/metadata/types/metadata-interface.d.ts", "../../node_modules/next/types/index.d.ts", "../../node_modules/next/dist/shared/lib/html-context.shared-runtime.d.ts", "../../node_modules/@next/env/dist/index.d.ts", "../../node_modules/next/dist/shared/lib/utils.d.ts", "../../node_modules/next/dist/pages/_app.d.ts", "../../node_modules/next/app.d.ts", "../../node_modules/next/dist/server/web/spec-extension/unstable-cache.d.ts", "../../node_modules/next/dist/server/web/spec-extension/revalidate.d.ts", "../../node_modules/next/dist/server/web/spec-extension/unstable-no-store.d.ts", "../../node_modules/next/cache.d.ts", "../../node_modules/next/dist/shared/lib/runtime-config.external.d.ts", "../../node_modules/next/config.d.ts", "../../node_modules/next/dist/pages/_document.d.ts", "../../node_modules/next/document.d.ts", "../../node_modules/next/dist/shared/lib/dynamic.d.ts", "../../node_modules/next/dynamic.d.ts", "../../node_modules/next/dist/pages/_error.d.ts", "../../node_modules/next/error.d.ts", "../../node_modules/next/dist/shared/lib/head.d.ts", "../../node_modules/next/head.d.ts", "../../node_modules/next/dist/client/components/draft-mode.d.ts", "../../node_modules/next/dist/client/components/headers.d.ts", "../../node_modules/next/headers.d.ts", "../../node_modules/next/dist/shared/lib/get-img-props.d.ts", "../../node_modules/next/dist/client/image-component.d.ts", "../../node_modules/next/dist/shared/lib/image-external.d.ts", "../../node_modules/next/image.d.ts", "../../node_modules/next/dist/client/link.d.ts", "../../node_modules/next/link.d.ts", "../../node_modules/next/dist/client/components/redirect-status-code.d.ts", "../../node_modules/next/dist/client/components/redirect.d.ts", "../../node_modules/next/dist/client/components/not-found.d.ts", "../../node_modules/next/dist/client/components/navigation.react-server.d.ts", "../../node_modules/next/dist/client/components/navigation.d.ts", "../../node_modules/next/navigation.d.ts", "../../node_modules/next/router.d.ts", "../../node_modules/next/dist/client/script.d.ts", "../../node_modules/next/script.d.ts", "../../node_modules/next/dist/server/web/spec-extension/user-agent.d.ts", "../../node_modules/next/dist/compiled/@edge-runtime/primitives/url.d.ts", "../../node_modules/next/dist/server/web/spec-extension/image-response.d.ts", "../../node_modules/next/dist/compiled/@vercel/og/satori/index.d.ts", "../../node_modules/next/dist/compiled/@vercel/og/emoji/index.d.ts", "../../node_modules/next/dist/compiled/@vercel/og/types.d.ts", "../../node_modules/next/server.d.ts", "../../node_modules/next/types/global.d.ts", "../../node_modules/next/types/compiled.d.ts", "../../node_modules/next/index.d.ts", "../../node_modules/next/image-types/global.d.ts", "../../next-env.d.ts", "../../app/auth/authapi.ts", "../../node_modules/axios/index.d.ts", "../../utils/authheaders.ts", "../../app/dashboard/dashboardapi.ts", "../../node_modules/@tanstack/query-core/build/modern/removable.d.ts", "../../node_modules/@tanstack/query-core/build/modern/subscribable.d.ts", "../../node_modules/@tanstack/query-core/build/modern/hydration-cr-4kky1.d.ts", "../../node_modules/@tanstack/query-core/build/modern/queriesobserver.d.ts", "../../node_modules/@tanstack/query-core/build/modern/infinitequeryobserver.d.ts", "../../node_modules/@tanstack/query-core/build/modern/notifymanager.d.ts", "../../node_modules/@tanstack/query-core/build/modern/focusmanager.d.ts", "../../node_modules/@tanstack/query-core/build/modern/onlinemanager.d.ts", "../../node_modules/@tanstack/query-core/build/modern/streamedquery.d.ts", "../../node_modules/@tanstack/query-core/build/modern/index.d.ts", "../../node_modules/@tanstack/react-query/build/modern/types.d.ts", "../../node_modules/@tanstack/react-query/build/modern/usequeries.d.ts", "../../node_modules/@tanstack/react-query/build/modern/queryoptions.d.ts", "../../node_modules/@tanstack/react-query/build/modern/usequery.d.ts", "../../node_modules/@tanstack/react-query/build/modern/usesuspensequery.d.ts", "../../node_modules/@tanstack/react-query/build/modern/usesuspenseinfinitequery.d.ts", "../../node_modules/@tanstack/react-query/build/modern/usesuspensequeries.d.ts", "../../node_modules/@tanstack/react-query/build/modern/useprefetchquery.d.ts", "../../node_modules/@tanstack/react-query/build/modern/useprefetchinfinitequery.d.ts", "../../node_modules/@tanstack/react-query/build/modern/infinitequeryoptions.d.ts", "../../node_modules/@tanstack/react-query/build/modern/queryclientprovider.d.ts", "../../node_modules/@tanstack/react-query/build/modern/queryerrorresetboundary.d.ts", "../../node_modules/@tanstack/react-query/build/modern/hydrationboundary.d.ts", "../../node_modules/@tanstack/react-query/build/modern/useisfetching.d.ts", "../../node_modules/@tanstack/react-query/build/modern/usemutationstate.d.ts", "../../node_modules/@tanstack/react-query/build/modern/usemutation.d.ts", "../../node_modules/@tanstack/react-query/build/modern/useinfinitequery.d.ts", "../../node_modules/@tanstack/react-query/build/modern/isrestoringprovider.d.ts", "../../node_modules/@tanstack/react-query/build/modern/index.d.ts", "../../contexts/authcontext.tsx", "../../app/dashboard/hooks/usedashboarddata.ts", "../../app/inventory/products/productcategoriesapi.ts", "../../app/inventory/products/productsapi.ts", "../../app/inventory/stock/adjustments/api.ts", "../../app/inventory/stock-levels/api.ts", "../../app/inventory/stock-levels/hooks/usestocklevelsdata.ts", "../../app/logistics/regions/api.ts", "../../app/logistics/routes/api.ts", "../../app/logistics/vans/vansapi.ts", "../../app/logs/logsapi.ts", "../../app/logs/components/entitydisplayconfig.ts", "../../app/logs/components/examples/newentityexample.ts", "../../app/purchase/purchase/purchaseapi.ts", "../../app/purchase/purchase/purchasehelpers.ts", "../../node_modules/react-icons/lib/iconsmanifest.d.ts", "../../node_modules/react-icons/lib/iconbase.d.ts", "../../node_modules/react-icons/lib/iconcontext.d.ts", "../../node_modules/react-icons/lib/index.d.ts", "../../node_modules/react-icons/fi/index.d.ts", "../../app/purchase/purchase/components/purchaseheader.tsx", "../../app/purchase/purchase/components/purchasefilters.tsx", "../../app/purchase/purchase/components/purchaselistview.tsx", "../../app/purchase/purchase/components/purchasedetailsmodal.tsx", "../../app/purchase/purchase/components/purchasecancelmodal.tsx", "../../node_modules/sonner/dist/index.d.mts", "../../app/purchase/purchase/pos/styles/posstyles.ts", "../../app/purchase/purchase/pos/types/index.ts", "../../app/purchase/purchase/pos/components/searchbar.tsx", "../../app/purchase/purchase/pos/utils/poshelpers.ts", "../../app/purchase/purchase/pos/components/productlist.tsx", "../../app/purchase/purchase/pos/components/paymentselector.tsx", "../../app/purchase/purchase/pos/components/taxcontrols.tsx", "../../app/purchase/purchase/pos/components/notescontrols.tsx", "../../app/purchase/purchase/pos/components/purchasecart.tsx", "../../app/purchase/purchase/pos/components/quantitymodal.tsx", "../../node_modules/@heroicons/react/24/outline/academiccapicon.d.ts", "../../node_modules/@heroicons/react/24/outline/adjustmentshorizontalicon.d.ts", "../../node_modules/@heroicons/react/24/outline/adjustmentsverticalicon.d.ts", "../../node_modules/@heroicons/react/24/outline/archiveboxarrowdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/archiveboxxmarkicon.d.ts", "../../node_modules/@heroicons/react/24/outline/archiveboxicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowdowncircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowdownlefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowdownonsquarestackicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowdownonsquareicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowdownrighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowdowntrayicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowleftcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowleftendonrectangleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowleftonrectangleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowleftstartonrectangleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowlefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowlongdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowlonglefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowlongrighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowlongupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowpathroundedsquareicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowpathicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowrightcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowrightendonrectangleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowrightonrectangleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowrightstartonrectangleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowrighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowsmalldownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowsmalllefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowsmallrighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowsmallupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowtoprightonsquareicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowtrendingdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowtrendingupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowturndownlefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowturndownrighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowturnleftdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowturnleftupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowturnrightdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowturnrightupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowturnuplefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowturnuprighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowupcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowuplefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowuponsquarestackicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowuponsquareicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowuprighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowuptrayicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowuturndownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowuturnlefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowuturnrighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowuturnupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowspointinginicon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowspointingouticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowsrightlefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/arrowsupdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/atsymbolicon.d.ts", "../../node_modules/@heroicons/react/24/outline/backspaceicon.d.ts", "../../node_modules/@heroicons/react/24/outline/backwardicon.d.ts", "../../node_modules/@heroicons/react/24/outline/banknotesicon.d.ts", "../../node_modules/@heroicons/react/24/outline/bars2icon.d.ts", "../../node_modules/@heroicons/react/24/outline/bars3bottomlefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/bars3bottomrighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/bars3centerlefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/bars3icon.d.ts", "../../node_modules/@heroicons/react/24/outline/bars4icon.d.ts", "../../node_modules/@heroicons/react/24/outline/barsarrowdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/barsarrowupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/battery0icon.d.ts", "../../node_modules/@heroicons/react/24/outline/battery100icon.d.ts", "../../node_modules/@heroicons/react/24/outline/battery50icon.d.ts", "../../node_modules/@heroicons/react/24/outline/beakericon.d.ts", "../../node_modules/@heroicons/react/24/outline/bellalerticon.d.ts", "../../node_modules/@heroicons/react/24/outline/bellslashicon.d.ts", "../../node_modules/@heroicons/react/24/outline/bellsnoozeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/bellicon.d.ts", "../../node_modules/@heroicons/react/24/outline/boldicon.d.ts", "../../node_modules/@heroicons/react/24/outline/boltslashicon.d.ts", "../../node_modules/@heroicons/react/24/outline/bolticon.d.ts", "../../node_modules/@heroicons/react/24/outline/bookopenicon.d.ts", "../../node_modules/@heroicons/react/24/outline/bookmarkslashicon.d.ts", "../../node_modules/@heroicons/react/24/outline/bookmarksquareicon.d.ts", "../../node_modules/@heroicons/react/24/outline/bookmarkicon.d.ts", "../../node_modules/@heroicons/react/24/outline/briefcaseicon.d.ts", "../../node_modules/@heroicons/react/24/outline/buganticon.d.ts", "../../node_modules/@heroicons/react/24/outline/buildinglibraryicon.d.ts", "../../node_modules/@heroicons/react/24/outline/buildingoffice2icon.d.ts", "../../node_modules/@heroicons/react/24/outline/buildingofficeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/buildingstorefronticon.d.ts", "../../node_modules/@heroicons/react/24/outline/cakeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/calculatoricon.d.ts", "../../node_modules/@heroicons/react/24/outline/calendardaterangeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/calendardaysicon.d.ts", "../../node_modules/@heroicons/react/24/outline/calendaricon.d.ts", "../../node_modules/@heroicons/react/24/outline/cameraicon.d.ts", "../../node_modules/@heroicons/react/24/outline/chartbarsquareicon.d.ts", "../../node_modules/@heroicons/react/24/outline/chartbaricon.d.ts", "../../node_modules/@heroicons/react/24/outline/chartpieicon.d.ts", "../../node_modules/@heroicons/react/24/outline/chatbubblebottomcentertexticon.d.ts", "../../node_modules/@heroicons/react/24/outline/chatbubblebottomcentericon.d.ts", "../../node_modules/@heroicons/react/24/outline/chatbubbleleftellipsisicon.d.ts", "../../node_modules/@heroicons/react/24/outline/chatbubbleleftrighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/chatbubblelefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/chatbubbleovalleftellipsisicon.d.ts", "../../node_modules/@heroicons/react/24/outline/chatbubbleovallefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/checkbadgeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/checkcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/checkicon.d.ts", "../../node_modules/@heroicons/react/24/outline/chevrondoubledownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/chevrondoublelefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/chevrondoublerighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/chevrondoubleupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/chevrondownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/chevronlefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/chevronrighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/chevronupdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/chevronupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/circlestackicon.d.ts", "../../node_modules/@heroicons/react/24/outline/clipboarddocumentcheckicon.d.ts", "../../node_modules/@heroicons/react/24/outline/clipboarddocumentlisticon.d.ts", "../../node_modules/@heroicons/react/24/outline/clipboarddocumenticon.d.ts", "../../node_modules/@heroicons/react/24/outline/clipboardicon.d.ts", "../../node_modules/@heroicons/react/24/outline/clockicon.d.ts", "../../node_modules/@heroicons/react/24/outline/cloudarrowdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/cloudarrowupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/cloudicon.d.ts", "../../node_modules/@heroicons/react/24/outline/codebracketsquareicon.d.ts", "../../node_modules/@heroicons/react/24/outline/codebracketicon.d.ts", "../../node_modules/@heroicons/react/24/outline/cog6toothicon.d.ts", "../../node_modules/@heroicons/react/24/outline/cog8toothicon.d.ts", "../../node_modules/@heroicons/react/24/outline/cogicon.d.ts", "../../node_modules/@heroicons/react/24/outline/commandlineicon.d.ts", "../../node_modules/@heroicons/react/24/outline/computerdesktopicon.d.ts", "../../node_modules/@heroicons/react/24/outline/cpuchipicon.d.ts", "../../node_modules/@heroicons/react/24/outline/creditcardicon.d.ts", "../../node_modules/@heroicons/react/24/outline/cubetransparenticon.d.ts", "../../node_modules/@heroicons/react/24/outline/cubeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/currencybangladeshiicon.d.ts", "../../node_modules/@heroicons/react/24/outline/currencydollaricon.d.ts", "../../node_modules/@heroicons/react/24/outline/currencyeuroicon.d.ts", "../../node_modules/@heroicons/react/24/outline/currencypoundicon.d.ts", "../../node_modules/@heroicons/react/24/outline/currencyrupeeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/currencyyenicon.d.ts", "../../node_modules/@heroicons/react/24/outline/cursorarrowraysicon.d.ts", "../../node_modules/@heroicons/react/24/outline/cursorarrowrippleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/devicephonemobileicon.d.ts", "../../node_modules/@heroicons/react/24/outline/devicetableticon.d.ts", "../../node_modules/@heroicons/react/24/outline/divideicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentarrowdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentarrowupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentchartbaricon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentcheckicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentcurrencybangladeshiicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentcurrencydollaricon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentcurrencyeuroicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentcurrencypoundicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentcurrencyrupeeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentcurrencyyenicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentduplicateicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentmagnifyingglassicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentminusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documentplusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/documenttexticon.d.ts", "../../node_modules/@heroicons/react/24/outline/documenticon.d.ts", "../../node_modules/@heroicons/react/24/outline/ellipsishorizontalcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/ellipsishorizontalicon.d.ts", "../../node_modules/@heroicons/react/24/outline/ellipsisverticalicon.d.ts", "../../node_modules/@heroicons/react/24/outline/envelopeopenicon.d.ts", "../../node_modules/@heroicons/react/24/outline/envelopeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/equalsicon.d.ts", "../../node_modules/@heroicons/react/24/outline/exclamationcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/exclamationtriangleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/eyedroppericon.d.ts", "../../node_modules/@heroicons/react/24/outline/eyeslashicon.d.ts", "../../node_modules/@heroicons/react/24/outline/eyeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/facefrownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/facesmileicon.d.ts", "../../node_modules/@heroicons/react/24/outline/filmicon.d.ts", "../../node_modules/@heroicons/react/24/outline/fingerprinticon.d.ts", "../../node_modules/@heroicons/react/24/outline/fireicon.d.ts", "../../node_modules/@heroicons/react/24/outline/flagicon.d.ts", "../../node_modules/@heroicons/react/24/outline/folderarrowdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/folderminusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/folderopenicon.d.ts", "../../node_modules/@heroicons/react/24/outline/folderplusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/foldericon.d.ts", "../../node_modules/@heroicons/react/24/outline/forwardicon.d.ts", "../../node_modules/@heroicons/react/24/outline/funnelicon.d.ts", "../../node_modules/@heroicons/react/24/outline/gificon.d.ts", "../../node_modules/@heroicons/react/24/outline/gifttopicon.d.ts", "../../node_modules/@heroicons/react/24/outline/gifticon.d.ts", "../../node_modules/@heroicons/react/24/outline/globealticon.d.ts", "../../node_modules/@heroicons/react/24/outline/globeamericasicon.d.ts", "../../node_modules/@heroicons/react/24/outline/globeasiaaustraliaicon.d.ts", "../../node_modules/@heroicons/react/24/outline/globeeuropeafricaicon.d.ts", "../../node_modules/@heroicons/react/24/outline/h1icon.d.ts", "../../node_modules/@heroicons/react/24/outline/h2icon.d.ts", "../../node_modules/@heroicons/react/24/outline/h3icon.d.ts", "../../node_modules/@heroicons/react/24/outline/handraisedicon.d.ts", "../../node_modules/@heroicons/react/24/outline/handthumbdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/handthumbupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/hashtagicon.d.ts", "../../node_modules/@heroicons/react/24/outline/hearticon.d.ts", "../../node_modules/@heroicons/react/24/outline/homemodernicon.d.ts", "../../node_modules/@heroicons/react/24/outline/homeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/identificationicon.d.ts", "../../node_modules/@heroicons/react/24/outline/inboxarrowdownicon.d.ts", "../../node_modules/@heroicons/react/24/outline/inboxstackicon.d.ts", "../../node_modules/@heroicons/react/24/outline/inboxicon.d.ts", "../../node_modules/@heroicons/react/24/outline/informationcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/italicicon.d.ts", "../../node_modules/@heroicons/react/24/outline/keyicon.d.ts", "../../node_modules/@heroicons/react/24/outline/languageicon.d.ts", "../../node_modules/@heroicons/react/24/outline/lifebuoyicon.d.ts", "../../node_modules/@heroicons/react/24/outline/lightbulbicon.d.ts", "../../node_modules/@heroicons/react/24/outline/linkslashicon.d.ts", "../../node_modules/@heroicons/react/24/outline/linkicon.d.ts", "../../node_modules/@heroicons/react/24/outline/listbulleticon.d.ts", "../../node_modules/@heroicons/react/24/outline/lockclosedicon.d.ts", "../../node_modules/@heroicons/react/24/outline/lockopenicon.d.ts", "../../node_modules/@heroicons/react/24/outline/magnifyingglasscircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/magnifyingglassminusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/magnifyingglassplusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/magnifyingglassicon.d.ts", "../../node_modules/@heroicons/react/24/outline/mappinicon.d.ts", "../../node_modules/@heroicons/react/24/outline/mapicon.d.ts", "../../node_modules/@heroicons/react/24/outline/megaphoneicon.d.ts", "../../node_modules/@heroicons/react/24/outline/microphoneicon.d.ts", "../../node_modules/@heroicons/react/24/outline/minuscircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/minussmallicon.d.ts", "../../node_modules/@heroicons/react/24/outline/minusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/moonicon.d.ts", "../../node_modules/@heroicons/react/24/outline/musicalnoteicon.d.ts", "../../node_modules/@heroicons/react/24/outline/newspapericon.d.ts", "../../node_modules/@heroicons/react/24/outline/nosymbolicon.d.ts", "../../node_modules/@heroicons/react/24/outline/numberedlisticon.d.ts", "../../node_modules/@heroicons/react/24/outline/paintbrushicon.d.ts", "../../node_modules/@heroicons/react/24/outline/paperairplaneicon.d.ts", "../../node_modules/@heroicons/react/24/outline/paperclipicon.d.ts", "../../node_modules/@heroicons/react/24/outline/pausecircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/pauseicon.d.ts", "../../node_modules/@heroicons/react/24/outline/pencilsquareicon.d.ts", "../../node_modules/@heroicons/react/24/outline/pencilicon.d.ts", "../../node_modules/@heroicons/react/24/outline/percentbadgeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/phonearrowdownlefticon.d.ts", "../../node_modules/@heroicons/react/24/outline/phonearrowuprighticon.d.ts", "../../node_modules/@heroicons/react/24/outline/phonexmarkicon.d.ts", "../../node_modules/@heroicons/react/24/outline/phoneicon.d.ts", "../../node_modules/@heroicons/react/24/outline/photoicon.d.ts", "../../node_modules/@heroicons/react/24/outline/playcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/playpauseicon.d.ts", "../../node_modules/@heroicons/react/24/outline/playicon.d.ts", "../../node_modules/@heroicons/react/24/outline/pluscircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/plussmallicon.d.ts", "../../node_modules/@heroicons/react/24/outline/plusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/powericon.d.ts", "../../node_modules/@heroicons/react/24/outline/presentationchartbaricon.d.ts", "../../node_modules/@heroicons/react/24/outline/presentationchartlineicon.d.ts", "../../node_modules/@heroicons/react/24/outline/printericon.d.ts", "../../node_modules/@heroicons/react/24/outline/puzzlepieceicon.d.ts", "../../node_modules/@heroicons/react/24/outline/qrcodeicon.d.ts", "../../node_modules/@heroicons/react/24/outline/questionmarkcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/queuelisticon.d.ts", "../../node_modules/@heroicons/react/24/outline/radioicon.d.ts", "../../node_modules/@heroicons/react/24/outline/receiptpercenticon.d.ts", "../../node_modules/@heroicons/react/24/outline/receiptrefundicon.d.ts", "../../node_modules/@heroicons/react/24/outline/rectanglegroupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/rectanglestackicon.d.ts", "../../node_modules/@heroicons/react/24/outline/rocketlaunchicon.d.ts", "../../node_modules/@heroicons/react/24/outline/rssicon.d.ts", "../../node_modules/@heroicons/react/24/outline/scaleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/scissorsicon.d.ts", "../../node_modules/@heroicons/react/24/outline/serverstackicon.d.ts", "../../node_modules/@heroicons/react/24/outline/servericon.d.ts", "../../node_modules/@heroicons/react/24/outline/shareicon.d.ts", "../../node_modules/@heroicons/react/24/outline/shieldcheckicon.d.ts", "../../node_modules/@heroicons/react/24/outline/shieldexclamationicon.d.ts", "../../node_modules/@heroicons/react/24/outline/shoppingbagicon.d.ts", "../../node_modules/@heroicons/react/24/outline/shoppingcarticon.d.ts", "../../node_modules/@heroicons/react/24/outline/signalslashicon.d.ts", "../../node_modules/@heroicons/react/24/outline/signalicon.d.ts", "../../node_modules/@heroicons/react/24/outline/slashicon.d.ts", "../../node_modules/@heroicons/react/24/outline/sparklesicon.d.ts", "../../node_modules/@heroicons/react/24/outline/speakerwaveicon.d.ts", "../../node_modules/@heroicons/react/24/outline/speakerxmarkicon.d.ts", "../../node_modules/@heroicons/react/24/outline/square2stackicon.d.ts", "../../node_modules/@heroicons/react/24/outline/square3stack3dicon.d.ts", "../../node_modules/@heroicons/react/24/outline/squares2x2icon.d.ts", "../../node_modules/@heroicons/react/24/outline/squaresplusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/staricon.d.ts", "../../node_modules/@heroicons/react/24/outline/stopcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/stopicon.d.ts", "../../node_modules/@heroicons/react/24/outline/strikethroughicon.d.ts", "../../node_modules/@heroicons/react/24/outline/sunicon.d.ts", "../../node_modules/@heroicons/react/24/outline/swatchicon.d.ts", "../../node_modules/@heroicons/react/24/outline/tablecellsicon.d.ts", "../../node_modules/@heroicons/react/24/outline/tagicon.d.ts", "../../node_modules/@heroicons/react/24/outline/ticketicon.d.ts", "../../node_modules/@heroicons/react/24/outline/trashicon.d.ts", "../../node_modules/@heroicons/react/24/outline/trophyicon.d.ts", "../../node_modules/@heroicons/react/24/outline/truckicon.d.ts", "../../node_modules/@heroicons/react/24/outline/tvicon.d.ts", "../../node_modules/@heroicons/react/24/outline/underlineicon.d.ts", "../../node_modules/@heroicons/react/24/outline/usercircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/usergroupicon.d.ts", "../../node_modules/@heroicons/react/24/outline/userminusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/userplusicon.d.ts", "../../node_modules/@heroicons/react/24/outline/usericon.d.ts", "../../node_modules/@heroicons/react/24/outline/usersicon.d.ts", "../../node_modules/@heroicons/react/24/outline/variableicon.d.ts", "../../node_modules/@heroicons/react/24/outline/videocameraslashicon.d.ts", "../../node_modules/@heroicons/react/24/outline/videocameraicon.d.ts", "../../node_modules/@heroicons/react/24/outline/viewcolumnsicon.d.ts", "../../node_modules/@heroicons/react/24/outline/viewfindercircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/walleticon.d.ts", "../../node_modules/@heroicons/react/24/outline/wifiicon.d.ts", "../../node_modules/@heroicons/react/24/outline/windowicon.d.ts", "../../node_modules/@heroicons/react/24/outline/wrenchscrewdrivericon.d.ts", "../../node_modules/@heroicons/react/24/outline/wrenchicon.d.ts", "../../node_modules/@heroicons/react/24/outline/xcircleicon.d.ts", "../../node_modules/@heroicons/react/24/outline/xmarkicon.d.ts", "../../node_modules/@heroicons/react/24/outline/index.d.ts", "../../app/purchase/purchase/pos/components/pagination.tsx", "../../app/purchase/purchase/pos/components/categoryfilter.tsx", "../../app/purchase/purchase/pos/components/index.ts", "../../components/suppliermodal/types.ts", "../../components/suppliermodal/api/supplierapi.ts", "../../components/suppliermodal/hooks/usesupplierdata.ts", "../../components/suppliermodal/utils/supplierhelpers.ts", "../../components/suppliermodal/styles/suppliermodalstyles.ts", "../../components/suppliermodal/suppliermodal.tsx", "../../components/suppliermodal/index.ts", "../../app/purchase/purchase/pos/config/posconfig.ts", "../../app/purchase/purchase/pos/hooks/useposstate.ts", "../../app/purchase/purchase/pos/hooks/useposoperations.ts", "../../app/purchase/purchase/pos/hooks/useposproducts.ts", "../../app/purchase/purchase/pos/hooks/usekeyboardnavigation.ts", "../../app/purchase/purchase/pos/hooks/index.ts", "../../app/purchase/purchase/components/posview.tsx", "../../app/purchase/purchase/components/poscomponent.tsx", "../../app/purchase/purchase/components/listcomponent.tsx", "../../app/purchase/components/invoiceprint.tsx", "../../app/purchase/purchase/components/index.ts", "../../app/purchase/purchase/hooks/usepurchasedata.ts", "../../app/settings/companiesapi.ts", "../../app/purchasing/suppliers/suppliersapi.ts", "../../app/purchase/purchase/hooks/usepurchaseactions.ts", "../../app/purchase/purchase/hooks/usepurchaseposstate.ts", "../../app/purchase/purchase/hooks/index.ts", "../../app/settings/accountsettingsapi.ts", "../../app/purchase/purchase/pos/posapi.ts", "../../app/purchasing/suppliers/usesuppliers.ts", "../../node_modules/lucide-react/dist/lucide-react.d.ts", "../../app/purchasing/suppliers/components/searchandfilters.tsx", "../../app/purchasing/suppliers/components/suppliertableactions.tsx", "../../app/purchasing/suppliers/components/index.ts", "../../app/purchasing/suppliers/hooks/usesupplierfilters.ts", "../../app/purchasing/suppliers/hooks/usesupplieractions.ts", "../../app/purchasing/suppliers/hooks/index.ts", "../../app/sales/sales/salesapi.ts", "../../app/sales/components/invoiceprint.tsx", "../../app/sales/components/index.ts", "../../app/sales/customers/customerpaymentsapi.ts", "../../app/sales/customers/customersapi.ts", "../../app/sales/customers/usecustomerpayments.ts", "../../app/sales/customers/usecustomers.ts", "../../app/sales/customers/components/searchandfilters.tsx", "../../app/sales/customers/components/customertableactions.tsx", "../../app/sales/customers/components/index.ts", "../../app/sales/customers/hooks/usecustomerfilters.ts", "../../app/sales/sales/pos/config/posconfig.ts", "../../app/sales/customers/hooks/usecustomeractions.ts", "../../app/sales/customers/hooks/index.ts", "../../app/sales/sales/saleshelpers.ts", "../../app/sales/sales/components/salesheader.tsx", "../../components/customermodal/types/index.ts", "../../components/customermodal/api/customerapi.ts", "../../components/customermodal/hooks/usecustomerdata.ts", "../../components/customermodal/utils/customerhelpers.ts", "../../components/customermodal/styles/customermodalstyles.ts", "../../components/customermodal/customermodal.tsx", "../../components/customermodal/index.ts", "../../app/sales/sales/components/salesfilters.tsx", "../../components/itemstable/itemstable.tsx", "../../components/itemstable/tableactionbuttons.tsx", "../../app/sales/sales/pos/types/index.ts", "../../app/sales/sales/pos/utils/poshelpers.ts", "../../app/sales/sales/components/saleslistview.tsx", "../../app/sales/sales/components/saledetailsmodal.tsx", "../../app/sales/sales/components/salescancelmodal.tsx", "../../app/sales/sales/pos/styles/posstyles.ts", "../../app/sales/sales/pos/components/searchbar.tsx", "../../app/sales/sales/pos/components/productlist.tsx", "../../app/sales/sales/pos/components/paymentselector.tsx", "../../app/sales/sales/pos/components/taxcontrols.tsx", "../../app/sales/sales/pos/components/notescontrols.tsx", "../../app/sales/sales/pos/components/salescart.tsx", "../../app/sales/sales/pos/components/quantitymodal.tsx", "../../app/sales/sales/pos/components/pagination.tsx", "../../app/sales/sales/pos/components/index.ts", "../../app/sales/sales/pos/components/categoryfilter.tsx", "../../app/sales/sales/pos/hooks/usekeyboardnavigation.ts", "../../app/sales/sales/pos/hooks/useposstate.ts", "../../app/sales/sales/pos/posapi.ts", "../../app/sales/sales/pos/hooks/useposoperations.ts", "../../app/settings/warehousesapi.ts", "../../app/sales/sales/pos/hooks/useposproducts.ts", "../../app/sales/sales/pos/hooks/index.ts", "../../app/sales/sales/components/posview.tsx", "../../app/sales/sales/components/poscomponent.tsx", "../../app/sales/sales/components/listcomponent.tsx", "../../app/sales/sales/components/index.ts", "../../app/sales/sales/hooks/usesalesdata.ts", "../../app/sales/sales/hooks/usesalesactions.ts", "../../app/sales/sales/hooks/usesalesposstate.ts", "../../app/sales/sales/hooks/index.ts", "../../app/settings/roles/rolesapi.ts", "../../app/settings/users/usersapi.ts", "../../components/usermodal/usermodal.tsx", "../../components/usermodal/index.ts", "../../node_modules/@mui/types/esm/index.d.ts", "../../node_modules/@mui/material/esm/styles/identifier.d.ts", "../../node_modules/@emotion/sheet/dist/declarations/src/index.d.ts", "../../node_modules/@emotion/sheet/dist/emotion-sheet.cjs.d.mts", "../../node_modules/@emotion/utils/dist/declarations/src/types.d.ts", "../../node_modules/@emotion/utils/dist/declarations/src/index.d.ts", "../../node_modules/@emotion/utils/dist/emotion-utils.cjs.d.mts", "../../node_modules/@emotion/cache/dist/declarations/src/types.d.ts", "../../node_modules/@emotion/cache/dist/declarations/src/index.d.ts", "../../node_modules/@emotion/cache/dist/emotion-cache.cjs.default.d.ts", "../../node_modules/@emotion/cache/dist/emotion-cache.cjs.d.mts", "../../node_modules/@emotion/serialize/dist/declarations/src/index.d.ts", "../../node_modules/@emotion/serialize/dist/emotion-serialize.cjs.d.mts", "../../node_modules/@emotion/react/dist/declarations/src/context.d.ts", "../../node_modules/@emotion/react/dist/declarations/src/types.d.ts", "../../node_modules/@emotion/react/dist/declarations/src/theming.d.ts", "../../node_modules/@emotion/react/dist/declarations/src/jsx-namespace.d.ts", "../../node_modules/@emotion/react/dist/declarations/src/jsx.d.ts", "../../node_modules/@emotion/react/dist/declarations/src/global.d.ts", "../../node_modules/@emotion/react/dist/declarations/src/keyframes.d.ts", "../../node_modules/@emotion/react/dist/declarations/src/class-names.d.ts", "../../node_modules/@emotion/react/dist/declarations/src/css.d.ts", "../../node_modules/@emotion/react/dist/declarations/src/index.d.ts", "../../node_modules/@emotion/react/dist/emotion-react.cjs.d.mts", "../../node_modules/@emotion/styled/dist/declarations/src/jsx-namespace.d.ts", "../../node_modules/@emotion/styled/dist/declarations/src/types.d.ts", "../../node_modules/@emotion/styled/dist/declarations/src/index.d.ts", "../../node_modules/@emotion/styled/dist/emotion-styled.cjs.default.d.ts", "../../node_modules/@emotion/styled/dist/emotion-styled.cjs.d.mts", "../../node_modules/@mui/styled-engine/esm/styledengineprovider/styledengineprovider.d.ts", "../../node_modules/@mui/styled-engine/esm/styledengineprovider/index.d.ts", "../../node_modules/@mui/styled-engine/esm/globalstyles/globalstyles.d.ts", "../../node_modules/@mui/styled-engine/esm/globalstyles/index.d.ts", "../../node_modules/@mui/styled-engine/esm/index.d.ts", "../../node_modules/@mui/system/esm/style/style.d.ts", "../../node_modules/@mui/system/esm/style/index.d.ts", "../../node_modules/@mui/system/esm/borders/borders.d.ts", "../../node_modules/@mui/system/esm/borders/index.d.ts", "../../node_modules/@mui/system/esm/createbreakpoints/createbreakpoints.d.ts", "../../node_modules/@mui/system/esm/createtheme/shape.d.ts", "../../node_modules/@mui/system/esm/createtheme/createspacing.d.ts", "../../node_modules/@mui/system/esm/stylefunctionsx/standardcssproperties.d.ts", "../../node_modules/@mui/system/esm/stylefunctionsx/aliasescssproperties.d.ts", "../../node_modules/@mui/system/esm/stylefunctionsx/overwritecssproperties.d.ts", "../../node_modules/@mui/system/esm/stylefunctionsx/stylefunctionsx.d.ts", "../../node_modules/@mui/system/esm/stylefunctionsx/extendsxprop.d.ts", "../../node_modules/@mui/system/esm/stylefunctionsx/defaultsxconfig.d.ts", "../../node_modules/@mui/system/esm/stylefunctionsx/index.d.ts", "../../node_modules/@mui/system/esm/createtheme/applystyles.d.ts", "../../node_modules/@mui/system/esm/csscontainerqueries/csscontainerqueries.d.ts", "../../node_modules/@mui/system/esm/csscontainerqueries/index.d.ts", "../../node_modules/@mui/system/esm/createtheme/createtheme.d.ts", "../../node_modules/@mui/system/esm/createtheme/index.d.ts", "../../node_modules/@mui/system/esm/breakpoints/breakpoints.d.ts", "../../node_modules/@mui/system/esm/breakpoints/index.d.ts", "../../node_modules/@mui/system/esm/compose/compose.d.ts", "../../node_modules/@mui/system/esm/compose/index.d.ts", "../../node_modules/@mui/system/esm/display/display.d.ts", "../../node_modules/@mui/system/esm/display/index.d.ts", "../../node_modules/@mui/system/esm/flexbox/flexbox.d.ts", "../../node_modules/@mui/system/esm/flexbox/index.d.ts", "../../node_modules/@mui/system/esm/cssgrid/cssgrid.d.ts", "../../node_modules/@mui/system/esm/cssgrid/index.d.ts", "../../node_modules/@mui/system/esm/palette/palette.d.ts", "../../node_modules/@mui/system/esm/palette/index.d.ts", "../../node_modules/@mui/system/esm/positions/positions.d.ts", "../../node_modules/@mui/system/esm/positions/index.d.ts", "../../node_modules/@mui/system/esm/shadows/shadows.d.ts", "../../node_modules/@mui/system/esm/shadows/index.d.ts", "../../node_modules/@mui/system/esm/sizing/sizing.d.ts", "../../node_modules/@mui/system/esm/sizing/index.d.ts", "../../node_modules/@mui/system/esm/typography/typography.d.ts", "../../node_modules/@mui/system/esm/typography/index.d.ts", "../../node_modules/@mui/system/esm/getthemevalue/getthemevalue.d.ts", "../../node_modules/@mui/system/esm/getthemevalue/index.d.ts", "../../node_modules/@mui/private-theming/esm/defaulttheme/index.d.ts", "../../node_modules/@mui/private-theming/esm/themeprovider/themeprovider.d.ts", "../../node_modules/@mui/private-theming/esm/themeprovider/index.d.ts", "../../node_modules/@mui/private-theming/esm/usetheme/usetheme.d.ts", "../../node_modules/@mui/private-theming/esm/usetheme/index.d.ts", "../../node_modules/@mui/private-theming/esm/index.d.ts", "../../node_modules/@mui/system/esm/globalstyles/globalstyles.d.ts", "../../node_modules/@mui/system/esm/globalstyles/index.d.ts", "../../node_modules/@mui/system/esm/spacing/spacing.d.ts", "../../node_modules/@mui/system/esm/spacing/index.d.ts", "../../node_modules/@mui/system/esm/box/box.d.ts", "../../node_modules/@mui/system/esm/box/boxclasses.d.ts", "../../node_modules/@mui/system/esm/box/index.d.ts", "../../node_modules/@mui/system/esm/createbox/createbox.d.ts", "../../node_modules/@mui/system/esm/createbox/index.d.ts", "../../node_modules/@mui/system/esm/createstyled/createstyled.d.ts", "../../node_modules/@mui/system/esm/createstyled/index.d.ts", "../../node_modules/@mui/system/esm/styled/styled.d.ts", "../../node_modules/@mui/system/esm/styled/index.d.ts", "../../node_modules/@mui/system/esm/usethemeprops/usethemeprops.d.ts", "../../node_modules/@mui/system/esm/usethemeprops/getthemeprops.d.ts", "../../node_modules/@mui/system/esm/usethemeprops/index.d.ts", "../../node_modules/@mui/system/esm/usetheme/usetheme.d.ts", "../../node_modules/@mui/system/esm/usetheme/index.d.ts", "../../node_modules/@mui/system/esm/usethemewithoutdefault/usethemewithoutdefault.d.ts", "../../node_modules/@mui/system/esm/usethemewithoutdefault/index.d.ts", "../../node_modules/@mui/system/esm/usemediaquery/usemediaquery.d.ts", "../../node_modules/@mui/system/esm/usemediaquery/index.d.ts", "../../node_modules/@mui/system/esm/colormanipulator/colormanipulator.d.ts", "../../node_modules/@mui/system/esm/colormanipulator/index.d.ts", "../../node_modules/@mui/system/esm/themeprovider/themeprovider.d.ts", "../../node_modules/@mui/system/esm/themeprovider/index.d.ts", "../../node_modules/@mui/system/esm/memotheme.d.ts", "../../node_modules/@mui/system/esm/initcolorschemescript/initcolorschemescript.d.ts", "../../node_modules/@mui/system/esm/initcolorschemescript/index.d.ts", "../../node_modules/@mui/system/esm/cssvars/localstoragemanager.d.ts", "../../node_modules/@mui/system/esm/cssvars/usecurrentcolorscheme.d.ts", "../../node_modules/@mui/system/esm/cssvars/createcssvarsprovider.d.ts", "../../node_modules/@mui/system/esm/cssvars/preparecssvars.d.ts", "../../node_modules/@mui/system/esm/cssvars/preparetypographyvars.d.ts", "../../node_modules/@mui/system/esm/cssvars/createcssvarstheme.d.ts", "../../node_modules/@mui/system/esm/cssvars/getcolorschemeselector.d.ts", "../../node_modules/@mui/system/esm/cssvars/index.d.ts", "../../node_modules/@mui/system/esm/cssvars/creategetcssvar.d.ts", "../../node_modules/@mui/system/esm/cssvars/cssvarsparser.d.ts", "../../node_modules/@mui/system/esm/responsiveproptype/responsiveproptype.d.ts", "../../node_modules/@mui/system/esm/responsiveproptype/index.d.ts", "../../node_modules/@mui/system/esm/container/containerclasses.d.ts", "../../node_modules/@mui/system/esm/container/containerprops.d.ts", "../../node_modules/@mui/system/esm/container/createcontainer.d.ts", "../../node_modules/@mui/system/esm/container/container.d.ts", "../../node_modules/@mui/system/esm/container/index.d.ts", "../../node_modules/@mui/system/esm/grid/gridprops.d.ts", "../../node_modules/@mui/system/esm/grid/grid.d.ts", "../../node_modules/@mui/system/esm/grid/creategrid.d.ts", "../../node_modules/@mui/system/esm/grid/gridclasses.d.ts", "../../node_modules/@mui/system/esm/grid/traversebreakpoints.d.ts", "../../node_modules/@mui/system/esm/grid/gridgenerator.d.ts", "../../node_modules/@mui/system/esm/grid/index.d.ts", "../../node_modules/@mui/system/esm/stack/stackprops.d.ts", "../../node_modules/@mui/system/esm/stack/stack.d.ts", "../../node_modules/@mui/system/esm/stack/createstack.d.ts", "../../node_modules/@mui/system/esm/stack/stackclasses.d.ts", "../../node_modules/@mui/system/esm/stack/index.d.ts", "../../node_modules/@mui/system/esm/version/index.d.ts", "../../node_modules/@mui/system/esm/index.d.ts", "../../node_modules/@mui/material/esm/styles/createmixins.d.ts", "../../node_modules/@mui/material/esm/styles/createpalette.d.ts", "../../node_modules/@mui/material/esm/styles/createtypography.d.ts", "../../node_modules/@mui/material/esm/styles/shadows.d.ts", "../../node_modules/@mui/material/esm/styles/createtransitions.d.ts", "../../node_modules/@mui/material/esm/styles/zindex.d.ts", "../../node_modules/@mui/material/esm/overridablecomponent/index.d.ts", "../../node_modules/@mui/material/esm/svgicon/svgiconclasses.d.ts", "../../node_modules/@mui/material/esm/svgicon/svgicon.d.ts", "../../node_modules/@mui/material/esm/svgicon/index.d.ts", "../../node_modules/@mui/material/esm/internal/index.d.ts", "../../node_modules/@mui/material/esm/buttonbase/touchrippleclasses.d.ts", "../../node_modules/@mui/material/esm/buttonbase/touchripple.d.ts", "../../node_modules/@mui/material/esm/buttonbase/buttonbaseclasses.d.ts", "../../node_modules/@mui/material/esm/buttonbase/buttonbase.d.ts", "../../node_modules/@mui/material/esm/buttonbase/index.d.ts", "../../node_modules/@mui/material/esm/iconbutton/iconbuttonclasses.d.ts", "../../node_modules/@mui/material/esm/iconbutton/iconbutton.d.ts", "../../node_modules/@mui/material/esm/iconbutton/index.d.ts", "../../node_modules/@mui/material/esm/paper/paperclasses.d.ts", "../../node_modules/@mui/material/esm/paper/paper.d.ts", "../../node_modules/@mui/material/esm/paper/index.d.ts", "../../node_modules/@mui/material/esm/alert/alertclasses.d.ts", "../../node_modules/@mui/utils/esm/types/index.d.ts", "../../node_modules/@mui/material/esm/utils/types.d.ts", "../../node_modules/@mui/material/esm/alert/alert.d.ts", "../../node_modules/@mui/material/esm/alert/index.d.ts", "../../node_modules/@mui/material/esm/typography/typographyclasses.d.ts", "../../node_modules/@mui/material/esm/typography/typography.d.ts", "../../node_modules/@mui/material/esm/typography/index.d.ts", "../../node_modules/@mui/material/esm/alerttitle/alerttitleclasses.d.ts", "../../node_modules/@mui/material/esm/alerttitle/alerttitle.d.ts", "../../node_modules/@mui/material/esm/alerttitle/index.d.ts", "../../node_modules/@mui/material/esm/appbar/appbarclasses.d.ts", "../../node_modules/@mui/material/esm/appbar/appbar.d.ts", "../../node_modules/@mui/material/esm/appbar/index.d.ts", "../../node_modules/@mui/material/esm/chip/chipclasses.d.ts", "../../node_modules/@mui/material/esm/chip/chip.d.ts", "../../node_modules/@mui/material/esm/chip/index.d.ts", "../../node_modules/@popperjs/core/lib/enums.d.ts", "../../node_modules/@popperjs/core/lib/modifiers/popperoffsets.d.ts", "../../node_modules/@popperjs/core/lib/modifiers/flip.d.ts", "../../node_modules/@popperjs/core/lib/modifiers/hide.d.ts", "../../node_modules/@popperjs/core/lib/modifiers/offset.d.ts", "../../node_modules/@popperjs/core/lib/modifiers/eventlisteners.d.ts", "../../node_modules/@popperjs/core/lib/modifiers/computestyles.d.ts", "../../node_modules/@popperjs/core/lib/modifiers/arrow.d.ts", "../../node_modules/@popperjs/core/lib/modifiers/preventoverflow.d.ts", "../../node_modules/@popperjs/core/lib/modifiers/applystyles.d.ts", "../../node_modules/@popperjs/core/lib/types.d.ts", "../../node_modules/@popperjs/core/lib/modifiers/index.d.ts", "../../node_modules/@popperjs/core/lib/utils/detectoverflow.d.ts", "../../node_modules/@popperjs/core/lib/createpopper.d.ts", "../../node_modules/@popperjs/core/lib/popper-lite.d.ts", "../../node_modules/@popperjs/core/lib/popper.d.ts", "../../node_modules/@popperjs/core/lib/index.d.ts", "../../node_modules/@popperjs/core/index.d.ts", "../../node_modules/@mui/material/esm/portal/portal.types.d.ts", "../../node_modules/@mui/material/esm/portal/portal.d.ts", "../../node_modules/@mui/material/esm/portal/index.d.ts", "../../node_modules/@mui/material/esm/utils/polymorphiccomponent.d.ts", "../../node_modules/@mui/material/esm/popper/basepopper.types.d.ts", "../../node_modules/@mui/material/esm/popper/popper.d.ts", "../../node_modules/@mui/material/esm/popper/popperclasses.d.ts", "../../node_modules/@mui/material/esm/popper/index.d.ts", "../../node_modules/@mui/material/esm/useautocomplete/useautocomplete.d.ts", "../../node_modules/@mui/material/esm/useautocomplete/index.d.ts", "../../node_modules/@mui/material/esm/autocomplete/autocompleteclasses.d.ts", "../../node_modules/@mui/material/esm/autocomplete/autocomplete.d.ts", "../../node_modules/@mui/material/esm/autocomplete/index.d.ts", "../../node_modules/@mui/material/esm/avatar/avatarclasses.d.ts", "../../node_modules/@mui/material/esm/avatar/avatar.d.ts", "../../node_modules/@mui/material/esm/avatar/index.d.ts", "../../node_modules/@mui/material/esm/avatargroup/avatargroupclasses.d.ts", "../../node_modules/@mui/material/esm/avatargroup/avatargroup.d.ts", "../../node_modules/@mui/material/esm/avatargroup/index.d.ts", "../../node_modules/@types/react-transition-group/transition.d.ts", "../../node_modules/@mui/material/esm/transitions/transition.d.ts", "../../node_modules/@mui/material/esm/fade/fade.d.ts", "../../node_modules/@mui/material/esm/fade/index.d.ts", "../../node_modules/@mui/material/esm/backdrop/backdropclasses.d.ts", "../../node_modules/@mui/material/esm/backdrop/backdrop.d.ts", "../../node_modules/@mui/material/esm/backdrop/index.d.ts", "../../node_modules/@mui/material/esm/badge/badgeclasses.d.ts", "../../node_modules/@mui/material/esm/badge/badge.d.ts", "../../node_modules/@mui/material/esm/badge/index.d.ts", "../../node_modules/@mui/material/esm/bottomnavigationaction/bottomnavigationactionclasses.d.ts", "../../node_modules/@mui/material/esm/bottomnavigationaction/bottomnavigationaction.d.ts", "../../node_modules/@mui/material/esm/bottomnavigationaction/index.d.ts", "../../node_modules/@mui/material/esm/bottomnavigation/bottomnavigationclasses.d.ts", "../../node_modules/@mui/material/esm/bottomnavigation/bottomnavigation.d.ts", "../../node_modules/@mui/material/esm/bottomnavigation/index.d.ts", "../../node_modules/@mui/material/esm/breadcrumbs/breadcrumbsclasses.d.ts", "../../node_modules/@mui/material/esm/breadcrumbs/breadcrumbs.d.ts", "../../node_modules/@mui/material/esm/breadcrumbs/index.d.ts", "../../node_modules/@mui/material/esm/buttongroup/buttongroupclasses.d.ts", "../../node_modules/@mui/material/esm/buttongroup/buttongroup.d.ts", "../../node_modules/@mui/material/esm/buttongroup/buttongroupcontext.d.ts", "../../node_modules/@mui/material/esm/buttongroup/buttongroupbuttoncontext.d.ts", "../../node_modules/@mui/material/esm/buttongroup/index.d.ts", "../../node_modules/@mui/material/esm/button/buttonclasses.d.ts", "../../node_modules/@mui/material/esm/button/button.d.ts", "../../node_modules/@mui/material/esm/button/index.d.ts", "../../node_modules/@mui/material/esm/cardactionarea/cardactionareaclasses.d.ts", "../../node_modules/@mui/material/esm/cardactionarea/cardactionarea.d.ts", "../../node_modules/@mui/material/esm/cardactionarea/index.d.ts", "../../node_modules/@mui/material/esm/cardactions/cardactionsclasses.d.ts", "../../node_modules/@mui/material/esm/cardactions/cardactions.d.ts", "../../node_modules/@mui/material/esm/cardactions/index.d.ts", "../../node_modules/@mui/material/esm/cardcontent/cardcontentclasses.d.ts", "../../node_modules/@mui/material/esm/cardcontent/cardcontent.d.ts", "../../node_modules/@mui/material/esm/cardcontent/index.d.ts", "../../node_modules/@mui/material/esm/cardheader/cardheaderclasses.d.ts", "../../node_modules/@mui/material/esm/cardheader/cardheader.d.ts", "../../node_modules/@mui/material/esm/cardheader/index.d.ts", "../../node_modules/@mui/material/esm/cardmedia/cardmediaclasses.d.ts", "../../node_modules/@mui/material/esm/cardmedia/cardmedia.d.ts", "../../node_modules/@mui/material/esm/cardmedia/index.d.ts", "../../node_modules/@mui/material/esm/card/cardclasses.d.ts", "../../node_modules/@mui/material/esm/card/card.d.ts", "../../node_modules/@mui/material/esm/card/index.d.ts", "../../node_modules/@mui/material/esm/internal/switchbaseclasses.d.ts", "../../node_modules/@mui/material/esm/internal/switchbase.d.ts", "../../node_modules/@mui/material/esm/checkbox/checkboxclasses.d.ts", "../../node_modules/@mui/material/esm/checkbox/checkbox.d.ts", "../../node_modules/@mui/material/esm/checkbox/index.d.ts", "../../node_modules/@mui/material/esm/circularprogress/circularprogressclasses.d.ts", "../../node_modules/@mui/material/esm/circularprogress/circularprogress.d.ts", "../../node_modules/@mui/material/esm/circularprogress/index.d.ts", "../../node_modules/@mui/material/esm/collapse/collapseclasses.d.ts", "../../node_modules/@mui/material/esm/collapse/collapse.d.ts", "../../node_modules/@mui/material/esm/collapse/index.d.ts", "../../node_modules/@mui/material/esm/container/containerclasses.d.ts", "../../node_modules/@mui/material/esm/container/container.d.ts", "../../node_modules/@mui/material/esm/container/index.d.ts", "../../node_modules/@mui/material/esm/cssbaseline/cssbaseline.d.ts", "../../node_modules/@mui/material/esm/cssbaseline/index.d.ts", "../../node_modules/@mui/material/esm/dialogactions/dialogactionsclasses.d.ts", "../../node_modules/@mui/material/esm/dialogactions/dialogactions.d.ts", "../../node_modules/@mui/material/esm/dialogactions/index.d.ts", "../../node_modules/@mui/material/esm/dialogcontent/dialogcontentclasses.d.ts", "../../node_modules/@mui/material/esm/dialogcontent/dialogcontent.d.ts", "../../node_modules/@mui/material/esm/dialogcontent/index.d.ts", "../../node_modules/@mui/material/esm/dialogcontenttext/dialogcontenttextclasses.d.ts", "../../node_modules/@mui/material/esm/dialogcontenttext/dialogcontenttext.d.ts", "../../node_modules/@mui/material/esm/dialogcontenttext/index.d.ts", "../../node_modules/@mui/material/esm/modal/modalmanager.d.ts", "../../node_modules/@mui/material/esm/modal/modalclasses.d.ts", "../../node_modules/@mui/material/esm/modal/modal.d.ts", "../../node_modules/@mui/material/esm/modal/index.d.ts", "../../node_modules/@mui/material/esm/dialog/dialogclasses.d.ts", "../../node_modules/@mui/material/esm/dialog/dialog.d.ts", "../../node_modules/@mui/material/esm/dialog/index.d.ts", "../../node_modules/@mui/material/esm/dialogtitle/dialogtitleclasses.d.ts", "../../node_modules/@mui/material/esm/dialogtitle/dialogtitle.d.ts", "../../node_modules/@mui/material/esm/dialogtitle/index.d.ts", "../../node_modules/@mui/material/esm/divider/dividerclasses.d.ts", "../../node_modules/@mui/material/esm/divider/divider.d.ts", "../../node_modules/@mui/material/esm/divider/index.d.ts", "../../node_modules/@mui/material/esm/slide/slide.d.ts", "../../node_modules/@mui/material/esm/slide/index.d.ts", "../../node_modules/@mui/material/esm/drawer/drawerclasses.d.ts", "../../node_modules/@mui/material/esm/drawer/drawer.d.ts", "../../node_modules/@mui/material/esm/drawer/index.d.ts", "../../node_modules/@mui/material/esm/accordionactions/accordionactionsclasses.d.ts", "../../node_modules/@mui/material/esm/accordionactions/accordionactions.d.ts", "../../node_modules/@mui/material/esm/accordionactions/index.d.ts", "../../node_modules/@mui/material/esm/accordiondetails/accordiondetailsclasses.d.ts", "../../node_modules/@mui/material/esm/accordiondetails/accordiondetails.d.ts", "../../node_modules/@mui/material/esm/accordiondetails/index.d.ts", "../../node_modules/@mui/material/esm/accordion/accordionclasses.d.ts", "../../node_modules/@mui/material/esm/accordion/accordion.d.ts", "../../node_modules/@mui/material/esm/accordion/index.d.ts", "../../node_modules/@mui/material/esm/accordionsummary/accordionsummaryclasses.d.ts", "../../node_modules/@mui/material/esm/accordionsummary/accordionsummary.d.ts", "../../node_modules/@mui/material/esm/accordionsummary/index.d.ts", "../../node_modules/@mui/material/esm/fab/fabclasses.d.ts", "../../node_modules/@mui/material/esm/fab/fab.d.ts", "../../node_modules/@mui/material/esm/fab/index.d.ts", "../../node_modules/@mui/material/esm/inputbase/inputbaseclasses.d.ts", "../../node_modules/@mui/material/esm/inputbase/inputbase.d.ts", "../../node_modules/@mui/material/esm/inputbase/index.d.ts", "../../node_modules/@mui/material/esm/filledinput/filledinputclasses.d.ts", "../../node_modules/@mui/material/esm/filledinput/filledinput.d.ts", "../../node_modules/@mui/material/esm/filledinput/index.d.ts", "../../node_modules/@mui/material/esm/formcontrollabel/formcontrollabelclasses.d.ts", "../../node_modules/@mui/material/esm/formcontrollabel/formcontrollabel.d.ts", "../../node_modules/@mui/material/esm/formcontrollabel/index.d.ts", "../../node_modules/@mui/material/esm/formcontrol/formcontrolclasses.d.ts", "../../node_modules/@mui/material/esm/formcontrol/formcontrol.d.ts", "../../node_modules/@mui/material/esm/formcontrol/formcontrolcontext.d.ts", "../../node_modules/@mui/material/esm/formcontrol/useformcontrol.d.ts", "../../node_modules/@mui/material/esm/formcontrol/index.d.ts", "../../node_modules/@mui/material/esm/formgroup/formgroupclasses.d.ts", "../../node_modules/@mui/material/esm/formgroup/formgroup.d.ts", "../../node_modules/@mui/material/esm/formgroup/index.d.ts", "../../node_modules/@mui/material/esm/formhelpertext/formhelpertextclasses.d.ts", "../../node_modules/@mui/material/esm/formhelpertext/formhelpertext.d.ts", "../../node_modules/@mui/material/esm/formhelpertext/index.d.ts", "../../node_modules/@mui/material/esm/formlabel/formlabelclasses.d.ts", "../../node_modules/@mui/material/esm/formlabel/formlabel.d.ts", "../../node_modules/@mui/material/esm/formlabel/index.d.ts", "../../node_modules/@mui/material/esm/gridlegacy/gridlegacyclasses.d.ts", "../../node_modules/@mui/material/esm/gridlegacy/gridlegacy.d.ts", "../../node_modules/@mui/material/esm/gridlegacy/index.d.ts", "../../node_modules/@mui/material/esm/grid/grid.d.ts", "../../node_modules/@mui/material/esm/grid/gridclasses.d.ts", "../../node_modules/@mui/material/esm/grid/index.d.ts", "../../node_modules/@mui/material/esm/icon/iconclasses.d.ts", "../../node_modules/@mui/material/esm/icon/icon.d.ts", "../../node_modules/@mui/material/esm/icon/index.d.ts", "../../node_modules/@mui/material/esm/imagelist/imagelistclasses.d.ts", "../../node_modules/@mui/material/esm/imagelist/imagelist.d.ts", "../../node_modules/@mui/material/esm/imagelist/index.d.ts", "../../node_modules/@mui/material/esm/imagelistitembar/imagelistitembarclasses.d.ts", "../../node_modules/@mui/material/esm/imagelistitembar/imagelistitembar.d.ts", "../../node_modules/@mui/material/esm/imagelistitembar/index.d.ts", "../../node_modules/@mui/material/esm/imagelistitem/imagelistitemclasses.d.ts", "../../node_modules/@mui/material/esm/imagelistitem/imagelistitem.d.ts", "../../node_modules/@mui/material/esm/imagelistitem/index.d.ts", "../../node_modules/@mui/material/esm/inputadornment/inputadornmentclasses.d.ts", "../../node_modules/@mui/material/esm/inputadornment/inputadornment.d.ts", "../../node_modules/@mui/material/esm/inputadornment/index.d.ts", "../../node_modules/@mui/material/esm/inputlabel/inputlabelclasses.d.ts", "../../node_modules/@mui/material/esm/inputlabel/inputlabel.d.ts", "../../node_modules/@mui/material/esm/inputlabel/index.d.ts", "../../node_modules/@mui/material/esm/input/inputclasses.d.ts", "../../node_modules/@mui/material/esm/input/input.d.ts", "../../node_modules/@mui/material/esm/input/index.d.ts", "../../node_modules/@mui/material/esm/linearprogress/linearprogressclasses.d.ts", "../../node_modules/@mui/material/esm/linearprogress/linearprogress.d.ts", "../../node_modules/@mui/material/esm/linearprogress/index.d.ts", "../../node_modules/@mui/material/esm/link/linkclasses.d.ts", "../../node_modules/@mui/material/esm/link/link.d.ts", "../../node_modules/@mui/material/esm/link/index.d.ts", "../../node_modules/@mui/material/esm/listitemavatar/listitemavatarclasses.d.ts", "../../node_modules/@mui/material/esm/listitemavatar/listitemavatar.d.ts", "../../node_modules/@mui/material/esm/listitemavatar/index.d.ts", "../../node_modules/@mui/material/esm/listitemicon/listitemiconclasses.d.ts", "../../node_modules/@mui/material/esm/listitemicon/listitemicon.d.ts", "../../node_modules/@mui/material/esm/listitemicon/index.d.ts", "../../node_modules/@mui/material/esm/listitem/listitemclasses.d.ts", "../../node_modules/@mui/material/esm/listitem/listitem.d.ts", "../../node_modules/@mui/material/esm/listitem/index.d.ts", "../../node_modules/@mui/material/esm/listitembutton/listitembuttonclasses.d.ts", "../../node_modules/@mui/material/esm/listitembutton/listitembutton.d.ts", "../../node_modules/@mui/material/esm/listitembutton/index.d.ts", "../../node_modules/@mui/material/esm/listitemsecondaryaction/listitemsecondaryactionclasses.d.ts", "../../node_modules/@mui/material/esm/listitemsecondaryaction/listitemsecondaryaction.d.ts", "../../node_modules/@mui/material/esm/listitemsecondaryaction/index.d.ts", "../../node_modules/@mui/material/esm/listitemtext/listitemtextclasses.d.ts", "../../node_modules/@mui/material/esm/listitemtext/listitemtext.d.ts", "../../node_modules/@mui/material/esm/listitemtext/index.d.ts", "../../node_modules/@mui/material/esm/list/listclasses.d.ts", "../../node_modules/@mui/material/esm/list/list.d.ts", "../../node_modules/@mui/material/esm/list/index.d.ts", "../../node_modules/@mui/material/esm/listsubheader/listsubheaderclasses.d.ts", "../../node_modules/@mui/material/esm/listsubheader/listsubheader.d.ts", "../../node_modules/@mui/material/esm/listsubheader/index.d.ts", "../../node_modules/@mui/material/esm/menuitem/menuitemclasses.d.ts", "../../node_modules/@mui/material/esm/menuitem/menuitem.d.ts", "../../node_modules/@mui/material/esm/menuitem/index.d.ts", "../../node_modules/@mui/material/esm/menulist/menulist.d.ts", "../../node_modules/@mui/material/esm/menulist/index.d.ts", "../../node_modules/@mui/material/esm/popover/popoverclasses.d.ts", "../../node_modules/@mui/material/esm/popover/popover.d.ts", "../../node_modules/@mui/material/esm/popover/index.d.ts", "../../node_modules/@mui/material/esm/menu/menuclasses.d.ts", "../../node_modules/@mui/material/esm/menu/menu.d.ts", "../../node_modules/@mui/material/esm/menu/index.d.ts", "../../node_modules/@mui/material/esm/mobilestepper/mobilestepperclasses.d.ts", "../../node_modules/@mui/material/esm/mobilestepper/mobilestepper.d.ts", "../../node_modules/@mui/material/esm/mobilestepper/index.d.ts", "../../node_modules/@mui/material/esm/nativeselect/nativeselectinput.d.ts", "../../node_modules/@mui/material/esm/nativeselect/nativeselectclasses.d.ts", "../../node_modules/@mui/material/esm/nativeselect/nativeselect.d.ts", "../../node_modules/@mui/material/esm/nativeselect/index.d.ts", "../../node_modules/@mui/material/esm/usemediaquery/index.d.ts", "../../node_modules/@mui/material/esm/outlinedinput/outlinedinputclasses.d.ts", "../../node_modules/@mui/material/esm/outlinedinput/outlinedinput.d.ts", "../../node_modules/@mui/material/esm/outlinedinput/index.d.ts", "../../node_modules/@mui/material/esm/usepagination/usepagination.d.ts", "../../node_modules/@mui/material/esm/pagination/paginationclasses.d.ts", "../../node_modules/@mui/material/esm/pagination/pagination.d.ts", "../../node_modules/@mui/material/esm/pagination/index.d.ts", "../../node_modules/@mui/material/esm/paginationitem/paginationitemclasses.d.ts", "../../node_modules/@mui/material/esm/paginationitem/paginationitem.d.ts", "../../node_modules/@mui/material/esm/paginationitem/index.d.ts", "../../node_modules/@mui/material/esm/radiogroup/radiogroup.d.ts", "../../node_modules/@mui/material/esm/radiogroup/radiogroupcontext.d.ts", "../../node_modules/@mui/material/esm/radiogroup/useradiogroup.d.ts", "../../node_modules/@mui/material/esm/radiogroup/radiogroupclasses.d.ts", "../../node_modules/@mui/material/esm/radiogroup/index.d.ts", "../../node_modules/@mui/material/esm/radio/radioclasses.d.ts", "../../node_modules/@mui/material/esm/radio/radio.d.ts", "../../node_modules/@mui/material/esm/radio/index.d.ts", "../../node_modules/@mui/material/esm/rating/ratingclasses.d.ts", "../../node_modules/@mui/material/esm/rating/rating.d.ts", "../../node_modules/@mui/material/esm/rating/index.d.ts", "../../node_modules/@mui/material/esm/scopedcssbaseline/scopedcssbaselineclasses.d.ts", "../../node_modules/@mui/material/esm/scopedcssbaseline/scopedcssbaseline.d.ts", "../../node_modules/@mui/material/esm/scopedcssbaseline/index.d.ts", "../../node_modules/@mui/material/esm/select/selectinput.d.ts", "../../node_modules/@mui/material/esm/select/selectclasses.d.ts", "../../node_modules/@mui/material/esm/select/select.d.ts", "../../node_modules/@mui/material/esm/select/index.d.ts", "../../node_modules/@mui/material/esm/skeleton/skeletonclasses.d.ts", "../../node_modules/@mui/material/esm/skeleton/skeleton.d.ts", "../../node_modules/@mui/material/esm/skeleton/index.d.ts", "../../node_modules/@mui/material/esm/slider/useslider.types.d.ts", "../../node_modules/@mui/material/esm/slider/slidervaluelabel.types.d.ts", "../../node_modules/@mui/material/esm/slider/slidervaluelabel.d.ts", "../../node_modules/@mui/material/esm/slider/sliderclasses.d.ts", "../../node_modules/@mui/material/esm/slider/slider.d.ts", "../../node_modules/@mui/material/esm/slider/index.d.ts", "../../node_modules/@mui/material/esm/snackbarcontent/snackbarcontentclasses.d.ts", "../../node_modules/@mui/material/esm/snackbarcontent/snackbarcontent.d.ts", "../../node_modules/@mui/material/esm/snackbarcontent/index.d.ts", "../../node_modules/@mui/material/esm/clickawaylistener/clickawaylistener.d.ts", "../../node_modules/@mui/material/esm/clickawaylistener/index.d.ts", "../../node_modules/@mui/material/esm/snackbar/snackbarclasses.d.ts", "../../node_modules/@mui/material/esm/snackbar/snackbar.d.ts", "../../node_modules/@mui/material/esm/snackbar/index.d.ts", "../../node_modules/@mui/material/esm/transitions/index.d.ts", "../../node_modules/@mui/material/esm/speeddial/speeddialclasses.d.ts", "../../node_modules/@mui/material/esm/speeddial/speeddial.d.ts", "../../node_modules/@mui/material/esm/speeddial/index.d.ts", "../../node_modules/@mui/material/esm/tooltip/tooltipclasses.d.ts", "../../node_modules/@mui/material/esm/tooltip/tooltip.d.ts", "../../node_modules/@mui/material/esm/tooltip/index.d.ts", "../../node_modules/@mui/material/esm/speeddialaction/speeddialactionclasses.d.ts", "../../node_modules/@mui/material/esm/speeddialaction/speeddialaction.d.ts", "../../node_modules/@mui/material/esm/speeddialaction/index.d.ts", "../../node_modules/@mui/material/esm/speeddialicon/speeddialiconclasses.d.ts", "../../node_modules/@mui/material/esm/speeddialicon/speeddialicon.d.ts", "../../node_modules/@mui/material/esm/speeddialicon/index.d.ts", "../../node_modules/@mui/material/esm/stack/stack.d.ts", "../../node_modules/@mui/material/esm/stack/stackclasses.d.ts", "../../node_modules/@mui/material/esm/stack/index.d.ts", "../../node_modules/@mui/material/esm/stepbutton/stepbuttonclasses.d.ts", "../../node_modules/@mui/material/esm/stepbutton/stepbutton.d.ts", "../../node_modules/@mui/material/esm/stepbutton/index.d.ts", "../../node_modules/@mui/material/esm/stepconnector/stepconnectorclasses.d.ts", "../../node_modules/@mui/material/esm/stepconnector/stepconnector.d.ts", "../../node_modules/@mui/material/esm/stepconnector/index.d.ts", "../../node_modules/@mui/material/esm/stepcontent/stepcontentclasses.d.ts", "../../node_modules/@mui/material/esm/stepcontent/stepcontent.d.ts", "../../node_modules/@mui/material/esm/stepcontent/index.d.ts", "../../node_modules/@mui/material/esm/stepicon/stepiconclasses.d.ts", "../../node_modules/@mui/material/esm/stepicon/stepicon.d.ts", "../../node_modules/@mui/material/esm/stepicon/index.d.ts", "../../node_modules/@mui/material/esm/steplabel/steplabelclasses.d.ts", "../../node_modules/@mui/material/esm/steplabel/steplabel.d.ts", "../../node_modules/@mui/material/esm/steplabel/index.d.ts", "../../node_modules/@mui/material/esm/stepper/stepperclasses.d.ts", "../../node_modules/@mui/material/esm/stepper/stepper.d.ts", "../../node_modules/@mui/material/esm/stepper/steppercontext.d.ts", "../../node_modules/@mui/material/esm/stepper/index.d.ts", "../../node_modules/@mui/material/esm/step/stepclasses.d.ts", "../../node_modules/@mui/material/esm/step/step.d.ts", "../../node_modules/@mui/material/esm/step/stepcontext.d.ts", "../../node_modules/@mui/material/esm/step/index.d.ts", "../../node_modules/@mui/material/esm/swipeabledrawer/swipeabledrawer.d.ts", "../../node_modules/@mui/material/esm/swipeabledrawer/index.d.ts", "../../node_modules/@mui/material/esm/switch/switchclasses.d.ts", "../../node_modules/@mui/material/esm/switch/switch.d.ts", "../../node_modules/@mui/material/esm/switch/index.d.ts", "../../node_modules/@mui/material/esm/tablebody/tablebodyclasses.d.ts", "../../node_modules/@mui/material/esm/tablebody/tablebody.d.ts", "../../node_modules/@mui/material/esm/tablebody/index.d.ts", "../../node_modules/@mui/material/esm/tablecell/tablecellclasses.d.ts", "../../node_modules/@mui/material/esm/tablecell/tablecell.d.ts", "../../node_modules/@mui/material/esm/tablecell/index.d.ts", "../../node_modules/@mui/material/esm/tablecontainer/tablecontainerclasses.d.ts", "../../node_modules/@mui/material/esm/tablecontainer/tablecontainer.d.ts", "../../node_modules/@mui/material/esm/tablecontainer/index.d.ts", "../../node_modules/@mui/material/esm/tablehead/tableheadclasses.d.ts", "../../node_modules/@mui/material/esm/tablehead/tablehead.d.ts", "../../node_modules/@mui/material/esm/tablehead/index.d.ts", "../../node_modules/@mui/material/esm/tablepaginationactions/tablepaginationactions.d.ts", "../../node_modules/@mui/material/esm/tablepaginationactions/tablepaginationactionsclasses.d.ts", "../../node_modules/@mui/material/esm/tablepaginationactions/index.d.ts", "../../node_modules/@mui/material/esm/tablepagination/tablepaginationclasses.d.ts", "../../node_modules/@mui/material/esm/toolbar/toolbarclasses.d.ts", "../../node_modules/@mui/material/esm/toolbar/toolbar.d.ts", "../../node_modules/@mui/material/esm/toolbar/index.d.ts", "../../node_modules/@mui/material/esm/tablepagination/tablepagination.d.ts", "../../node_modules/@mui/material/esm/tablepagination/index.d.ts", "../../node_modules/@mui/material/esm/table/tableclasses.d.ts", "../../node_modules/@mui/material/esm/table/table.d.ts", "../../node_modules/@mui/material/esm/table/index.d.ts", "../../node_modules/@mui/material/esm/tablerow/tablerowclasses.d.ts", "../../node_modules/@mui/material/esm/tablerow/tablerow.d.ts", "../../node_modules/@mui/material/esm/tablerow/index.d.ts", "../../node_modules/@mui/material/esm/tablesortlabel/tablesortlabelclasses.d.ts", "../../node_modules/@mui/material/esm/tablesortlabel/tablesortlabel.d.ts", "../../node_modules/@mui/material/esm/tablesortlabel/index.d.ts", "../../node_modules/@mui/material/esm/tablefooter/tablefooterclasses.d.ts", "../../node_modules/@mui/material/esm/tablefooter/tablefooter.d.ts", "../../node_modules/@mui/material/esm/tablefooter/index.d.ts", "../../node_modules/@mui/material/esm/tab/tabclasses.d.ts", "../../node_modules/@mui/material/esm/tab/tab.d.ts", "../../node_modules/@mui/material/esm/tab/index.d.ts", "../../node_modules/@mui/material/esm/tabscrollbutton/tabscrollbuttonclasses.d.ts", "../../node_modules/@mui/material/esm/tabscrollbutton/tabscrollbutton.d.ts", "../../node_modules/@mui/material/esm/tabscrollbutton/index.d.ts", "../../node_modules/@mui/material/esm/tabs/tabsclasses.d.ts", "../../node_modules/@mui/material/esm/tabs/tabs.d.ts", "../../node_modules/@mui/material/esm/tabs/index.d.ts", "../../node_modules/@mui/material/esm/textfield/textfieldclasses.d.ts", "../../node_modules/@mui/material/esm/textfield/textfield.d.ts", "../../node_modules/@mui/material/esm/textfield/index.d.ts", "../../node_modules/@mui/material/esm/togglebutton/togglebuttonclasses.d.ts", "../../node_modules/@mui/material/esm/togglebutton/togglebutton.d.ts", "../../node_modules/@mui/material/esm/togglebutton/index.d.ts", "../../node_modules/@mui/material/esm/togglebuttongroup/togglebuttongroupclasses.d.ts", "../../node_modules/@mui/material/esm/togglebuttongroup/togglebuttongroup.d.ts", "../../node_modules/@mui/material/esm/togglebuttongroup/index.d.ts", "../../node_modules/@mui/material/esm/styles/props.d.ts", "../../node_modules/@mui/material/esm/styles/overrides.d.ts", "../../node_modules/@mui/material/esm/styles/variants.d.ts", "../../node_modules/@mui/material/esm/styles/components.d.ts", "../../node_modules/@mui/material/esm/styles/createthemenovars.d.ts", "../../node_modules/@mui/material/esm/styles/createthemewithvars.d.ts", "../../node_modules/@mui/material/esm/styles/createtheme.d.ts", "../../node_modules/@mui/material/esm/styles/adaptv4theme.d.ts", "../../node_modules/@mui/material/esm/styles/createcolorscheme.d.ts", "../../node_modules/@mui/material/esm/styles/createstyles.d.ts", "../../node_modules/@mui/material/esm/styles/responsivefontsizes.d.ts", "../../node_modules/@mui/system/esm/createbreakpoints/index.d.ts", "../../node_modules/@mui/material/esm/styles/usetheme.d.ts", "../../node_modules/@mui/material/esm/styles/usethemeprops.d.ts", "../../node_modules/@mui/material/esm/styles/slotshouldforwardprop.d.ts", "../../node_modules/@mui/material/esm/styles/rootshouldforwardprop.d.ts", "../../node_modules/@mui/material/esm/styles/styled.d.ts", "../../node_modules/@mui/material/esm/styles/themeprovider.d.ts", "../../node_modules/@mui/material/esm/styles/cssutils.d.ts", "../../node_modules/@mui/material/esm/styles/makestyles.d.ts", "../../node_modules/@mui/material/esm/styles/withstyles.d.ts", "../../node_modules/@mui/material/esm/styles/withtheme.d.ts", "../../node_modules/@mui/material/esm/styles/themeproviderwithvars.d.ts", "../../node_modules/@mui/material/esm/styles/getoverlayalpha.d.ts", "../../node_modules/@mui/material/esm/styles/shouldskipgeneratingvar.d.ts", "../../node_modules/@mui/material/esm/styles/excludevariablesfromroot.d.ts", "../../node_modules/@mui/material/esm/styles/index.d.ts", "../../components/dynamictable/dynamictablestyles.tsx", "../../components/dynamictable/dynamictablelogic.ts", "../../styles/themes.ts", "../../utils/networkutils.ts", "../../node_modules/next/dist/compiled/@next/font/dist/types.d.ts", "../../node_modules/next/dist/compiled/@next/font/dist/google/index.d.ts", "../../node_modules/next/font/google/index.d.ts", "../../contexts/themecontext.tsx", "../../app/providers.tsx", "../../app/layout.tsx", "../../components/sidetaskbar/sidetaskbar.tsx", "../../app/page.tsx", "../../components/backendstatuschecker.tsx", "../../components/networkstatuschecker.tsx", "../../app/auth/page.tsx", "../../app/auth/callback/page.tsx", "../../components/toptaskbar/toptaskbar.tsx", "../../app/dashboard/layout.tsx", "../../app/dashboard/components/dashboardcard.tsx", "../../app/dashboard/components/quickactionbutton.tsx", "../../app/dashboard/components/recentactivity.tsx", "../../app/dashboard/page.tsx", "../../app/inventory/layout.tsx", "../../node_modules/react-hook-form/dist/constants.d.ts", "../../node_modules/react-hook-form/dist/utils/createsubject.d.ts", "../../node_modules/react-hook-form/dist/types/events.d.ts", "../../node_modules/react-hook-form/dist/types/path/common.d.ts", "../../node_modules/react-hook-form/dist/types/path/eager.d.ts", "../../node_modules/react-hook-form/dist/types/path/index.d.ts", "../../node_modules/react-hook-form/dist/types/fieldarray.d.ts", "../../node_modules/react-hook-form/dist/types/resolvers.d.ts", "../../node_modules/react-hook-form/dist/types/form.d.ts", "../../node_modules/react-hook-form/dist/types/utils.d.ts", "../../node_modules/react-hook-form/dist/types/fields.d.ts", "../../node_modules/react-hook-form/dist/types/errors.d.ts", "../../node_modules/react-hook-form/dist/types/validator.d.ts", "../../node_modules/react-hook-form/dist/types/controller.d.ts", "../../node_modules/react-hook-form/dist/types/index.d.ts", "../../node_modules/react-hook-form/dist/controller.d.ts", "../../node_modules/react-hook-form/dist/form.d.ts", "../../node_modules/react-hook-form/dist/logic/appenderrors.d.ts", "../../node_modules/react-hook-form/dist/logic/createformcontrol.d.ts", "../../node_modules/react-hook-form/dist/logic/index.d.ts", "../../node_modules/react-hook-form/dist/usecontroller.d.ts", "../../node_modules/react-hook-form/dist/usefieldarray.d.ts", "../../node_modules/react-hook-form/dist/useform.d.ts", "../../node_modules/react-hook-form/dist/useformcontext.d.ts", "../../node_modules/react-hook-form/dist/useformstate.d.ts", "../../node_modules/react-hook-form/dist/usewatch.d.ts", "../../node_modules/react-hook-form/dist/utils/get.d.ts", "../../node_modules/react-hook-form/dist/utils/set.d.ts", "../../node_modules/react-hook-form/dist/utils/index.d.ts", "../../node_modules/react-hook-form/dist/index.d.ts", "../../node_modules/zod/dist/types/v3/helpers/typealiases.d.ts", "../../node_modules/zod/dist/types/v3/helpers/util.d.ts", "../../node_modules/zod/dist/types/v3/zoderror.d.ts", "../../node_modules/zod/dist/types/v3/locales/en.d.ts", "../../node_modules/zod/dist/types/v3/errors.d.ts", "../../node_modules/zod/dist/types/v3/helpers/parseutil.d.ts", "../../node_modules/zod/dist/types/v3/helpers/enumutil.d.ts", "../../node_modules/zod/dist/types/v3/helpers/errorutil.d.ts", "../../node_modules/zod/dist/types/v3/helpers/partialutil.d.ts", "../../node_modules/zod/dist/types/v3/standard-schema.d.ts", "../../node_modules/zod/dist/types/v3/types.d.ts", "../../node_modules/zod/dist/types/v3/external.d.ts", "../../node_modules/zod/dist/types/v3/index.d.ts", "../../node_modules/zod/dist/types/index.d.ts", "../../node_modules/zod/dist/types/v4/core/standard-schema.d.ts", "../../node_modules/zod/dist/types/v4/core/util.d.ts", "../../node_modules/zod/dist/types/v4/core/versions.d.ts", "../../node_modules/zod/dist/types/v4/core/schemas.d.ts", "../../node_modules/zod/dist/types/v4/core/checks.d.ts", "../../node_modules/zod/dist/types/v4/core/errors.d.ts", "../../node_modules/zod/dist/types/v4/core/core.d.ts", "../../node_modules/zod/dist/types/v4/core/parse.d.ts", "../../node_modules/zod/dist/types/v4/core/regexes.d.ts", "../../node_modules/zod/dist/types/v4/locales/ar.d.ts", "../../node_modules/zod/dist/types/v4/locales/az.d.ts", "../../node_modules/zod/dist/types/v4/locales/be.d.ts", "../../node_modules/zod/dist/types/v4/locales/ca.d.ts", "../../node_modules/zod/dist/types/v4/locales/cs.d.ts", "../../node_modules/zod/dist/types/v4/locales/de.d.ts", "../../node_modules/zod/dist/types/v4/locales/en.d.ts", "../../node_modules/zod/dist/types/v4/locales/es.d.ts", "../../node_modules/zod/dist/types/v4/locales/fa.d.ts", "../../node_modules/zod/dist/types/v4/locales/fi.d.ts", "../../node_modules/zod/dist/types/v4/locales/fr.d.ts", "../../node_modules/zod/dist/types/v4/locales/fr-ca.d.ts", "../../node_modules/zod/dist/types/v4/locales/he.d.ts", "../../node_modules/zod/dist/types/v4/locales/hu.d.ts", "../../node_modules/zod/dist/types/v4/locales/id.d.ts", "../../node_modules/zod/dist/types/v4/locales/it.d.ts", "../../node_modules/zod/dist/types/v4/locales/ja.d.ts", "../../node_modules/zod/dist/types/v4/locales/kh.d.ts", "../../node_modules/zod/dist/types/v4/locales/ko.d.ts", "../../node_modules/zod/dist/types/v4/locales/mk.d.ts", "../../node_modules/zod/dist/types/v4/locales/ms.d.ts", "../../node_modules/zod/dist/types/v4/locales/nl.d.ts", "../../node_modules/zod/dist/types/v4/locales/no.d.ts", "../../node_modules/zod/dist/types/v4/locales/ota.d.ts", "../../node_modules/zod/dist/types/v4/locales/ps.d.ts", "../../node_modules/zod/dist/types/v4/locales/pl.d.ts", "../../node_modules/zod/dist/types/v4/locales/pt.d.ts", "../../node_modules/zod/dist/types/v4/locales/ru.d.ts", "../../node_modules/zod/dist/types/v4/locales/sl.d.ts", "../../node_modules/zod/dist/types/v4/locales/sv.d.ts", "../../node_modules/zod/dist/types/v4/locales/ta.d.ts", "../../node_modules/zod/dist/types/v4/locales/th.d.ts", "../../node_modules/zod/dist/types/v4/locales/tr.d.ts", "../../node_modules/zod/dist/types/v4/locales/ua.d.ts", "../../node_modules/zod/dist/types/v4/locales/ur.d.ts", "../../node_modules/zod/dist/types/v4/locales/vi.d.ts", "../../node_modules/zod/dist/types/v4/locales/zh-cn.d.ts", "../../node_modules/zod/dist/types/v4/locales/zh-tw.d.ts", "../../node_modules/zod/dist/types/v4/locales/index.d.ts", "../../node_modules/zod/dist/types/v4/core/registries.d.ts", "../../node_modules/zod/dist/types/v4/core/doc.d.ts", "../../node_modules/zod/dist/types/v4/core/function.d.ts", "../../node_modules/zod/dist/types/v4/core/api.d.ts", "../../node_modules/zod/dist/types/v4/core/json-schema.d.ts", "../../node_modules/zod/dist/types/v4/core/to-json-schema.d.ts", "../../node_modules/zod/dist/types/v4/core/index.d.ts", "../../node_modules/@hookform/resolvers/zod/dist/zod.d.ts", "../../node_modules/@hookform/resolvers/zod/dist/index.d.ts", "../../app/inventory/products/productform.tsx", "../../app/inventory/products/productmodal.tsx", "../../app/inventory/products/create.tsx", "../../app/inventory/products/page.tsx", "../../app/inventory/products/[uuid]/edit.tsx", "../../app/inventory/products/categories/page.tsx", "../../node_modules/goober/goober.d.ts", "../../node_modules/react-hot-toast/dist/index.d.ts", "../../app/inventory/stock/adjustments/components/productselectionmodal.tsx", "../../app/inventory/stock/adjustments/page.tsx", "../../app/inventory/stock/alerts/page.tsx", "../../app/inventory/stock/levels/page.tsx", "../../app/inventory/stock/movements/page.tsx", "../../app/inventory/stock/transfers/page.tsx", "../../app/inventory/stock/vans/page.tsx", "../../app/inventory/stock-levels/page.tsx", "../../app/logistics/layout.tsx", "../../app/logistics/regions/regionform.tsx", "../../components/protectedroute.tsx", "../../app/logistics/regions/page.tsx", "../../node_modules/@types/geojson/index.d.ts", "../../node_modules/@types/leaflet/index.d.ts", "../../node_modules/react-leaflet/lib/hooks.d.ts", "../../node_modules/react-leaflet/lib/attributioncontrol.d.ts", "../../node_modules/@react-leaflet/core/lib/attribution.d.ts", "../../node_modules/@react-leaflet/core/lib/context.d.ts", "../../node_modules/@react-leaflet/core/lib/element.d.ts", "../../node_modules/@react-leaflet/core/lib/events.d.ts", "../../node_modules/@react-leaflet/core/lib/layer.d.ts", "../../node_modules/@react-leaflet/core/lib/path.d.ts", "../../node_modules/@react-leaflet/core/lib/circle.d.ts", "../../node_modules/@react-leaflet/core/lib/div-overlay.d.ts", "../../node_modules/@react-leaflet/core/lib/component.d.ts", "../../node_modules/@react-leaflet/core/lib/control.d.ts", "../../node_modules/@react-leaflet/core/lib/dom.d.ts", "../../node_modules/@react-leaflet/core/lib/generic.d.ts", "../../node_modules/@react-leaflet/core/lib/grid-layer.d.ts", "../../node_modules/@react-leaflet/core/lib/media-overlay.d.ts", "../../node_modules/@react-leaflet/core/lib/pane.d.ts", "../../node_modules/@react-leaflet/core/lib/index.d.ts", "../../node_modules/react-leaflet/lib/circle.d.ts", "../../node_modules/react-leaflet/lib/circlemarker.d.ts", "../../node_modules/react-leaflet/lib/layergroup.d.ts", "../../node_modules/react-leaflet/lib/featuregroup.d.ts", "../../node_modules/react-leaflet/lib/geojson.d.ts", "../../node_modules/react-leaflet/lib/imageoverlay.d.ts", "../../node_modules/react-leaflet/lib/layerscontrol.d.ts", "../../node_modules/react-leaflet/lib/mapcontainer.d.ts", "../../node_modules/react-leaflet/lib/marker.d.ts", "../../node_modules/react-leaflet/lib/pane.d.ts", "../../node_modules/react-leaflet/lib/polygon.d.ts", "../../node_modules/react-leaflet/lib/polyline.d.ts", "../../node_modules/react-leaflet/lib/popup.d.ts", "../../node_modules/react-leaflet/lib/rectangle.d.ts", "../../node_modules/react-leaflet/lib/scalecontrol.d.ts", "../../node_modules/react-leaflet/lib/svgoverlay.d.ts", "../../node_modules/react-leaflet/lib/tilelayer.d.ts", "../../node_modules/react-leaflet/lib/tooltip.d.ts", "../../node_modules/react-leaflet/lib/videooverlay.d.ts", "../../node_modules/react-leaflet/lib/wmstilelayer.d.ts", "../../node_modules/react-leaflet/lib/zoomcontrol.d.ts", "../../node_modules/react-leaflet/lib/index.d.ts", "../../app/logistics/routes/components/routemap.tsx", "../../app/logistics/routes/components/computeroutemodal.tsx", "../../app/logistics/routes/components/routelist.tsx", "../../app/logistics/routes/page.tsx", "../../app/logistics/van-loading/page.tsx", "../../app/logistics/van-stock/page.tsx", "../../app/logistics/vans/vanform.tsx", "../../app/logistics/vans/vanmodal.tsx", "../../app/logistics/vans/page.tsx", "../../app/logistics/warehouses/page.tsx", "../../app/logs/layout.tsx", "../../app/logs/components/logdetailsmodal.tsx", "../../app/logs/page.tsx", "../../app/logs/test-diff/page.tsx", "../../app/purchase/layout.tsx", "../../app/purchase/page.tsx", "../../app/purchasing/layout.tsx", "../../app/purchasing/goods-receipt/page.tsx", "../../app/purchasing/orders/page.tsx", "../../app/purchasing/returns/page.tsx", "../../app/purchasing/suppliers/components/suppliermodal.tsx", "../../app/purchasing/suppliers/page.tsx", "../../app/reports/layout.tsx", "../../app/reports/financial/page.tsx", "../../app/reports/inventory/page.tsx", "../../app/reports/sales/page.tsx", "../../app/reports/van-performance/page.tsx", "../../app/sales/layout.tsx", "../../app/sales/customers/customerdetailsmodal.tsx", "../../app/sales/customers/customermodal.tsx", "../../app/sales/customers/page.tsx", "../../app/sales/customers/locations/customermap.tsx", "../../app/sales/customers/locations/page.tsx", "../../app/sales/customers/payments/customerpaymentdetailsmodal.tsx", "../../app/sales/customers/payments/customerpaymentmodal.tsx", "../../app/sales/customers/payments/page.tsx", "../../app/sales/sales/page.tsx", "../../app/settings/layout.tsx", "../../app/settings/data/page.tsx", "../../components/errortoast.tsx", "../../app/settings/profile/page.tsx", "../../app/settings/roles/page.tsx", "../../app/settings/system/page.tsx", "../../node_modules/@heroicons/react/20/solid/academiccapicon.d.ts", "../../node_modules/@heroicons/react/20/solid/adjustmentshorizontalicon.d.ts", "../../node_modules/@heroicons/react/20/solid/adjustmentsverticalicon.d.ts", "../../node_modules/@heroicons/react/20/solid/archiveboxarrowdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/archiveboxxmarkicon.d.ts", "../../node_modules/@heroicons/react/20/solid/archiveboxicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowdowncircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowdownlefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowdownonsquarestackicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowdownonsquareicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowdownrighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowdowntrayicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowleftcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowleftendonrectangleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowleftonrectangleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowleftstartonrectangleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowlefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowlongdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowlonglefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowlongrighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowlongupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowpathroundedsquareicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowpathicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowrightcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowrightendonrectangleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowrightonrectangleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowrightstartonrectangleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowrighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowsmalldownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowsmalllefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowsmallrighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowsmallupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowtoprightonsquareicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowtrendingdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowtrendingupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowturndownlefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowturndownrighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowturnleftdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowturnleftupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowturnrightdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowturnrightupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowturnuplefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowturnuprighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowupcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowuplefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowuponsquarestackicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowuponsquareicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowuprighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowuptrayicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowuturndownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowuturnlefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowuturnrighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowuturnupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowspointinginicon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowspointingouticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowsrightlefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/arrowsupdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/atsymbolicon.d.ts", "../../node_modules/@heroicons/react/20/solid/backspaceicon.d.ts", "../../node_modules/@heroicons/react/20/solid/backwardicon.d.ts", "../../node_modules/@heroicons/react/20/solid/banknotesicon.d.ts", "../../node_modules/@heroicons/react/20/solid/bars2icon.d.ts", "../../node_modules/@heroicons/react/20/solid/bars3bottomlefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/bars3bottomrighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/bars3centerlefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/bars3icon.d.ts", "../../node_modules/@heroicons/react/20/solid/bars4icon.d.ts", "../../node_modules/@heroicons/react/20/solid/barsarrowdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/barsarrowupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/battery0icon.d.ts", "../../node_modules/@heroicons/react/20/solid/battery100icon.d.ts", "../../node_modules/@heroicons/react/20/solid/battery50icon.d.ts", "../../node_modules/@heroicons/react/20/solid/beakericon.d.ts", "../../node_modules/@heroicons/react/20/solid/bellalerticon.d.ts", "../../node_modules/@heroicons/react/20/solid/bellslashicon.d.ts", "../../node_modules/@heroicons/react/20/solid/bellsnoozeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/bellicon.d.ts", "../../node_modules/@heroicons/react/20/solid/boldicon.d.ts", "../../node_modules/@heroicons/react/20/solid/boltslashicon.d.ts", "../../node_modules/@heroicons/react/20/solid/bolticon.d.ts", "../../node_modules/@heroicons/react/20/solid/bookopenicon.d.ts", "../../node_modules/@heroicons/react/20/solid/bookmarkslashicon.d.ts", "../../node_modules/@heroicons/react/20/solid/bookmarksquareicon.d.ts", "../../node_modules/@heroicons/react/20/solid/bookmarkicon.d.ts", "../../node_modules/@heroicons/react/20/solid/briefcaseicon.d.ts", "../../node_modules/@heroicons/react/20/solid/buganticon.d.ts", "../../node_modules/@heroicons/react/20/solid/buildinglibraryicon.d.ts", "../../node_modules/@heroicons/react/20/solid/buildingoffice2icon.d.ts", "../../node_modules/@heroicons/react/20/solid/buildingofficeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/buildingstorefronticon.d.ts", "../../node_modules/@heroicons/react/20/solid/cakeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/calculatoricon.d.ts", "../../node_modules/@heroicons/react/20/solid/calendardaterangeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/calendardaysicon.d.ts", "../../node_modules/@heroicons/react/20/solid/calendaricon.d.ts", "../../node_modules/@heroicons/react/20/solid/cameraicon.d.ts", "../../node_modules/@heroicons/react/20/solid/chartbarsquareicon.d.ts", "../../node_modules/@heroicons/react/20/solid/chartbaricon.d.ts", "../../node_modules/@heroicons/react/20/solid/chartpieicon.d.ts", "../../node_modules/@heroicons/react/20/solid/chatbubblebottomcentertexticon.d.ts", "../../node_modules/@heroicons/react/20/solid/chatbubblebottomcentericon.d.ts", "../../node_modules/@heroicons/react/20/solid/chatbubbleleftellipsisicon.d.ts", "../../node_modules/@heroicons/react/20/solid/chatbubbleleftrighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/chatbubblelefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/chatbubbleovalleftellipsisicon.d.ts", "../../node_modules/@heroicons/react/20/solid/chatbubbleovallefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/checkbadgeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/checkcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/checkicon.d.ts", "../../node_modules/@heroicons/react/20/solid/chevrondoubledownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/chevrondoublelefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/chevrondoublerighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/chevrondoubleupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/chevrondownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/chevronlefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/chevronrighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/chevronupdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/chevronupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/circlestackicon.d.ts", "../../node_modules/@heroicons/react/20/solid/clipboarddocumentcheckicon.d.ts", "../../node_modules/@heroicons/react/20/solid/clipboarddocumentlisticon.d.ts", "../../node_modules/@heroicons/react/20/solid/clipboarddocumenticon.d.ts", "../../node_modules/@heroicons/react/20/solid/clipboardicon.d.ts", "../../node_modules/@heroicons/react/20/solid/clockicon.d.ts", "../../node_modules/@heroicons/react/20/solid/cloudarrowdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/cloudarrowupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/cloudicon.d.ts", "../../node_modules/@heroicons/react/20/solid/codebracketsquareicon.d.ts", "../../node_modules/@heroicons/react/20/solid/codebracketicon.d.ts", "../../node_modules/@heroicons/react/20/solid/cog6toothicon.d.ts", "../../node_modules/@heroicons/react/20/solid/cog8toothicon.d.ts", "../../node_modules/@heroicons/react/20/solid/cogicon.d.ts", "../../node_modules/@heroicons/react/20/solid/commandlineicon.d.ts", "../../node_modules/@heroicons/react/20/solid/computerdesktopicon.d.ts", "../../node_modules/@heroicons/react/20/solid/cpuchipicon.d.ts", "../../node_modules/@heroicons/react/20/solid/creditcardicon.d.ts", "../../node_modules/@heroicons/react/20/solid/cubetransparenticon.d.ts", "../../node_modules/@heroicons/react/20/solid/cubeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/currencybangladeshiicon.d.ts", "../../node_modules/@heroicons/react/20/solid/currencydollaricon.d.ts", "../../node_modules/@heroicons/react/20/solid/currencyeuroicon.d.ts", "../../node_modules/@heroicons/react/20/solid/currencypoundicon.d.ts", "../../node_modules/@heroicons/react/20/solid/currencyrupeeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/currencyyenicon.d.ts", "../../node_modules/@heroicons/react/20/solid/cursorarrowraysicon.d.ts", "../../node_modules/@heroicons/react/20/solid/cursorarrowrippleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/devicephonemobileicon.d.ts", "../../node_modules/@heroicons/react/20/solid/devicetableticon.d.ts", "../../node_modules/@heroicons/react/20/solid/divideicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentarrowdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentarrowupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentchartbaricon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentcheckicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentcurrencybangladeshiicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentcurrencydollaricon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentcurrencyeuroicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentcurrencypoundicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentcurrencyrupeeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentcurrencyyenicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentduplicateicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentmagnifyingglassicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentminusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documentplusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/documenttexticon.d.ts", "../../node_modules/@heroicons/react/20/solid/documenticon.d.ts", "../../node_modules/@heroicons/react/20/solid/ellipsishorizontalcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/ellipsishorizontalicon.d.ts", "../../node_modules/@heroicons/react/20/solid/ellipsisverticalicon.d.ts", "../../node_modules/@heroicons/react/20/solid/envelopeopenicon.d.ts", "../../node_modules/@heroicons/react/20/solid/envelopeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/equalsicon.d.ts", "../../node_modules/@heroicons/react/20/solid/exclamationcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/exclamationtriangleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/eyedroppericon.d.ts", "../../node_modules/@heroicons/react/20/solid/eyeslashicon.d.ts", "../../node_modules/@heroicons/react/20/solid/eyeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/facefrownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/facesmileicon.d.ts", "../../node_modules/@heroicons/react/20/solid/filmicon.d.ts", "../../node_modules/@heroicons/react/20/solid/fingerprinticon.d.ts", "../../node_modules/@heroicons/react/20/solid/fireicon.d.ts", "../../node_modules/@heroicons/react/20/solid/flagicon.d.ts", "../../node_modules/@heroicons/react/20/solid/folderarrowdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/folderminusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/folderopenicon.d.ts", "../../node_modules/@heroicons/react/20/solid/folderplusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/foldericon.d.ts", "../../node_modules/@heroicons/react/20/solid/forwardicon.d.ts", "../../node_modules/@heroicons/react/20/solid/funnelicon.d.ts", "../../node_modules/@heroicons/react/20/solid/gificon.d.ts", "../../node_modules/@heroicons/react/20/solid/gifttopicon.d.ts", "../../node_modules/@heroicons/react/20/solid/gifticon.d.ts", "../../node_modules/@heroicons/react/20/solid/globealticon.d.ts", "../../node_modules/@heroicons/react/20/solid/globeamericasicon.d.ts", "../../node_modules/@heroicons/react/20/solid/globeasiaaustraliaicon.d.ts", "../../node_modules/@heroicons/react/20/solid/globeeuropeafricaicon.d.ts", "../../node_modules/@heroicons/react/20/solid/h1icon.d.ts", "../../node_modules/@heroicons/react/20/solid/h2icon.d.ts", "../../node_modules/@heroicons/react/20/solid/h3icon.d.ts", "../../node_modules/@heroicons/react/20/solid/handraisedicon.d.ts", "../../node_modules/@heroicons/react/20/solid/handthumbdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/handthumbupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/hashtagicon.d.ts", "../../node_modules/@heroicons/react/20/solid/hearticon.d.ts", "../../node_modules/@heroicons/react/20/solid/homemodernicon.d.ts", "../../node_modules/@heroicons/react/20/solid/homeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/identificationicon.d.ts", "../../node_modules/@heroicons/react/20/solid/inboxarrowdownicon.d.ts", "../../node_modules/@heroicons/react/20/solid/inboxstackicon.d.ts", "../../node_modules/@heroicons/react/20/solid/inboxicon.d.ts", "../../node_modules/@heroicons/react/20/solid/informationcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/italicicon.d.ts", "../../node_modules/@heroicons/react/20/solid/keyicon.d.ts", "../../node_modules/@heroicons/react/20/solid/languageicon.d.ts", "../../node_modules/@heroicons/react/20/solid/lifebuoyicon.d.ts", "../../node_modules/@heroicons/react/20/solid/lightbulbicon.d.ts", "../../node_modules/@heroicons/react/20/solid/linkslashicon.d.ts", "../../node_modules/@heroicons/react/20/solid/linkicon.d.ts", "../../node_modules/@heroicons/react/20/solid/listbulleticon.d.ts", "../../node_modules/@heroicons/react/20/solid/lockclosedicon.d.ts", "../../node_modules/@heroicons/react/20/solid/lockopenicon.d.ts", "../../node_modules/@heroicons/react/20/solid/magnifyingglasscircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/magnifyingglassminusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/magnifyingglassplusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/magnifyingglassicon.d.ts", "../../node_modules/@heroicons/react/20/solid/mappinicon.d.ts", "../../node_modules/@heroicons/react/20/solid/mapicon.d.ts", "../../node_modules/@heroicons/react/20/solid/megaphoneicon.d.ts", "../../node_modules/@heroicons/react/20/solid/microphoneicon.d.ts", "../../node_modules/@heroicons/react/20/solid/minuscircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/minussmallicon.d.ts", "../../node_modules/@heroicons/react/20/solid/minusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/moonicon.d.ts", "../../node_modules/@heroicons/react/20/solid/musicalnoteicon.d.ts", "../../node_modules/@heroicons/react/20/solid/newspapericon.d.ts", "../../node_modules/@heroicons/react/20/solid/nosymbolicon.d.ts", "../../node_modules/@heroicons/react/20/solid/numberedlisticon.d.ts", "../../node_modules/@heroicons/react/20/solid/paintbrushicon.d.ts", "../../node_modules/@heroicons/react/20/solid/paperairplaneicon.d.ts", "../../node_modules/@heroicons/react/20/solid/paperclipicon.d.ts", "../../node_modules/@heroicons/react/20/solid/pausecircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/pauseicon.d.ts", "../../node_modules/@heroicons/react/20/solid/pencilsquareicon.d.ts", "../../node_modules/@heroicons/react/20/solid/pencilicon.d.ts", "../../node_modules/@heroicons/react/20/solid/percentbadgeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/phonearrowdownlefticon.d.ts", "../../node_modules/@heroicons/react/20/solid/phonearrowuprighticon.d.ts", "../../node_modules/@heroicons/react/20/solid/phonexmarkicon.d.ts", "../../node_modules/@heroicons/react/20/solid/phoneicon.d.ts", "../../node_modules/@heroicons/react/20/solid/photoicon.d.ts", "../../node_modules/@heroicons/react/20/solid/playcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/playpauseicon.d.ts", "../../node_modules/@heroicons/react/20/solid/playicon.d.ts", "../../node_modules/@heroicons/react/20/solid/pluscircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/plussmallicon.d.ts", "../../node_modules/@heroicons/react/20/solid/plusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/powericon.d.ts", "../../node_modules/@heroicons/react/20/solid/presentationchartbaricon.d.ts", "../../node_modules/@heroicons/react/20/solid/presentationchartlineicon.d.ts", "../../node_modules/@heroicons/react/20/solid/printericon.d.ts", "../../node_modules/@heroicons/react/20/solid/puzzlepieceicon.d.ts", "../../node_modules/@heroicons/react/20/solid/qrcodeicon.d.ts", "../../node_modules/@heroicons/react/20/solid/questionmarkcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/queuelisticon.d.ts", "../../node_modules/@heroicons/react/20/solid/radioicon.d.ts", "../../node_modules/@heroicons/react/20/solid/receiptpercenticon.d.ts", "../../node_modules/@heroicons/react/20/solid/receiptrefundicon.d.ts", "../../node_modules/@heroicons/react/20/solid/rectanglegroupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/rectanglestackicon.d.ts", "../../node_modules/@heroicons/react/20/solid/rocketlaunchicon.d.ts", "../../node_modules/@heroicons/react/20/solid/rssicon.d.ts", "../../node_modules/@heroicons/react/20/solid/scaleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/scissorsicon.d.ts", "../../node_modules/@heroicons/react/20/solid/serverstackicon.d.ts", "../../node_modules/@heroicons/react/20/solid/servericon.d.ts", "../../node_modules/@heroicons/react/20/solid/shareicon.d.ts", "../../node_modules/@heroicons/react/20/solid/shieldcheckicon.d.ts", "../../node_modules/@heroicons/react/20/solid/shieldexclamationicon.d.ts", "../../node_modules/@heroicons/react/20/solid/shoppingbagicon.d.ts", "../../node_modules/@heroicons/react/20/solid/shoppingcarticon.d.ts", "../../node_modules/@heroicons/react/20/solid/signalslashicon.d.ts", "../../node_modules/@heroicons/react/20/solid/signalicon.d.ts", "../../node_modules/@heroicons/react/20/solid/slashicon.d.ts", "../../node_modules/@heroicons/react/20/solid/sparklesicon.d.ts", "../../node_modules/@heroicons/react/20/solid/speakerwaveicon.d.ts", "../../node_modules/@heroicons/react/20/solid/speakerxmarkicon.d.ts", "../../node_modules/@heroicons/react/20/solid/square2stackicon.d.ts", "../../node_modules/@heroicons/react/20/solid/square3stack3dicon.d.ts", "../../node_modules/@heroicons/react/20/solid/squares2x2icon.d.ts", "../../node_modules/@heroicons/react/20/solid/squaresplusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/staricon.d.ts", "../../node_modules/@heroicons/react/20/solid/stopcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/stopicon.d.ts", "../../node_modules/@heroicons/react/20/solid/strikethroughicon.d.ts", "../../node_modules/@heroicons/react/20/solid/sunicon.d.ts", "../../node_modules/@heroicons/react/20/solid/swatchicon.d.ts", "../../node_modules/@heroicons/react/20/solid/tablecellsicon.d.ts", "../../node_modules/@heroicons/react/20/solid/tagicon.d.ts", "../../node_modules/@heroicons/react/20/solid/ticketicon.d.ts", "../../node_modules/@heroicons/react/20/solid/trashicon.d.ts", "../../node_modules/@heroicons/react/20/solid/trophyicon.d.ts", "../../node_modules/@heroicons/react/20/solid/truckicon.d.ts", "../../node_modules/@heroicons/react/20/solid/tvicon.d.ts", "../../node_modules/@heroicons/react/20/solid/underlineicon.d.ts", "../../node_modules/@heroicons/react/20/solid/usercircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/usergroupicon.d.ts", "../../node_modules/@heroicons/react/20/solid/userminusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/userplusicon.d.ts", "../../node_modules/@heroicons/react/20/solid/usericon.d.ts", "../../node_modules/@heroicons/react/20/solid/usersicon.d.ts", "../../node_modules/@heroicons/react/20/solid/variableicon.d.ts", "../../node_modules/@heroicons/react/20/solid/videocameraslashicon.d.ts", "../../node_modules/@heroicons/react/20/solid/videocameraicon.d.ts", "../../node_modules/@heroicons/react/20/solid/viewcolumnsicon.d.ts", "../../node_modules/@heroicons/react/20/solid/viewfindercircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/walleticon.d.ts", "../../node_modules/@heroicons/react/20/solid/wifiicon.d.ts", "../../node_modules/@heroicons/react/20/solid/windowicon.d.ts", "../../node_modules/@heroicons/react/20/solid/wrenchscrewdrivericon.d.ts", "../../node_modules/@heroicons/react/20/solid/wrenchicon.d.ts", "../../node_modules/@heroicons/react/20/solid/xcircleicon.d.ts", "../../node_modules/@heroicons/react/20/solid/xmarkicon.d.ts", "../../node_modules/@heroicons/react/20/solid/index.d.ts", "../../app/settings/users/components/usertable.tsx", "../../node_modules/@headlessui/react/dist/types.d.ts", "../../node_modules/@headlessui/react/dist/utils/render.d.ts", "../../node_modules/@headlessui/react/dist/components/button/button.d.ts", "../../node_modules/@headlessui/react/dist/components/checkbox/checkbox.d.ts", "../../node_modules/@headlessui/react/dist/components/close-button/close-button.d.ts", "../../node_modules/@headlessui/react/dist/hooks/use-by-comparator.d.ts", "../../node_modules/@floating-ui/utils/dist/floating-ui.utils.d.mts", "../../node_modules/@floating-ui/core/dist/floating-ui.core.d.mts", "../../node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.d.mts", "../../node_modules/@floating-ui/dom/dist/floating-ui.dom.d.mts", "../../node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.d.mts", "../../node_modules/@floating-ui/react/dist/floating-ui.react.d.mts", "../../node_modules/@headlessui/react/dist/internal/floating.d.ts", "../../node_modules/@headlessui/react/dist/components/label/label.d.ts", "../../node_modules/@headlessui/react/dist/components/combobox/combobox.d.ts", "../../node_modules/@headlessui/react/dist/components/data-interactive/data-interactive.d.ts", "../../node_modules/@headlessui/react/dist/components/description/description.d.ts", "../../node_modules/@headlessui/react/dist/components/dialog/dialog.d.ts", "../../node_modules/@headlessui/react/dist/components/disclosure/disclosure.d.ts", "../../node_modules/@headlessui/react/dist/components/field/field.d.ts", "../../node_modules/@headlessui/react/dist/components/fieldset/fieldset.d.ts", "../../node_modules/@headlessui/react/dist/components/focus-trap/focus-trap.d.ts", "../../node_modules/@headlessui/react/dist/components/input/input.d.ts", "../../node_modules/@headlessui/react/dist/components/legend/legend.d.ts", "../../node_modules/@headlessui/react/dist/components/listbox/listbox.d.ts", "../../node_modules/@headlessui/react/dist/components/menu/menu.d.ts", "../../node_modules/@headlessui/react/dist/components/popover/popover.d.ts", "../../node_modules/@headlessui/react/dist/components/portal/portal.d.ts", "../../node_modules/@headlessui/react/dist/components/radio-group/radio-group.d.ts", "../../node_modules/@headlessui/react/dist/components/select/select.d.ts", "../../node_modules/@headlessui/react/dist/components/switch/switch.d.ts", "../../node_modules/@headlessui/react/dist/components/tabs/tabs.d.ts", "../../node_modules/@headlessui/react/dist/components/textarea/textarea.d.ts", "../../node_modules/@headlessui/react/dist/internal/close-provider.d.ts", "../../node_modules/@headlessui/react/dist/components/transition/transition.d.ts", "../../node_modules/@headlessui/react/dist/index.d.ts", "../../app/settings/users/components/addusermodal.tsx", "../../app/settings/users/components/editusermodal.tsx", "../../app/settings/users/components/deleteuserdialog.tsx", "../../node_modules/@types/qrcode/index.d.ts", "../../app/settings/users/components/userdetailsmodal.tsx", "../../app/settings/users/page.tsx", "../../components/dynamictable/dynamictable.tsx", "../../components/itemstable/loadingdemo.tsx", "../types/app/page.ts", "../types/app/auth/page.ts", "../types/app/auth/callback/page.ts", "../types/app/dashboard/layout.ts", "../types/app/dashboard/page.ts", "../types/app/inventory/layout.ts", "../types/app/inventory/products/page.ts", "../types/app/inventory/products/categories/page.ts", "../types/app/inventory/stock/adjustments/page.ts", "../types/app/inventory/stock/alerts/page.ts", "../types/app/inventory/stock/levels/page.ts", "../types/app/inventory/stock/movements/page.ts", "../types/app/inventory/stock/transfers/page.ts", "../types/app/inventory/stock/vans/page.ts", "../types/app/inventory/stock-levels/page.ts", "../types/app/logistics/layout.ts", "../types/app/logistics/regions/page.ts", "../types/app/logistics/routes/page.ts", "../types/app/logistics/van-loading/page.ts", "../types/app/logistics/van-stock/page.ts", "../types/app/logistics/vans/page.ts", "../types/app/logistics/warehouses/page.ts", "../types/app/logs/page.ts", "../types/app/logs/test-diff/page.ts", "../types/app/purchase/layout.ts", "../types/app/purchase/page.ts", "../types/app/purchasing/layout.ts", "../types/app/purchasing/goods-receipt/page.ts", "../types/app/purchasing/orders/page.ts", "../types/app/purchasing/returns/page.ts", "../types/app/purchasing/suppliers/page.ts", "../types/app/reports/layout.ts", "../types/app/reports/financial/page.ts", "../types/app/reports/inventory/page.ts", "../types/app/reports/sales/page.ts", "../types/app/reports/van-performance/page.ts", "../types/app/sales/layout.ts", "../types/app/sales/customers/page.ts", "../types/app/sales/customers/locations/page.ts", "../types/app/sales/customers/payments/page.ts", "../types/app/sales/sales/page.ts", "../types/app/settings/layout.ts", "../types/app/settings/data/page.ts", "../types/app/settings/profile/page.ts", "../types/app/settings/roles/page.ts", "../types/app/settings/system/page.ts", "../types/app/settings/users/page.ts", "../../node_modules/@types/json5/index.d.ts", "../../node_modules/@types/parse-json/index.d.ts", "../../node_modules/@types/raf/index.d.ts", "../../node_modules/@types/react-transition-group/config.d.ts", "../../node_modules/@types/react-transition-group/csstransition.d.ts", "../../node_modules/@types/react-transition-group/switchtransition.d.ts", "../../node_modules/@types/react-transition-group/transitiongroup.d.ts", "../../node_modules/@types/react-transition-group/index.d.ts", "../../node_modules/@types/trusted-types/lib/index.d.ts", "../../node_modules/@types/trusted-types/index.d.ts", "../types/app/api/[...path]/route.ts", "../types/app/logs/layout.ts", "../types/app/sales/cash-register/page.ts", "../types/app/sales/invoices/page.ts", "../types/app/sales/orders/page.ts", "../types/app/sales/quotes/page.ts", "../types/app/sales/returns/page.ts", "../../app/api/[...path]/route.ts", "../../app/purchase/components/index.ts", "../../app/purchase/components/paymentmodal.tsx", "../../app/purchase/components/productlist.tsx", "../../app/purchase/components/purchasecart.tsx", "../../app/purchase/components/purchasedetailsmodal.tsx", "../../app/purchase/components/purchaseheader.tsx", "../../app/purchase/components/quantitymodal.tsx", "../../app/purchase/components/searchbar.tsx", "../../app/purchase/components/suppliermodal.tsx", "../../app/purchase/components/supplierselector.tsx", "../../app/purchase/hooks/index.ts", "../../app/purchase/hooks/usekeyboardnavigation.ts", "../../app/purchase/hooks/useproductsearch.ts", "../../app/purchase/hooks/usepurchasecart.ts", "../../app/purchase/hooks/usepurchasedata.ts", "../../app/purchase/purchaseapi.ts", "../../app/purchase/styles/purchasestyles.ts", "../../app/purchase/types/index.ts", "../../app/purchase/utils/purchasehelpers.ts", "../../app/purchasing/suppliers/components/suppliersearchbar.tsx", "../../app/purchasing/suppliers/components/supplierstable.tsx", "../../app/purchasing/suppliers/types.ts", "../../app/sales/cash-register/page.tsx", "../../app/sales/components/orderprint.tsx", "../../app/sales/components/pagination.tsx", "../../app/sales/invoiceapi.ts", "../../app/sales/invoices/page.tsx", "../../app/sales/orders/page.tsx", "../../app/sales/ordersapi.ts", "../../app/sales/quotes/page.tsx", "../../app/sales/returns/page.tsx", "../../components/securitystatus.tsx", "../../node_modules/html2canvas/dist/types/core/cache-storage.d.ts", "../../node_modules/html2canvas/dist/types/core/context.d.ts", "../../node_modules/html2canvas/dist/types/core/logger.d.ts", "../../node_modules/html2canvas/dist/types/css/index.d.ts", "../../node_modules/html2canvas/dist/types/css/ipropertydescriptor.d.ts", "../../node_modules/html2canvas/dist/types/css/itypedescriptor.d.ts", "../../node_modules/html2canvas/dist/types/css/layout/bounds.d.ts", "../../node_modules/html2canvas/dist/types/css/layout/text.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/background-clip.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/background-image.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/background-origin.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/background-position.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/background-repeat.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/background-size.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/border-radius.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/border-style.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/border-width.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/box-shadow.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/content.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/counter-increment.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/counter-reset.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/direction.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/display.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/duration.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/float.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/font-family.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/font-style.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/font-variant.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/font-weight.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/letter-spacing.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/line-break.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/list-style-image.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/list-style-position.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/list-style-type.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/opacity.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/overflow-wrap.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/overflow.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/paint-order.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/position.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/quotes.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/text-align.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/text-decoration-line.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/text-shadow.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/text-transform.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/transform-origin.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/transform.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/visibility.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/webkit-text-stroke-width.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/word-break.d.ts", "../../node_modules/html2canvas/dist/types/css/property-descriptors/z-index.d.ts", "../../node_modules/html2canvas/dist/types/css/syntax/parser.d.ts", "../../node_modules/html2canvas/dist/types/css/syntax/tokenizer.d.ts", "../../node_modules/html2canvas/dist/types/css/types/color.d.ts", "../../node_modules/html2canvas/dist/types/css/types/image.d.ts", "../../node_modules/html2canvas/dist/types/css/types/index.d.ts", "../../node_modules/html2canvas/dist/types/css/types/length-percentage.d.ts", "../../node_modules/html2canvas/dist/types/css/types/length.d.ts", "../../node_modules/html2canvas/dist/types/dom/document-cloner.d.ts", "../../node_modules/html2canvas/dist/types/dom/element-container.d.ts", "../../node_modules/html2canvas/dist/types/dom/replaced-elements/canvas-element-container.d.ts", "../../node_modules/html2canvas/dist/types/dom/replaced-elements/image-element-container.d.ts", "../../node_modules/html2canvas/dist/types/dom/replaced-elements/index.d.ts", "../../node_modules/html2canvas/dist/types/dom/replaced-elements/svg-element-container.d.ts", "../../node_modules/html2canvas/dist/types/dom/text-container.d.ts", "../../node_modules/html2canvas/dist/types/index.d.ts", "../../node_modules/html2canvas/dist/types/render/bezier-curve.d.ts", "../../node_modules/html2canvas/dist/types/render/bound-curves.d.ts", "../../node_modules/html2canvas/dist/types/render/canvas/canvas-renderer.d.ts", "../../node_modules/html2canvas/dist/types/render/effects.d.ts", "../../node_modules/html2canvas/dist/types/render/path.d.ts", "../../node_modules/html2canvas/dist/types/render/renderer.d.ts", "../../node_modules/html2canvas/dist/types/render/stacking-context.d.ts", "../../node_modules/html2canvas/dist/types/render/vector.d.ts", "../../node_modules/jspdf/types/index.d.ts"], "fileIdsList": [[97, 139, 355, 1499], [97, 139, 355, 1498], [97, 139, 355, 1501], [97, 139, 355, 1505], [97, 139, 355, 1506], [97, 139, 355, 1613], [97, 139, 355, 1611], [97, 139, 355, 1623], [97, 139, 355, 1617], [97, 139, 355, 1618], [97, 139, 355, 1619], [97, 139, 355, 1620], [97, 139, 355, 1621], [97, 139, 355, 1622], [97, 139, 355, 1624], [97, 139, 355, 1627], [97, 139, 355, 1673], [97, 139, 355, 1674], [97, 139, 355, 1675], [97, 139, 355, 1678], [97, 139, 355, 1679], [97, 139, 355, 1682], [97, 139, 355, 1683], [97, 139, 355, 1495], [97, 139, 355, 1684], [97, 139, 355, 1685], [97, 139, 355, 1687], [97, 139, 355, 1686], [97, 139, 355, 1688], [97, 139, 355, 1689], [97, 139, 355, 1691], [97, 139, 355, 1693], [97, 139, 355, 1694], [97, 139, 355, 1692], [97, 139, 355, 1695], [97, 139, 355, 1696], [97, 139, 355, 1702], [97, 139, 355, 1700], [97, 139, 355, 1705], [97, 139, 355, 1697], [97, 139, 355, 1706], [97, 139, 355, 1708], [97, 139, 355, 1707], [97, 139, 355, 1710], [97, 139, 355, 1711], [97, 139, 355, 1712], [97, 139, 355, 2080], [97, 139], [85, 97, 139, 390, 406, 439, 1496, 1497], [85, 97, 139, 390, 406, 439, 1487, 1496, 1497], [85, 97, 139, 384], [85, 97, 139, 409], [97, 139, 407, 408], [97, 139, 409, 438, 439], [97, 139, 1494, 1500], [85, 97, 139, 439, 440, 1502, 1503, 1504], [85, 97, 139, 1494, 1500], [85, 97, 139, 390, 439, 442, 1608], [85, 97, 139, 438, 439, 441, 861, 862], [85, 97, 139, 438, 439, 441, 442, 861, 862, 1608], [85, 97, 139, 438, 439, 441, 1536, 1550, 1607], [85, 97, 139], [97, 139, 407, 408, 441], [85, 97, 139, 438, 439, 441, 442, 444], [85, 97, 139, 439, 445, 830, 861], [85, 97, 139, 439, 441, 442, 830, 1615], [85, 97, 139, 439, 442, 443, 830, 1536, 1615, 1616], [97, 139, 403, 464, 1490, 1492], [85, 97, 139, 439, 446, 861, 862, 1615, 1625, 1626], [85, 97, 139, 447, 830, 841], [85, 97, 139, 447, 830], [85, 97, 139, 447, 830, 841, 1629, 1669], [85, 97, 139, 408, 439, 446, 447, 830, 841, 1615, 1626, 1670, 1671, 1672], [85, 97, 139, 439, 448, 861, 862, 1615, 1676, 1677], [85, 97, 139, 448, 1676], [85, 97, 139, 439, 458, 861, 883, 1615, 1626], [97, 139, 449], [97, 139, 449, 450], [85, 97, 139, 449, 450, 458], [97, 139, 408], [85, 97, 139, 439, 449, 450, 458, 861, 895, 897, 1626, 1681], [85, 97, 139, 449, 1681], [97, 139, 1494], [85, 97, 139, 438, 439, 1491], [85, 97, 139, 452], [85, 97, 139, 390, 439, 453, 458, 464, 810, 820, 825, 826, 1626], [97, 139, 459, 460, 461, 462, 463, 816, 817, 818, 819], [85, 97, 139, 452, 816], [85, 97, 139, 390, 439, 452, 458, 464, 466, 801, 802, 809, 810, 815], [85, 97, 139, 453, 458], [85, 97, 139, 452, 460], [97, 139, 821, 824, 825], [85, 97, 139, 439, 452, 453, 464, 822, 823], [85, 97, 139, 438, 439, 452], [85, 97, 139, 439, 815], [97, 139, 467, 469, 470, 471, 472, 473, 474, 800, 801], [85, 97, 139, 466], [85, 97, 139, 466, 799], [85, 97, 139, 466, 468], [85, 97, 139, 465, 466, 468], [85, 97, 139, 458, 466, 468, 470, 471, 472], [85, 97, 139, 458, 465, 466], [97, 139, 811, 812, 813, 814], [85, 97, 139, 439, 452, 809, 811], [85, 97, 139, 439, 466], [85, 97, 139, 439, 466, 809], [97, 139, 407, 408, 809, 823, 827], [97, 139, 466], [97, 139, 452], [97, 139, 1626], [97, 139, 831, 832], [85, 97, 139, 830], [85, 97, 139, 823], [85, 97, 139, 823, 830], [97, 139, 834, 835], [85, 97, 139, 464, 823, 829], [85, 97, 139, 439, 464, 823, 829, 833, 836, 861, 1690], [97, 139, 438, 823], [97, 139, 838], [85, 97, 139, 458, 837], [85, 97, 139, 830, 841], [97, 139, 844, 845], [85, 97, 139, 841, 843], [85, 97, 139, 841], [97, 139, 407], [97, 139, 847, 849], [85, 97, 139, 464, 841, 843, 848], [85, 97, 139, 830, 841, 1629, 1669], [85, 97, 139, 371, 439, 830, 841, 843, 1626, 1701], [85, 97, 139, 439, 464, 841, 843, 846, 848, 850, 861, 1698, 1699], [85, 97, 139, 830, 840, 842], [85, 97, 139, 439, 464, 830, 840, 842, 843], [85, 97, 139, 439, 464, 830, 840, 842, 843, 861, 862, 1626, 1703, 1704], [97, 139, 438, 840], [97, 139, 438, 841], [97, 139, 838, 852, 860, 865, 866, 867, 886, 887, 888], [85, 97, 139, 860, 865], [85, 97, 139, 837, 886], [85, 97, 139, 390, 439, 458, 464, 837, 848, 859, 863, 877, 878, 885], [85, 97, 139, 458, 837, 851, 864], [85, 97, 139, 837], [85, 97, 139, 458, 837, 859], [85, 97, 139, 458, 851], [85, 97, 139, 458, 837, 851, 861, 862, 864], [97, 139, 890, 891, 892], [85, 97, 139, 439, 464, 822, 837, 841, 851, 881], [85, 97, 139, 438, 439, 837], [85, 97, 139, 439, 885], [85, 97, 139, 390, 439, 458, 464, 848, 851, 889, 892, 893, 1626], [85, 97, 139, 439, 441], [97, 139, 869, 870, 871, 872, 873, 874, 875, 876], [85, 97, 139, 863, 868], [85, 97, 139, 799, 863], [85, 97, 139, 863, 864, 868], [85, 97, 139, 863, 864, 868, 871, 872, 873], [85, 97, 139, 458, 863, 868], [97, 139, 879, 880, 882, 884], [85, 97, 139, 863], [85, 97, 139, 439, 464, 859, 880, 881], [85, 97, 139, 439, 444, 863, 881, 883], [85, 97, 139, 439, 859, 863], [97, 139, 407, 408, 827, 841, 859], [97, 139, 863], [97, 139, 837], [85, 97, 139, 438, 439, 458, 464, 822, 827, 895, 1626, 1709], [85, 97, 139, 438, 439, 861, 862, 894, 1536], [85, 97, 139, 448, 895, 1536, 1550, 1607, 2037, 2074], [85, 97, 139, 2074], [85, 97, 139, 438, 439, 448, 895, 2074, 2078], [85, 97, 139, 448, 861, 862, 895, 2037], [85, 97, 139, 438, 439, 448, 464, 895, 1709, 2038, 2075, 2076, 2077, 2079], [97, 139, 408, 853], [85, 97, 139, 439, 458, 853, 855, 856, 857], [85, 97, 139, 439, 853, 854], [97, 139, 853, 854, 855, 856, 858], [97, 139, 853], [85, 97, 139, 1060, 1408, 1411, 1414, 1417, 1426, 1429, 1432, 1484, 1485], [97, 139, 1484], [97, 139, 1483], [85, 97, 139, 861, 862], [85, 97, 139, 390, 439], [85, 97, 139, 390, 401, 439, 458], [97, 139, 803], [85, 97, 139, 803, 804], [97, 139, 803, 804, 805, 806, 808], [85, 97, 139, 439, 458, 803, 805, 806, 807], [85, 97, 139, 382, 390, 401, 439, 458, 883], [97, 139, 896], [85, 97, 139, 439, 458, 895], [85, 97, 139, 390], [85, 97, 139, 1486], [97, 139, 403, 404], [97, 139, 904, 905], [97, 139, 906, 907], [97, 139, 906], [85, 97, 139, 910, 913], [85, 97, 139, 908], [97, 139, 904, 910], [97, 139, 908, 910, 911, 912, 913, 915, 916, 917, 918, 919], [85, 97, 139, 914], [97, 139, 910], [85, 97, 139, 912], [97, 139, 914], [97, 139, 920], [83, 97, 139, 904], [97, 139, 909], [97, 139, 900], [97, 139, 910, 921, 922, 923], [97, 139, 910, 921, 922], [97, 139, 924, 925], [97, 139, 924], [97, 139, 902], [97, 139, 901], [97, 139, 903], [97, 139, 2045], [97, 139, 2046, 2047], [85, 97, 139, 2048], [85, 97, 139, 2049], [85, 97, 139, 2039, 2040], [85, 97, 139, 2041], [85, 97, 139, 2039, 2040, 2044, 2051, 2052], [85, 97, 139, 2039, 2040, 2055], [85, 97, 139, 2039, 2040, 2052], [85, 97, 139, 2039, 2040, 2051], [85, 97, 139, 2039, 2040, 2044, 2052, 2055], [85, 97, 139, 2039, 2040, 2052, 2055], [97, 139, 2041, 2042, 2043, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073], [85, 97, 139, 2049, 2050], [85, 97, 139, 2039], [97, 139, 1713, 1714, 1715, 1716, 1717, 1718, 1719, 1720, 1721, 1722, 1723, 1724, 1725, 1726, 1727, 1728, 1729, 1730, 1731, 1732, 1733, 1734, 1735, 1736, 1737, 1738, 1739, 1740, 1741, 1742, 1743, 1744, 1745, 1746, 1747, 1748, 1749, 1750, 1751, 1752, 1753, 1754, 1755, 1756, 1757, 1758, 1759, 1760, 1761, 1762, 1763, 1764, 1765, 1766, 1767, 1768, 1769, 1770, 1771, 1772, 1773, 1774, 1775, 1776, 1777, 1778, 1779, 1780, 1781, 1782, 1783, 1784, 1785, 1786, 1787, 1788, 1789, 1790, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 1799, 1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808, 1809, 1810, 1811, 1812, 1813, 1814, 1815, 1816, 1817, 1818, 1819, 1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827, 1828, 1829, 1830, 1831, 1832, 1833, 1834, 1835, 1836, 1837, 1838, 1839, 1840, 1841, 1842, 1843, 1844, 1845, 1846, 1847, 1848, 1849, 1850, 1851, 1852, 1853, 1854, 1855, 1856, 1857, 1858, 1859, 1860, 1861, 1862, 1863, 1864, 1865, 1866, 1867, 1868, 1869, 1870, 1871, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 1880, 1881, 1882, 1883, 1884, 1885, 1886, 1887, 1888, 1889, 1890, 1891, 1892, 1893, 1894, 1895, 1896, 1897, 1898, 1899, 1900, 1901, 1902, 1903, 1904, 1905, 1906, 1907, 1908, 1909, 1910, 1911, 1912, 1913, 1914, 1915, 1916, 1917, 1918, 1919, 1920, 1921, 1922, 1923, 1924, 1925, 1926, 1927, 1928, 1929, 1930, 1931, 1932, 1933, 1934, 1935, 1936, 1937, 1938, 1939, 1940, 1941, 1942, 1943, 1944, 1945, 1946, 1947, 1948, 1949, 1950, 1951, 1952, 1953, 1954, 1955, 1956, 1957, 1958, 1959, 1960, 1961, 1962, 1963, 1964, 1965, 1966, 1967, 1968, 1969, 1970, 1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 1980, 1981, 1982, 1983, 1984, 1985, 1986, 1987, 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036], [97, 139, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798], [97, 139, 1606], [97, 139, 1536, 1550, 1605], [85, 97, 139, 1038, 1045, 1059, 1063, 1116, 1209, 1483], [97, 139, 1209, 1210], [85, 97, 139, 1038, 1049, 1203, 1483], [97, 139, 1203, 1204], [85, 97, 139, 1038, 1049, 1206, 1483], [97, 139, 1206, 1207], [85, 97, 139, 1038, 1045, 1054, 1063, 1212, 1483], [97, 139, 1212, 1213], [85, 97, 139, 898, 1038, 1048, 1049, 1057, 1060, 1061, 1063, 1483], [97, 139, 1061, 1064], [85, 97, 139, 1038, 1068, 1069, 1483], [97, 139, 1069, 1070], [85, 97, 139, 898, 1038, 1045, 1059, 1072, 1483], [97, 139, 1072, 1073], [85, 97, 139, 898, 1038, 1049, 1057, 1060, 1063, 1077, 1103, 1105, 1106, 1483], [97, 139, 1106, 1107], [85, 97, 139, 898, 1038, 1045, 1048, 1063, 1109, 1483], [97, 139, 1109, 1110], [85, 97, 139, 898, 1038, 1063, 1111, 1112, 1483], [97, 139, 1112, 1113], [85, 97, 139, 1038, 1045, 1063, 1116, 1118, 1119, 1483], [97, 139, 1119, 1120], [85, 97, 139, 898, 1038, 1045, 1063, 1122, 1483], [97, 139, 1122, 1123], [85, 97, 139, 1038, 1045, 1128, 1483], [97, 139, 1128, 1129], [85, 97, 139, 1038, 1045, 1054, 1063, 1125, 1483], [97, 139, 1125, 1126], [85, 97, 139, 1038, 1045, 1048, 1063, 1131, 1483], [97, 139, 1131, 1132], [85, 97, 139, 898, 1038, 1045, 1054, 1139, 1483], [97, 139, 1139, 1140], [85, 97, 139, 1038, 1045, 1051, 1052, 1483], [97, 139, 1050, 1052, 1053], [85, 97, 139, 1049, 1050], [85, 97, 139, 898, 1038, 1045, 1134, 1483], [85, 97, 139, 1135], [97, 139, 1134, 1135, 1136, 1137], [85, 97, 139, 898, 1038, 1045, 1060, 1157, 1483], [97, 139, 1157, 1158], [85, 97, 139, 1038, 1045, 1054, 1063, 1142, 1483], [97, 139, 1142, 1143], [85, 97, 139, 1038, 1049, 1145, 1483], [97, 139, 1145, 1146], [85, 97, 139, 1038, 1045, 1148, 1483], [97, 139, 1148, 1149], [85, 97, 139, 1038, 1045, 1063, 1068, 1151, 1483], [97, 139, 1151, 1152], [85, 97, 139, 1038, 1045, 1154, 1483], [97, 139, 1154, 1155], [85, 97, 139, 898, 1038, 1049, 1063, 1161, 1162, 1483], [97, 139, 1162, 1163], [85, 97, 139, 898, 1038, 1045, 1063, 1075, 1483], [97, 139, 1075, 1076], [85, 97, 139, 898, 1038, 1049, 1165, 1483], [97, 139, 1165, 1166], [97, 139, 1357], [85, 97, 139, 1038, 1049, 1116, 1168, 1483], [97, 139, 1168, 1169], [85, 97, 139, 1038, 1045, 1171, 1483], [97, 139, 1038], [97, 139, 1171, 1172], [85, 97, 139, 1483], [97, 139, 1174], [85, 97, 139, 1038, 1049, 1060, 1063, 1116, 1121, 1188, 1189, 1483], [97, 139, 1189, 1190], [85, 97, 139, 1038, 1049, 1176, 1483], [97, 139, 1176, 1177], [85, 97, 139, 1038, 1049, 1179, 1483], [97, 139, 1179, 1180], [85, 97, 139, 1038, 1045, 1068, 1182, 1483], [97, 139, 1182, 1183], [85, 97, 139, 1038, 1045, 1068, 1192, 1483], [97, 139, 1192, 1193], [85, 97, 139, 898, 1038, 1045, 1195, 1483], [97, 139, 1195, 1196], [85, 97, 139, 1038, 1049, 1060, 1063, 1116, 1121, 1188, 1199, 1200, 1483], [97, 139, 1200, 1201], [85, 97, 139, 898, 1038, 1045, 1054, 1215, 1483], [97, 139, 1215, 1216], [85, 97, 139, 1116], [97, 139, 1117], [97, 139, 1038, 1049, 1220, 1221, 1483], [97, 139, 1221, 1222], [85, 97, 139, 898, 1038, 1045, 1227, 1483], [85, 97, 139, 1228], [97, 139, 1227, 1228, 1229, 1230], [97, 139, 1229], [85, 97, 139, 1038, 1049, 1063, 1068, 1224, 1483], [97, 139, 1224, 1225], [85, 97, 139, 1038, 1049, 1232, 1483], [97, 139, 1232, 1233], [85, 97, 139, 898, 1038, 1045, 1235, 1483], [97, 139, 1235, 1236], [85, 97, 139, 898, 1038, 1045, 1238, 1483], [97, 139, 1238, 1239], [97, 139, 898, 1038, 1483], [97, 139, 1244, 1245], [85, 97, 139, 898, 1038, 1045, 1241, 1483], [97, 139, 1241, 1242], [85, 97, 139, 898, 1038, 1045, 1247, 1483], [97, 139, 1247, 1248], [85, 97, 139, 898, 1038, 1045, 1054, 1055, 1483], [97, 139, 1055, 1056], [85, 97, 139, 898, 1038, 1045, 1250, 1483], [97, 139, 1250, 1251], [85, 97, 139, 1038, 1045, 1256, 1483], [97, 139, 1256, 1257], [85, 97, 139, 1038, 1049, 1253, 1483], [97, 139, 1253, 1254], [97, 139, 1265, 1266], [97, 139, 1038, 1049, 1220, 1265, 1483], [97, 139, 1259, 1260], [85, 97, 139, 1038, 1045, 1259, 1483], [97, 139, 1218, 1219], [85, 97, 139, 898, 1038, 1049, 1218, 1483], [97, 139, 1262, 1263], [85, 97, 139, 898, 1038, 1045, 1240, 1262, 1483], [97, 139, 898, 1483], [85, 97, 139, 1049, 1054, 1063, 1160], [97, 139, 1268, 1269], [85, 97, 139, 898, 1038, 1049, 1268, 1483], [97, 139, 1271, 1272], [85, 97, 139, 898, 1038, 1045, 1068, 1271, 1483], [97, 139, 1292, 1293], [85, 97, 139, 1038, 1045, 1292, 1483], [97, 139, 1280, 1281], [85, 97, 139, 1038, 1045, 1280, 1483], [97, 139, 1274, 1275], [97, 139, 1038, 1049, 1274, 1483], [97, 139, 1283, 1284], [85, 97, 139, 1038, 1045, 1054, 1283, 1483], [97, 139, 1277, 1278], [85, 97, 139, 1038, 1049, 1277, 1483], [97, 139, 1286, 1287], [85, 97, 139, 1038, 1049, 1286, 1483], [97, 139, 1289, 1290], [85, 97, 139, 1038, 1049, 1063, 1068, 1289, 1483], [97, 139, 1295, 1296], [85, 97, 139, 1038, 1045, 1295, 1483], [97, 139, 1306, 1307], [85, 97, 139, 1038, 1049, 1060, 1063, 1116, 1121, 1188, 1302, 1305, 1306, 1483], [97, 139, 1298, 1299], [85, 97, 139, 1038, 1045, 1054, 1298, 1483], [97, 139, 1301], [85, 97, 139, 1045, 1294], [97, 139, 1309, 1310], [85, 97, 139, 1038, 1049, 1060, 1063, 1270, 1309, 1483], [97, 139, 1185, 1186, 1187], [85, 97, 139, 898, 1038, 1045, 1063, 1098, 1121, 1186, 1483], [97, 139, 1313, 1314], [85, 97, 139, 1038, 1049, 1267, 1312, 1313, 1483], [85, 97, 139, 1038, 1483], [97, 139, 1317, 1318], [85, 97, 139, 1038, 1049, 1063, 1220, 1317, 1483], [85, 97, 139, 898, 1483], [97, 139, 1321, 1322], [85, 97, 139, 898, 1038, 1049, 1320, 1321, 1483], [97, 139, 1324, 1325], [85, 97, 139, 898, 1038, 1045, 1063, 1320, 1324, 1483], [97, 139, 1058, 1059], [85, 97, 139, 898, 1038, 1045, 1058, 1483], [97, 139, 1303, 1304], [85, 97, 139, 1038, 1049, 1060, 1062, 1063, 1116, 1121, 1188, 1303, 1483], [85, 97, 139, 1063, 1095, 1098, 1099], [97, 139, 1100, 1101, 1102], [85, 97, 139, 1038, 1100, 1483], [97, 139, 1096, 1097], [85, 97, 139, 1096], [97, 139, 1332, 1333], [85, 97, 139, 898, 1038, 1049, 1063, 1161, 1332, 1483], [97, 139, 1327, 1329, 1330], [85, 97, 139, 1234], [97, 139, 1234], [97, 139, 1328], [97, 139, 1335, 1336], [85, 97, 139, 898, 1038, 1045, 1063, 1335, 1483], [97, 139, 1338, 1339], [85, 97, 139, 1038, 1045, 1338, 1483], [97, 139, 1342, 1343], [85, 97, 139, 1038, 1049, 1223, 1267, 1308, 1319, 1341, 1342, 1483], [85, 97, 139, 1038, 1308, 1483], [97, 139, 1345, 1346], [85, 97, 139, 898, 1038, 1045, 1345, 1483], [97, 139, 1198], [97, 139, 1351, 1352], [85, 97, 139, 898, 1038, 1045, 1063, 1348, 1350, 1351, 1483], [85, 97, 139, 1349], [97, 139, 1359, 1360], [85, 97, 139, 1038, 1049, 1063, 1116, 1356, 1358, 1359, 1483], [97, 139, 1354, 1355], [85, 97, 139, 1038, 1049, 1060, 1354, 1483], [97, 139, 1363, 1364], [85, 97, 139, 1038, 1049, 1063, 1217, 1362, 1363, 1483], [97, 139, 1369, 1370], [85, 97, 139, 1038, 1049, 1063, 1217, 1368, 1369, 1483], [97, 139, 1372, 1373], [85, 97, 139, 1038, 1049, 1372, 1483], [97, 139, 1375, 1376], [85, 97, 139, 1038, 1045, 1463], [97, 139, 1397, 1398, 1399], [85, 97, 139, 1038, 1045, 1397, 1483], [97, 139, 1378, 1379], [85, 97, 139, 1038, 1045, 1054, 1378, 1483], [97, 139, 1381, 1382], [85, 97, 139, 1038, 1049, 1381, 1483], [97, 139, 1384, 1385], [85, 97, 139, 1038, 1049, 1063, 1116, 1170, 1384, 1483], [97, 139, 1387, 1388], [85, 97, 139, 1038, 1048, 1049, 1387, 1483], [97, 139, 1390, 1391], [85, 97, 139, 1038, 1049, 1063, 1389, 1390, 1483], [97, 139, 1393, 1394, 1395], [85, 97, 139, 1038, 1045, 1060, 1393, 1483], [97, 139, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1457, 1458, 1459, 1463], [97, 139, 1457, 1458, 1459], [97, 139, 1462], [83, 97, 139, 1038], [97, 139, 1461, 1462], [97, 139, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1460, 1462], [97, 139, 898, 1015, 1038, 1040, 1042, 1044, 1460, 1461], [85, 97, 139, 1039, 1040], [97, 139, 1039], [97, 139, 898, 899, 1015, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1457, 1458, 1459, 1460, 1462, 1463, 1464, 1465, 1466, 1467, 1468, 1469, 1470, 1473, 1474, 1475, 1476, 1477, 1478, 1479, 1480, 1481, 1482], [97, 139, 1038, 1048, 1051, 1054, 1057, 1060, 1065, 1068, 1071, 1074, 1077, 1103, 1108, 1111, 1114, 1121, 1124, 1127, 1130, 1133, 1138, 1141, 1144, 1147, 1150, 1153, 1156, 1159, 1164, 1167, 1170, 1173, 1178, 1181, 1184, 1188, 1191, 1194, 1197, 1202, 1205, 1208, 1211, 1214, 1217, 1220, 1223, 1226, 1231, 1234, 1237, 1240, 1243, 1246, 1249, 1252, 1255, 1258, 1261, 1264, 1267, 1270, 1273, 1276, 1279, 1282, 1285, 1288, 1291, 1294, 1297, 1300, 1302, 1305, 1308, 1311, 1315, 1319, 1323, 1326, 1331, 1334, 1337, 1340, 1344, 1347, 1353, 1356, 1361, 1365, 1368, 1371, 1374, 1377, 1380, 1383, 1386, 1389, 1392, 1396, 1400, 1405, 1408, 1411, 1414, 1417, 1420, 1424, 1426, 1429, 1432, 1435, 1438, 1441, 1447, 1450, 1453, 1456, 1457], [97, 139, 1048, 1051, 1054, 1057, 1060, 1065, 1068, 1071, 1074, 1077, 1103, 1108, 1111, 1114, 1121, 1124, 1127, 1130, 1133, 1138, 1141, 1144, 1147, 1150, 1153, 1156, 1159, 1164, 1167, 1170, 1173, 1175, 1178, 1181, 1184, 1188, 1191, 1194, 1197, 1202, 1205, 1208, 1211, 1214, 1217, 1220, 1223, 1226, 1231, 1234, 1237, 1240, 1243, 1246, 1249, 1252, 1255, 1258, 1261, 1264, 1267, 1270, 1273, 1276, 1279, 1282, 1285, 1288, 1291, 1294, 1297, 1300, 1302, 1305, 1308, 1311, 1315, 1316, 1319, 1323, 1326, 1331, 1334, 1337, 1340, 1344, 1347, 1353, 1356, 1361, 1365, 1368, 1371, 1374, 1377, 1380, 1383, 1386, 1389, 1392, 1396, 1400, 1402, 1405, 1408, 1411, 1414, 1417, 1420, 1424, 1426, 1429, 1432, 1435, 1438, 1441, 1447, 1450, 1453, 1456], [97, 139, 1038, 1041], [97, 139, 1038, 1463, 1471, 1472], [85, 97, 139, 1015, 1038, 1461], [85, 97, 139, 1007, 1038, 1462], [97, 139, 1463], [97, 139, 1460, 1463], [97, 139, 1038, 1457], [97, 139, 1046, 1047], [85, 97, 139, 898, 1038, 1045, 1046, 1483], [97, 139, 1401], [85, 97, 139, 1063, 1202], [97, 139, 1403, 1404], [85, 97, 139, 898, 1038, 1049, 1063, 1161, 1403, 1483], [97, 139, 1439, 1440], [85, 97, 139, 1038, 1045, 1054, 1439, 1483], [97, 139, 1427, 1428], [85, 97, 139, 898, 1038, 1045, 1427, 1483], [97, 139, 1406, 1407], [85, 97, 139, 1038, 1045, 1406, 1483], [97, 139, 1409, 1410], [85, 97, 139, 898, 1038, 1049, 1409, 1483], [97, 139, 1412, 1413], [85, 97, 139, 1038, 1045, 1412, 1483], [97, 139, 1436, 1437], [85, 97, 139, 1038, 1045, 1436, 1483], [97, 139, 1415, 1416], [85, 97, 139, 1038, 1045, 1415, 1483], [97, 139, 1421, 1425], [85, 97, 139, 1038, 1045, 1057, 1063, 1300, 1344, 1411, 1420, 1421, 1424, 1483], [97, 139, 1418, 1419], [85, 97, 139, 1048, 1056], [97, 139, 1430, 1431], [85, 97, 139, 1038, 1045, 1430, 1483], [97, 139, 1433, 1434], [85, 97, 139, 1038, 1045, 1054, 1063, 1433, 1483], [97, 139, 1445, 1446], [85, 97, 139, 898, 1038, 1045, 1048, 1063, 1444, 1445, 1483], [97, 139, 1442, 1443], [85, 97, 139, 1038, 1048, 1054, 1063, 1442, 1483], [97, 139, 1448, 1449], [85, 97, 139, 898, 1038, 1049, 1063, 1220, 1223, 1231, 1237, 1264, 1267, 1319, 1344, 1448, 1483], [97, 139, 1451, 1452], [85, 97, 139, 898, 1038, 1045, 1054, 1451, 1483], [97, 139, 1454, 1455], [85, 97, 139, 898, 1038, 1049, 1454, 1483], [97, 139, 1422, 1423], [85, 97, 139, 898, 1038, 1045, 1422, 1483], [97, 139, 1366, 1367], [85, 97, 139, 1038, 1049, 1063, 1103, 1116, 1366, 1483], [97, 139, 1116], [85, 97, 139, 1115], [97, 139, 1066, 1067], [85, 97, 139, 898, 1038, 1041, 1045, 1066, 1483], [97, 139, 1104], [85, 97, 139, 898], [97, 139, 1000, 1463], [97, 139, 1038, 1062, 1483], [97, 139, 973, 975, 977], [97, 139, 974], [97, 139, 973], [97, 139, 976], [85, 97, 139, 921], [97, 139, 929], [83, 97, 139, 921, 926, 928, 930], [97, 139, 927], [97, 139, 933], [97, 139, 934], [85, 97, 139, 898, 933, 935, 945, 950, 954, 956, 958, 960, 962, 964, 966, 968, 970, 982], [97, 139, 983, 984], [97, 139, 931, 933, 936, 945, 950], [97, 139, 951], [97, 139, 1001], [97, 139, 953], [97, 139, 898, 1021], [85, 97, 139, 898, 945, 950, 1020], [85, 97, 139, 898, 931, 950, 1021], [97, 139, 1020, 1021, 1023], [97, 139, 898, 950, 985], [97, 139, 986], [97, 139, 898], [97, 139, 936], [85, 97, 139, 931, 945, 950], [97, 139, 988], [97, 139, 931], [97, 139, 931, 936, 937, 938, 945, 946, 948], [97, 139, 946, 949], [97, 139, 947], [97, 139, 959], [85, 97, 139, 1007, 1008, 1009], [97, 139, 1011], [97, 139, 1008, 1010, 1011, 1012, 1013, 1014], [97, 139, 1008], [97, 139, 955], [97, 139, 957], [97, 139, 971], [85, 97, 139, 931, 950], [97, 139, 979], [85, 97, 139, 898, 931, 989, 996, 1025], [97, 139, 898, 1025], [97, 139, 936, 938, 945, 1025], [85, 97, 139, 898, 945, 950, 985], [97, 139, 1025, 1026, 1027, 1028, 1029, 1030], [97, 139, 931, 933, 935, 936, 937, 938, 945, 948, 950, 952, 954, 956, 958, 960, 962, 964, 966, 968, 970, 972, 978, 980, 982, 985, 987, 989, 991, 994, 996, 998, 1000, 1002, 1004, 1005, 1011, 1013, 1015, 1016, 1017, 1019, 1022, 1024, 1031, 1036, 1037], [97, 139, 1006], [97, 139, 961], [97, 139, 963], [97, 139, 1018], [97, 139, 965], [97, 139, 967], [97, 139, 981], [85, 97, 139, 898, 931, 936, 938, 989, 1032], [97, 139, 1032, 1033, 1034, 1035], [97, 139, 898, 1032], [97, 139, 932], [97, 139, 990], [97, 139, 989], [97, 139, 939], [97, 139, 942], [97, 139, 939, 940, 941, 942, 943, 944], [83, 97, 139], [83, 97, 139, 931, 939, 940, 941], [97, 139, 1003], [97, 139, 978], [97, 139, 969], [97, 139, 999], [97, 139, 995], [97, 139, 950], [97, 139, 992, 993], [97, 139, 997], [97, 139, 1094], [97, 139, 1088, 1090], [97, 139, 1078, 1088, 1089, 1091, 1092, 1093], [97, 139, 1088], [97, 139, 1078, 1088], [97, 139, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087], [97, 139, 1079, 1083, 1084, 1087, 1088, 1091], [97, 139, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087, 1088, 1089, 1091, 1092], [97, 139, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087], [97, 139, 1629], [85, 97, 139, 1629, 1637], [85, 97, 139, 1634, 1639], [85, 97, 139, 1629], [97, 139, 1629, 1634], [97, 139, 1629, 1633, 1634, 1636], [85, 97, 139, 1633], [85, 97, 139, 1629, 1633, 1634, 1636, 1637, 1639, 1640], [97, 139, 1632, 1633, 1634, 1635, 1636, 1637, 1638, 1639, 1640, 1641, 1642, 1643, 1644, 1645, 1646], [97, 139, 1629, 1633, 1634, 1635], [97, 139, 1629, 1636], [97, 139, 1629, 1633], [97, 139, 1629, 1634, 1636], [97, 139, 411], [97, 139, 410, 411], [97, 139, 410, 411, 412, 413, 414, 415, 416, 417, 418], [97, 139, 410, 411, 412], [85, 97, 139, 419], [85, 97, 139, 281, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437], [97, 139, 419, 420], [85, 97, 139, 281], [97, 139, 419], [97, 139, 419, 420, 429], [97, 139, 419, 420, 422], [97, 139, 1628], [97, 136, 139], [97, 138, 139], [139], [97, 139, 144, 173], [97, 139, 140, 145, 151, 152, 159, 170, 181], [97, 139, 140, 141, 151, 159], [92, 93, 94, 97, 139], [97, 139, 142, 182], [97, 139, 143, 144, 152, 160], [97, 139, 144, 170, 178], [97, 139, 145, 147, 151, 159], [97, 138, 139, 146], [97, 139, 147, 148], [97, 139, 149, 151], [97, 138, 139, 151], [97, 139, 151, 152, 153, 170, 181], [97, 139, 151, 152, 153, 166, 170, 173], [97, 134, 139], [97, 139, 147, 151, 154, 159, 170, 181], [97, 139, 151, 152, 154, 155, 159, 170, 178, 181], [97, 139, 154, 156, 170, 178, 181], [95, 96, 97, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187], [97, 139, 151, 157], [97, 139, 158, 181, 186], [97, 139, 147, 151, 159, 170], [97, 139, 160], [97, 139, 161], [97, 138, 139, 162], [97, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187], [97, 139, 164], [97, 139, 165], [97, 139, 151, 166, 167], [97, 139, 166, 168, 182, 184], [97, 139, 151, 170, 171, 173], [97, 139, 172, 173], [97, 139, 170, 171], [97, 139, 173], [97, 139, 174], [97, 136, 139, 170], [97, 139, 151, 176, 177], [97, 139, 176, 177], [97, 139, 144, 159, 170, 178], [97, 139, 179], [97, 139, 159, 180], [97, 139, 154, 165, 181], [97, 139, 144, 182], [97, 139, 170, 183], [97, 139, 158, 184], [97, 139, 185], [97, 139, 151, 153, 162, 170, 173, 181, 184, 186], [97, 139, 170, 187], [97, 139, 170, 188], [85, 97, 139, 192, 193, 194], [85, 97, 139, 192, 193], [97, 139, 1115, 2133, 2134, 2135, 2136], [85, 89, 97, 139, 191, 356, 399], [85, 89, 97, 139, 190, 356, 399], [82, 83, 84, 97, 139], [97, 139, 2138], [90, 97, 139], [97, 139, 360], [97, 139, 362, 363, 364], [97, 139, 366], [97, 139, 197, 207, 213, 215, 356], [97, 139, 197, 204, 206, 209, 227], [97, 139, 207], [97, 139, 207, 209, 334], [97, 139, 262, 280, 295, 402], [97, 139, 304], [97, 139, 197, 207, 214, 248, 258, 331, 332, 402], [97, 139, 214, 402], [97, 139, 207, 258, 259, 260, 402], [97, 139, 207, 214, 248, 402], [97, 139, 402], [97, 139, 197, 214, 215, 402], [97, 139, 288], [97, 138, 139, 188, 287], [85, 97, 139, 281, 282, 283, 301, 302], [97, 139, 271], [97, 139, 270, 272, 376], [85, 97, 139, 281, 282, 299], [97, 139, 277, 302, 388], [97, 139, 386, 387], [97, 139, 221, 385], [97, 139, 274], [97, 138, 139, 188, 221, 237, 270, 271, 272, 273], [85, 97, 139, 299, 301, 302], [97, 139, 299, 301], [97, 139, 299, 300, 302], [97, 139, 165, 188], [97, 139, 269], [97, 138, 139, 188, 206, 208, 265, 266, 267, 268], [85, 97, 139, 198, 379], [85, 97, 139, 181, 188], [85, 97, 139, 214, 246], [85, 97, 139, 214], [97, 139, 244, 249], [85, 97, 139, 245, 359], [97, 139, 1488], [85, 89, 97, 139, 154, 188, 190, 191, 356, 397, 398], [97, 139, 356], [97, 139, 196], [97, 139, 349, 350, 351, 352, 353, 354], [97, 139, 351], [85, 97, 139, 245, 281, 359], [85, 97, 139, 281, 357, 359], [85, 97, 139, 281, 359], [97, 139, 154, 188, 208, 359], [97, 139, 154, 188, 205, 206, 217, 235, 237, 269, 274, 275, 297, 299], [97, 139, 266, 269, 274, 282, 284, 285, 286, 288, 289, 290, 291, 292, 293, 294, 402], [97, 139, 267], [85, 97, 139, 165, 188, 206, 207, 235, 237, 238, 240, 265, 297, 298, 302, 356, 402], [97, 139, 154, 188, 208, 209, 221, 222, 270], [97, 139, 154, 188, 207, 209], [97, 139, 154, 170, 188, 205, 208, 209], [97, 139, 154, 165, 181, 188, 205, 206, 207, 208, 209, 214, 217, 218, 228, 229, 231, 234, 235, 237, 238, 239, 240, 264, 265, 298, 299, 307, 309, 312, 314, 317, 319, 320, 321, 322], [97, 139, 154, 170, 188], [97, 139, 197, 198, 199, 205, 206, 356, 359, 402], [97, 139, 154, 170, 181, 188, 202, 333, 335, 336, 402], [97, 139, 165, 181, 188, 202, 205, 208, 225, 229, 231, 232, 233, 238, 265, 312, 323, 325, 331, 345, 346], [97, 139, 207, 211, 265], [97, 139, 205, 207], [97, 139, 218, 313], [97, 139, 315, 316], [97, 139, 315], [97, 139, 313], [97, 139, 315, 318], [97, 139, 201, 202], [97, 139, 201, 241], [97, 139, 201], [97, 139, 203, 218, 311], [97, 139, 310], [97, 139, 202, 203], [97, 139, 203, 308], [97, 139, 202], [97, 139, 297], [97, 139, 154, 188, 205, 217, 236, 256, 262, 276, 279, 296, 299], [97, 139, 250, 251, 252, 253, 254, 255, 277, 278, 302, 357], [97, 139, 306], [97, 139, 154, 188, 205, 217, 236, 242, 303, 305, 307, 356, 359], [97, 139, 154, 181, 188, 198, 205, 207, 264], [97, 139, 261], [97, 139, 154, 188, 339, 344], [97, 139, 228, 237, 264, 359], [97, 139, 327, 331, 345, 348], [97, 139, 154, 211, 331, 339, 340, 348], [97, 139, 197, 207, 228, 239, 342], [97, 139, 154, 188, 207, 214, 239, 326, 327, 337, 338, 341, 343], [97, 139, 189, 235, 236, 237, 356, 359], [97, 139, 154, 165, 181, 188, 203, 205, 206, 208, 211, 216, 217, 225, 228, 229, 231, 232, 233, 234, 238, 240, 264, 265, 309, 323, 324, 359], [97, 139, 154, 188, 205, 207, 211, 325, 347], [97, 139, 154, 188, 206, 208], [85, 97, 139, 154, 165, 188, 196, 198, 205, 206, 209, 217, 234, 235, 237, 238, 240, 306, 356, 359], [97, 139, 154, 165, 181, 188, 200, 203, 204, 208], [97, 139, 201, 263], [97, 139, 154, 188, 201, 206, 217], [97, 139, 154, 188, 207, 218], [97, 139, 154, 188], [97, 139, 221], [97, 139, 220], [97, 139, 222], [97, 139, 207, 219, 221, 225], [97, 139, 207, 219, 221], [97, 139, 154, 188, 200, 207, 208, 214, 222, 223, 224], [85, 97, 139, 299, 300, 301], [97, 139, 257], [85, 97, 139, 198], [85, 97, 139, 231], [85, 97, 139, 189, 234, 237, 240, 356, 359], [97, 139, 198, 379, 380], [85, 97, 139, 249], [85, 97, 139, 165, 181, 188, 196, 243, 245, 247, 248, 359], [97, 139, 208, 214, 231], [97, 139, 230], [85, 97, 139, 152, 154, 165, 188, 196, 249, 258, 356, 357, 358], [81, 85, 86, 87, 88, 97, 139, 190, 191, 356, 399], [97, 139, 144], [97, 139, 328, 329, 330], [97, 139, 328], [97, 139, 368], [97, 139, 370], [97, 139, 372], [97, 139, 1489], [97, 139, 374], [97, 139, 377], [97, 139, 381], [89, 91, 97, 139, 356, 361, 365, 367, 369, 371, 373, 375, 378, 382, 384, 390, 391, 393, 400, 401, 402], [97, 139, 383], [97, 139, 389], [97, 139, 245], [97, 139, 392], [97, 138, 139, 222, 223, 224, 225, 394, 395, 396, 399], [97, 139, 188], [85, 89, 97, 139, 154, 156, 165, 188, 190, 191, 192, 194, 196, 209, 348, 355, 359, 399], [85, 97, 139, 1521], [97, 139, 1521, 1522, 1523, 1526, 1527, 1528, 1529, 1530, 1531, 1532, 1535], [97, 139, 1521], [97, 139, 1524, 1525], [85, 97, 139, 1519, 1521], [97, 139, 1516, 1517, 1519], [97, 139, 1512, 1515, 1517, 1519], [97, 139, 1516, 1519], [85, 97, 139, 1507, 1508, 1509, 1512, 1513, 1514, 1516, 1517, 1518, 1519], [97, 139, 1509, 1512, 1513, 1514, 1515, 1516, 1517, 1518, 1519, 1520], [97, 139, 1516], [97, 139, 1510, 1516, 1517], [97, 139, 1510, 1511], [97, 139, 1515, 1517, 1518], [97, 139, 1515], [97, 139, 1507, 1512, 1517, 1518], [97, 139, 1533, 1534], [85, 97, 139, 1614], [97, 139, 457], [97, 139, 454, 455, 456], [85, 97, 139, 1629, 1647], [85, 97, 139, 1629, 1647, 1650], [85, 97, 139, 1628, 1629, 1647, 1650], [97, 139, 1630, 1631, 1648, 1649, 1650, 1651, 1652, 1653, 1654, 1655, 1656, 1657, 1658, 1659, 1660, 1661, 1662, 1663, 1664, 1665, 1666, 1667, 1668], [85, 97, 139, 1628, 1629, 1647], [97, 106, 110, 139, 181], [97, 106, 139, 170, 181], [97, 101, 139], [97, 103, 106, 139, 178, 181], [97, 139, 159, 178], [97, 101, 139, 188], [97, 103, 106, 139, 159, 181], [97, 98, 99, 102, 105, 139, 151, 170, 181], [97, 106, 113, 139], [97, 98, 104, 139], [97, 106, 127, 128, 139], [97, 102, 106, 139, 173, 181, 188], [97, 127, 139, 188], [97, 100, 101, 139, 188], [97, 106, 139], [97, 100, 101, 102, 103, 104, 105, 106, 107, 108, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 128, 129, 130, 131, 132, 133, 139], [97, 106, 121, 139], [97, 106, 113, 114, 139], [97, 104, 106, 114, 115, 139], [97, 105, 139], [97, 98, 101, 106, 139], [97, 106, 110, 114, 115, 139], [97, 110, 139], [97, 104, 106, 109, 139, 181], [97, 98, 103, 106, 113, 139], [97, 139, 170], [97, 101, 106, 127, 139, 186, 188], [97, 139, 1549], [97, 139, 1539, 1540], [97, 139, 1537, 1538, 1539, 1541, 1542, 1547], [97, 139, 1538, 1539], [97, 139, 1547], [97, 139, 1548], [97, 139, 1539], [97, 139, 1537, 1538, 1539, 1542, 1543, 1544, 1545, 1546], [97, 139, 1537, 1538, 1549], [97, 139, 1552, 1554, 1555, 1556, 1557], [97, 139, 1552, 1554, 1556, 1557], [97, 139, 1552, 1554, 1556], [97, 139, 1552, 1554, 1555, 1557], [97, 139, 1552, 1554, 1557], [97, 139, 1552, 1553, 1554, 1555, 1556, 1557, 1558, 1559, 1598, 1599, 1600, 1601, 1602, 1603, 1604], [97, 139, 1554, 1557], [97, 139, 1551, 1552, 1553, 1555, 1556, 1557], [97, 139, 1554, 1599, 1603], [97, 139, 1554, 1555, 1556, 1557], [97, 139, 1556], [97, 139, 1560, 1561, 1562, 1563, 1564, 1565, 1566, 1567, 1568, 1569, 1570, 1571, 1572, 1573, 1574, 1575, 1576, 1577, 1578, 1579, 1580, 1581, 1582, 1583, 1584, 1585, 1586, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 1594, 1595, 1596, 1597]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "signature": false, "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "signature": false, "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "signature": false, "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "signature": false, "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "signature": false, "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "signature": false, "impliedFormat": 1}, {"version": "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "signature": false, "impliedFormat": 1}, {"version": "ee7bad0c15b58988daa84371e0b89d313b762ab83cb5b31b8a2d1162e8eb41c2", "signature": false, "impliedFormat": 1}, {"version": "27bdc30a0e32783366a5abeda841bc22757c1797de8681bbe81fbc735eeb1c10", "signature": false, "impliedFormat": 1}, {"version": "8fd575e12870e9944c7e1d62e1f5a73fcf23dd8d3a321f2a2c74c20d022283fe", "signature": false, "impliedFormat": 1}, {"version": "8bf8b5e44e3c9c36f98e1007e8b7018c0f38d8adc07aecef42f5200114547c70", "signature": false, "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "af3dd424cf267428f30ccfc376f47a2c0114546b55c44d8c0f1d57d841e28d74", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "959d36cddf5e7d572a65045b876f2956c973a586da58e5d26cde519184fd9b8a", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "965f36eae237dd74e6cca203a43e9ca801ce38824ead814728a2807b1910117d", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3925a6c820dcb1a06506c90b1577db1fdbf7705d65b62b99dce4be75c637e26b", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a3d63ef2b853447ec4f749d3f368ce642264246e02911fcb1590d8c161b8005", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b5ce7a470bc3628408429040c4e3a53a27755022a32fd05e2cb694e7015386c7", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b4b67b1a91182421f5df999988c690f14d813b9850b40acd06ed44691f6727ad", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "df83c2a6c73228b625b0beb6669c7ee2a09c914637e2d35170723ad49c0f5cd4", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "436aaf437562f276ec2ddbee2f2cdedac7664c1e4c1d2c36839ddd582eeb3d0a", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e3c06ea092138bf9fa5e874a1fdbc9d54805d074bee1de31b99a11e2fec239d", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "87dc0f382502f5bbce5129bdc0aea21e19a3abbc19259e0b43ae038a9fc4e326", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b1cb28af0c891c8c96b2d6b7be76bd394fddcfdb4709a20ba05a7c1605eea0f9", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2fef54945a13095fdb9b84f705f2b5994597640c46afeb2ce78352fab4cb3279", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac77cb3e8c6d3565793eb90a8373ee8033146315a3dbead3bde8db5eaf5e5ec6", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "56e4ed5aab5f5920980066a9409bfaf53e6d21d3f8d020c17e4de584d29600ad", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ece9f17b3866cc077099c73f4983bddbcb1dc7ddb943227f1ec070f529dedd1", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a6282c8827e4b9a95f4bf4f5c205673ada31b982f50572d27103df8ceb8013c", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1c9319a09485199c1f7b0498f2988d6d2249793ef67edda49d1e584746be9032", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e3a2a0cee0f03ffdde24d89660eba2685bfbdeae955a6c67e8c4c9fd28928eeb", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811c71eee4aa0ac5f7adf713323a5c41b0cf6c4e17367a34fbce379e12bbf0a4", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "51ad4c928303041605b4d7ae32e0c1ee387d43a24cd6f1ebf4a2699e1076d4fa", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "60037901da1a425516449b9a20073aa03386cce92f7a1fd902d7602be3a7c2e9", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d4b1d2c51d058fc21ec2629fff7a76249dec2e36e12960ea056e3ef89174080f", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "22adec94ef7047a6c9d1af3cb96be87a335908bf9ef386ae9fd50eeb37f44c47", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4245fee526a7d1754529d19227ecbf3be066ff79ebb6a380d78e41648f2f224d", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0990a7576222f248f0a3b888adcb7389f957928ce2afb1cd5128169086ff4d29", "signature": false, "impliedFormat": 1}, {"version": "eb5b19b86227ace1d29ea4cf81387279d04bb34051e944bc53df69f58914b788", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "signature": false, "impliedFormat": 1}, {"version": "87d9d29dbc745f182683f63187bf3d53fd8673e5fca38ad5eaab69798ed29fbc", "signature": false, "impliedFormat": 1}, {"version": "472f5aab7edc498a0a761096e8e254c5bc3323d07a1e7f5f8b8ec0d6395b60a0", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "cc69795d9954ee4ad57545b10c7bf1a7260d990231b1685c147ea71a6faa265c", "signature": false, "impliedFormat": 1}, {"version": "8bc6c94ff4f2af1f4023b7bb2379b08d3d7dd80c698c9f0b07431ea16101f05f", "signature": false, "impliedFormat": 1}, {"version": "1b61d259de5350f8b1e5db06290d31eaebebc6baafd5f79d314b5af9256d7153", "signature": false, "impliedFormat": 1}, {"version": "57194e1f007f3f2cbef26fa299d4c6b21f4623a2eddc63dfeef79e38e187a36e", "signature": false, "impliedFormat": 1}, {"version": "0f6666b58e9276ac3a38fdc80993d19208442d6027ab885580d93aec76b4ef00", "signature": false, "impliedFormat": 1}, {"version": "05fd364b8ef02fb1e174fbac8b825bdb1e5a36a016997c8e421f5fab0a6da0a0", "signature": false, "impliedFormat": 1}, {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "signature": false, "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "49a5a44f2e68241a1d2bd9ec894535797998841c09729e506a7cbfcaa40f2180", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "signature": false, "impliedFormat": 1}, {"version": "763fe0f42b3d79b440a9b6e51e9ba3f3f91352469c1e4b3b67bfa4ff6352f3f4", "signature": false, "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "signature": false, "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "signature": false, "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "signature": false, "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "signature": false, "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "signature": false, "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "signature": false, "impliedFormat": 1}, {"version": "7f182617db458e98fc18dfb272d40aa2fff3a353c44a89b2c0ccb3937709bfb5", "signature": false, "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "signature": false, "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "signature": false, "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "signature": false, "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "signature": false, "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "signature": false, "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "signature": false, "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "signature": false, "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "signature": false, "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "signature": false, "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "signature": false, "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "signature": false, "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "signature": false, "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "signature": false, "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "signature": false, "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "signature": false, "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "signature": false, "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "signature": false, "impliedFormat": 1}, {"version": "e61be3f894b41b7baa1fbd6a66893f2579bfad01d208b4ff61daef21493ef0a8", "signature": false, "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "signature": false, "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "signature": false, "impliedFormat": 1}, {"version": "615ba88d0128ed16bf83ef8ccbb6aff05c3ee2db1cc0f89ab50a4939bfc1943f", "signature": false, "impliedFormat": 1}, {"version": "a4d551dbf8746780194d550c88f26cf937caf8d56f102969a110cfaed4b06656", "signature": false, "impliedFormat": 1}, {"version": "8bd86b8e8f6a6aa6c49b71e14c4ffe1211a0e97c80f08d2c8cc98838006e4b88", "signature": false, "impliedFormat": 1}, {"version": "317e63deeb21ac07f3992f5b50cdca8338f10acd4fbb7257ebf56735bf52ab00", "signature": false, "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "signature": false, "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "signature": false, "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "signature": false, "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "signature": false, "impliedFormat": 1}, {"version": "1ca84b44ad1d8e4576f24904d8b95dd23b94ea67e1575f89614ac90062fc67f4", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6b042aa5d277ad6963e2837179fd2f8fbb01968ac67115b0833c0244e93d1d50", "signature": false, "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "signature": false, "impliedFormat": 1}, {"version": "8c0bcd6c6b67b4b503c11e91a1fb91522ed585900eab2ab1f61bba7d7caa9d6f", "signature": false, "impliedFormat": 1}, {"version": "9e025aa38cad40827cc30aca974fe33fe2c4652fe8c88f48dadbbbd6300c8b07", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3e58c4c18a031cbb17abec7a4ad0bd5ae9fc70c1f4ba1e7fb921ad87c504aca", "signature": false, "impliedFormat": 1}, {"version": "84c1930e33d1bb12ad01bcbe11d656f9646bd21b2fb2afd96e8e10615a021aef", "signature": false, "impliedFormat": 1}, {"version": "35ec8b6760fd7138bbf5809b84551e31028fb2ba7b6dc91d95d098bf212ca8b4", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "signature": false, "impliedFormat": 1}, {"version": "4b87f767c7bc841511113c876a6b8bf1fd0cb0b718c888ad84478b372ec486b1", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d04e3640dd9eb67f7f1e5bd3d0bf96c784666f7aefc8ac1537af6f2d38d4c29", "signature": false, "impliedFormat": 1}, {"version": "9d19808c8c291a9010a6c788e8532a2da70f811adb431c97520803e0ec649991", "signature": false, "impliedFormat": 1}, {"version": "5a369483ac4cfbdf0331c248deeb36140e6907db5e1daed241546b4a2055f82c", "signature": false, "impliedFormat": 1}, {"version": "e8f5b5cc36615c17d330eaf8eebbc0d6bdd942c25991f96ef122f246f4ff722f", "signature": false, "impliedFormat": 1}, {"version": "f0bd7e6d931657b59605c44112eaf8b980ba7f957a5051ed21cb93d978cf2f45", "signature": false, "impliedFormat": 1}, {"version": "71450bbc2d82821d24ca05699a533e72758964e9852062c53b30f31c36978ab8", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0ada07543808f3b967624645a8e1ccd446f8b01ade47842acf1328aec899fed0", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4c21aaa8257d7950a5b75a251d9075b6a371208fc948c9c8402f6690ef3b5b55", "signature": false, "impliedFormat": 1}, {"version": "b5895e6353a5d708f55d8685c38a235c3a6d8138e374dee8ceb8ffde5aa8002a", "signature": false, "impliedFormat": 1}, {"version": "5b75ca915164e4a7ad94a60729fe45b8a62e7750ab232d0122f8ccdd768f5314", "signature": false, "impliedFormat": 1}, {"version": "de735eca2c51dd8b860254e9fdb6d9ec19fe402dfe597c23090841ce3937cfc5", "signature": false, "impliedFormat": 1}, {"version": "4ff41188773cbf465807dd2f7059c7494cbee5115608efc297383832a1150c43", "signature": false, "impliedFormat": 1}, {"version": "5650cf3dace09e7c25d384e3e6b818b938f68f4e8de96f52d9c5a1b3db068e86", "signature": false, "impliedFormat": 1}, {"version": "1354ca5c38bd3fd3836a68e0f7c9f91f172582ba30ab15bb8c075891b91502b7", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5155da3047ef977944d791a2188ff6e6c225f6975cc1910ab7bb6838ab84cede", "signature": false, "impliedFormat": 1}, {"version": "93f437e1398a4f06a984f441f7fa7a9f0535c04399619b5c22e0b87bdee182cb", "signature": false, "impliedFormat": 1}, {"version": "afbe24ab0d74694372baa632ecb28bb375be53f3be53f9b07ecd7fc994907de5", "signature": false, "impliedFormat": 1}, {"version": "e16d218a30f6a6810b57f7e968124eaa08c7bb366133ea34bbf01e7cd6b8c0ad", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb8692dea24c27821f77e397272d9ed2eda0b95e4a75beb0fdda31081d15a8ae", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e043a1bc8fbf2a255bccf9bf27e0f1caf916c3b0518ea34aa72357c0afd42ec", "signature": false, "impliedFormat": 1}, {"version": "b4f70ec656a11d570e1a9edce07d118cd58d9760239e2ece99306ee9dfe61d02", "signature": false, "impliedFormat": 1}, {"version": "3bc2f1e2c95c04048212c569ed38e338873f6a8593930cf5a7ef24ffb38fc3b6", "signature": false, "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "signature": false, "impliedFormat": 1}, {"version": "f9d9d753d430ed050dc1bf2667a1bab711ccbb1c1507183d794cc195a5b085cc", "signature": false, "impliedFormat": 1}, {"version": "9eece5e586312581ccd106d4853e861aaaa1a39f8e3ea672b8c3847eedd12f6e", "signature": false, "impliedFormat": 1}, {"version": "5b6844ad931dcc1d3aca53268f4bd671428421464b1286746027aede398094f2", "signature": false, "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "signature": false, "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "signature": false, "impliedFormat": 1}, {"version": "0dbcebe2126d03936c70545e96a6e41007cf065be38a1ce4d32a39fcedefead4", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1851a3b4db78664f83901bb9cac9e45e03a37bb5933cc5bf37e10bb7e91ab4eb", "signature": false, "impliedFormat": 1}, {"version": "46c0484bf0a50d57256a8cfb87714450c2ecd1e5d0bc29f84740f16199f47d6a", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "12ed4559eba17cd977aa0db658d25c4047067444b51acfdcbf38470630642b23", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3ffabc95802521e1e4bcba4c88d8615176dc6e09111d920c7a213bdda6e1d65", "signature": false, "impliedFormat": 1}, {"version": "e31e51c55800014d926e3f74208af49cb7352803619855c89296074d1ecbb524", "signature": false, "impliedFormat": 1}, {"version": "ae56f65caf3be91108707bd8dfbccc2a57a91feb5daabf7165a06a945545ed26", "signature": false, "impliedFormat": 1}, {"version": "a136d5de521da20f31631a0a96bf712370779d1c05b7015d7019a9b2a0446ca9", "signature": false, "impliedFormat": 1}, {"version": "dfb96ba5177b68003deec9e773c47257da5c4c8a74053d8956389d832df72002", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "92d3070580cf72b4bb80959b7f16ede9a3f39e6f4ef2ac87cfa4561844fdc69f", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d3dffd70e6375b872f0b4e152de4ae682d762c61a24881ecc5eb9f04c5caf76f", "signature": false, "impliedFormat": 1}, {"version": "613deebaec53731ff6b74fe1a89f094b708033db6396b601df3e6d5ab0ec0a47", "signature": false, "impliedFormat": 1}, {"version": "d91a7d8b5655c42986f1bdfe2105c4408f472831c8f20cf11a8c3345b6b56c8c", "signature": false, "impliedFormat": 1}, {"version": "e56eb632f0281c9f8210eb8c86cc4839a427a4ffffcfd2a5e40b956050b3e042", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e8a979b8af001c9fc2e774e7809d233c8ca955a28756f52ee5dee88ccb0611d2", "signature": false, "impliedFormat": 1}, {"version": "cac793cc47c29e26e4ac3601dcb00b4435ebed26203485790e44f2ad8b6ad847", "signature": false, "impliedFormat": 1}, {"version": "8caa5c86be1b793cd5f599e27ecb34252c41e011980f7d61ae4989a149ff6ccc", "signature": false, "impliedFormat": 1}, {"version": "3609e455ffcba8176c8ce0aa57f8258fe10cf03987e27f1fab68f702b4426521", "signature": false, "impliedFormat": 1}, {"version": "d1bd4e51810d159899aad1660ccb859da54e27e08b8c9862b40cd36c1d9ff00f", "signature": false, "impliedFormat": 1}, {"version": "17ed71200119e86ccef2d96b73b02ce8854b76ad6bd21b5021d4269bec527b5f", "signature": false, "impliedFormat": 1}, {"version": "1cfa8647d7d71cb03847d616bd79320abfc01ddea082a49569fda71ac5ece66b", "signature": false, "impliedFormat": 1}, {"version": "bb7a61dd55dc4b9422d13da3a6bb9cc5e89be888ef23bbcf6558aa9726b89a1c", "signature": false, "impliedFormat": 1}, {"version": "db6d2d9daad8a6d83f281af12ce4355a20b9a3e71b82b9f57cddcca0a8964a96", "signature": false, "impliedFormat": 1}, {"version": "cfe4ef4710c3786b6e23dae7c086c70b4f4835a2e4d77b75d39f9046106e83d3", "signature": false, "impliedFormat": 1}, {"version": "cbea99888785d49bb630dcbb1613c73727f2b5a2cf02e1abcaab7bcf8d6bf3c5", "signature": false, "impliedFormat": 1}, {"version": "3a8bddb66b659f6bd2ff641fc71df8a8165bafe0f4b799cc298be5cd3755bb20", "signature": false, "impliedFormat": 1}, {"version": "a86f82d646a739041d6702101afa82dcb935c416dd93cbca7fd754fd0282ce1f", "signature": false, "impliedFormat": 1}, {"version": "2dad084c67e649f0f354739ec7df7c7df0779a28a4f55c97c6b6883ae850d1ce", "signature": false, "impliedFormat": 1}, {"version": "fa5bbc7ab4130dd8cdc55ea294ec39f76f2bc507a0f75f4f873e38631a836ca7", "signature": false, "impliedFormat": 1}, {"version": "df45ca1176e6ac211eae7ddf51336dc075c5314bc5c253651bae639defd5eec5", "signature": false, "impliedFormat": 1}, {"version": "cf86de1054b843e484a3c9300d62fbc8c97e77f168bbffb131d560ca0474d4a8", "signature": false, "impliedFormat": 1}, {"version": "196c960b12253fde69b204aa4fbf69470b26daf7a430855d7f94107a16495ab0", "signature": false, "impliedFormat": 1}, {"version": "ee15ea5dd7a9fc9f5013832e5843031817a880bf0f24f37a29fd8337981aae07", "signature": false, "impliedFormat": 1}, {"version": "bf24f6d35f7318e246010ffe9924395893c4e96d34324cde77151a73f078b9ad", "signature": false, "impliedFormat": 1}, {"version": "ea53732769832d0f127ae16620bd5345991d26bf0b74e85e41b61b27d74ea90f", "signature": false, "impliedFormat": 1}, {"version": "10595c7ff5094dd5b6a959ccb1c00e6a06441b4e10a87bc09c15f23755d34439", "signature": false, "impliedFormat": 1}, {"version": "9620c1ff645afb4a9ab4044c85c26676f0a93e8c0e4b593aea03a89ccb47b6d0", "signature": false, "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "signature": false, "impliedFormat": 1}, {"version": "a9af0e608929aaf9ce96bd7a7b99c9360636c31d73670e4af09a09950df97841", "signature": false, "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "signature": false, "impliedFormat": 1}, {"version": "c86fe861cf1b4c46a0fb7d74dffe596cf679a2e5e8b1456881313170f092e3fa", "signature": false, "impliedFormat": 1}, {"version": "08ed0b3f0166787f84a6606f80aa3b1388c7518d78912571b203817406e471da", "signature": false, "impliedFormat": 1}, {"version": "47e5af2a841356a961f815e7c55d72554db0c11b4cba4d0caab91f8717846a94", "signature": false, "impliedFormat": 1}, {"version": "65f43099ded6073336e697512d9b80f2d4fec3182b7b2316abf712e84104db00", "signature": false, "impliedFormat": 1}, {"version": "f5f541902bf7ae0512a177295de9b6bcd6809ea38307a2c0a18bfca72212f368", "signature": false, "impliedFormat": 1}, {"version": "b0decf4b6da3ebc52ea0c96095bdfaa8503acc4ac8e9081c5f2b0824835dd3bd", "signature": false, "impliedFormat": 1}, {"version": "ca1b882a105a1972f82cc58e3be491e7d750a1eb074ffd13b198269f57ed9e1b", "signature": false, "impliedFormat": 1}, {"version": "fc3e1c87b39e5ba1142f27ec089d1966da168c04a859a4f6aab64dceae162c2b", "signature": false, "impliedFormat": 1}, {"version": "3b414b99a73171e1c4b7b7714e26b87d6c5cb03d200352da5342ab4088a54c85", "signature": false, "impliedFormat": 1}, {"version": "61888522cec948102eba94d831c873200aa97d00d8989fdfd2a3e0ee75ec65a2", "signature": false, "impliedFormat": 1}, {"version": "4e10622f89fea7b05dd9b52fb65e1e2b5cbd96d4cca3d9e1a60bb7f8a9cb86a1", "signature": false, "impliedFormat": 1}, {"version": "74b2a5e5197bd0f2e0077a1ea7c07455bbea67b87b0869d9786d55104006784f", "signature": false, "impliedFormat": 1}, {"version": "59bf32919de37809e101acffc120596a9e45fdbab1a99de5087f31fdc36e2f11", "signature": false, "impliedFormat": 1}, {"version": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "signature": false, "impliedFormat": 1}, {"version": "faa03dffb64286e8304a2ca96dd1317a77db6bfc7b3fb385163648f67e535d77", "signature": false, "impliedFormat": 1}, {"version": "c40c848daad198266370c1c72a7a8c3d18d2f50727c7859fcfefd3ff69a7f288", "signature": false, "impliedFormat": 1}, {"version": "ac60bbee0d4235643cc52b57768b22de8c257c12bd8c2039860540cab1fa1d82", "signature": false, "impliedFormat": 1}, {"version": "6428e6edd944ce6789afdf43f9376c1f2e4957eea34166177625aaff4c0da1a0", "signature": false, "impliedFormat": 1}, {"version": "ada39cbb2748ab2873b7835c90c8d4620723aedf323550e8489f08220e477c7f", "signature": false, "impliedFormat": 1}, {"version": "6e5f5cee603d67ee1ba6120815497909b73399842254fc1e77a0d5cdc51d8c9c", "signature": false, "impliedFormat": 1}, {"version": "8dba67056cbb27628e9b9a1cba8e57036d359dceded0725c72a3abe4b6c79cd4", "signature": false, "impliedFormat": 1}, {"version": "70f3814c457f54a7efe2d9ce9d2686de9250bb42eb7f4c539bd2280a42e52d33", "signature": false, "impliedFormat": 1}, {"version": "154dd2e22e1e94d5bc4ff7726706bc0483760bae40506bdce780734f11f7ec47", "signature": false, "impliedFormat": 1}, {"version": "ef61792acbfa8c27c9bd113f02731e66229f7d3a169e3c1993b508134f1a58e0", "signature": false, "impliedFormat": 1}, {"version": "9c82171d836c47486074e4ca8e059735bf97b205e70b196535b5efd40cbe1bc5", "signature": false, "impliedFormat": 1}, {"version": "0131e203d8560edb39678abe10db42564a068f98c4ebd1ed9ffe7279c78b3c81", "signature": false, "impliedFormat": 1}, {"version": "f6404e7837b96da3ea4d38c4f1a3812c96c9dcdf264e93d5bdb199f983a3ef4b", "signature": false, "impliedFormat": 1}, {"version": "c5426dbfc1cf90532f66965a7aa8c1136a78d4d0f96d8180ecbfc11d7722f1a5", "signature": false, "impliedFormat": 1}, {"version": "65a15fc47900787c0bd18b603afb98d33ede930bed1798fc984d5ebb78b26cf9", "signature": false, "impliedFormat": 1}, {"version": "9d202701f6e0744adb6314d03d2eb8fc994798fc83d91b691b75b07626a69801", "signature": false, "impliedFormat": 1}, {"version": "de9d2df7663e64e3a91bf495f315a7577e23ba088f2949d5ce9ec96f44fba37d", "signature": false, "impliedFormat": 1}, {"version": "c7af78a2ea7cb1cd009cfb5bdb48cd0b03dad3b54f6da7aab615c2e9e9d570c5", "signature": false, "impliedFormat": 1}, {"version": "1ee45496b5f8bdee6f7abc233355898e5bf9bd51255db65f5ff7ede617ca0027", "signature": false, "impliedFormat": 1}, {"version": "8b8f00491431fe82f060dfe8c7f2180a9fb239f3d851527db909b83230e75882", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "db01d18853469bcb5601b9fc9826931cc84cc1a1944b33cad76fd6f1e3d8c544", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dba114fb6a32b355a9cfc26ca2276834d72fe0e94cd2c3494005547025015369", "signature": false, "impliedFormat": 1}, {"version": "903e299a28282fa7b714586e28409ed73c3b63f5365519776bf78e8cf173db36", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fa6c12a7c0f6b84d512f200690bfc74819e99efae69e4c95c4cd30f6884c526e", "signature": false, "impliedFormat": 1}, {"version": "f1c32f9ce9c497da4dc215c3bc84b722ea02497d35f9134db3bb40a8d918b92b", "signature": false, "impliedFormat": 1}, {"version": "b73c319af2cc3ef8f6421308a250f328836531ea3761823b4cabbd133047aefa", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e433b0337b8106909e7953015e8fa3f2d30797cea27141d1c5b135365bb975a6", "signature": false, "impliedFormat": 1}, {"version": "dd3900b24a6a8745efeb7ad27629c0f8a626470ac229c1d73f1fe29d67e44dca", "signature": false, "impliedFormat": 1}, {"version": "ddff7fc6edbdc5163a09e22bf8df7bef75f75369ebd7ecea95ba55c4386e2441", "signature": false, "impliedFormat": 1}, {"version": "106c6025f1d99fd468fd8bf6e5bda724e11e5905a4076c5d29790b6c3745e50c", "signature": false, "impliedFormat": 1}, {"version": "ec29be0737d39268696edcec4f5e97ce26f449fa9b7afc2f0f99a86def34a418", "signature": false, "impliedFormat": 1}, {"version": "aeab39e8e0b1a3b250434c3b2bb8f4d17bbec2a9dbce5f77e8a83569d3d2cbc2", "signature": false, "impliedFormat": 1}, {"version": "ec6cba1c02c675e4dd173251b156792e8d3b0c816af6d6ad93f1a55d674591aa", "signature": false, "impliedFormat": 1}, {"version": "b620391fe8060cf9bedc176a4d01366e6574d7a71e0ac0ab344a4e76576fcbb8", "signature": false, "impliedFormat": 1}, {"version": "d729408dfde75b451530bcae944cf89ee8277e2a9df04d1f62f2abfd8b03c1e1", "signature": false, "impliedFormat": 1}, {"version": "e15d3c84d5077bb4a3adee4c791022967b764dc41cb8fa3cfa44d4379b2c95f5", "signature": false, "impliedFormat": 1}, {"version": "5f58e28cd22e8fc1ac1b3bc6b431869f1e7d0b39e2c21fbf79b9fa5195a85980", "signature": false, "impliedFormat": 1}, {"version": "e1fc1a1045db5aa09366be2b330e4ce391550041fc3e925f60998ca0b647aa97", "signature": false, "impliedFormat": 1}, {"version": "63533978dcda286422670f6e184ac516805a365fb37a086eeff4309e812f1402", "signature": false, "impliedFormat": 1}, {"version": "43ba4f2fa8c698f5c304d21a3ef596741e8e85a810b7c1f9b692653791d8d97a", "signature": false, "impliedFormat": 1}, {"version": "31fb49ef3aa3d76f0beb644984e01eab0ea222372ea9b49bb6533be5722d756c", "signature": false, "impliedFormat": 1}, {"version": "33cd131e1461157e3e06b06916b5176e7a8ec3fce15a5cfe145e56de744e07d2", "signature": false, "impliedFormat": 1}, {"version": "889ef863f90f4917221703781d9723278db4122d75596b01c429f7c363562b86", "signature": false, "impliedFormat": 1}, {"version": "3556cfbab7b43da96d15a442ddbb970e1f2fc97876d055b6555d86d7ac57dae5", "signature": false, "impliedFormat": 1}, {"version": "437751e0352c6e924ddf30e90849f1d9eb00ca78c94d58d6a37202ec84eb8393", "signature": false, "impliedFormat": 1}, {"version": "48e8af7fdb2677a44522fd185d8c87deff4d36ee701ea003c6c780b1407a1397", "signature": false, "impliedFormat": 1}, {"version": "d11308de5a36c7015bb73adb5ad1c1bdaac2baede4cc831a05cf85efa3cc7f2f", "signature": false, "impliedFormat": 1}, {"version": "38e4684c22ed9319beda6765bab332c724103d3a966c2e5e1c5a49cf7007845f", "signature": false, "impliedFormat": 1}, {"version": "f9812cfc220ecf7557183379531fa409acd249b9e5b9a145d0d52b76c20862de", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e650298721abc4f6ae851e60ae93ee8199791ceec4b544c3379862f81f43178c", "signature": false, "impliedFormat": 1}, {"version": "2e4f37ffe8862b14d8e24ae8763daaa8340c0df0b859d9a9733def0eee7562d9", "signature": false, "impliedFormat": 1}, {"version": "13283350547389802aa35d9f2188effaeac805499169a06ef5cd77ce2a0bd63f", "signature": false, "impliedFormat": 1}, {"version": "680793958f6a70a44c8d9ae7d46b7a385361c69ac29dcab3ed761edce1c14ab8", "signature": false, "impliedFormat": 1}, {"version": "6ac6715916fa75a1f7ebdfeacac09513b4d904b667d827b7535e84ff59679aff", "signature": false, "impliedFormat": 1}, {"version": "42c169fb8c2d42f4f668c624a9a11e719d5d07dacbebb63cbcf7ef365b0a75b3", "signature": false, "impliedFormat": 1}, {"version": "913ddbba170240070bd5921b8f33ea780021bdf42fbdfcd4fcb2691b1884ddde", "signature": false, "impliedFormat": 1}, {"version": "b4e6d416466999ff40d3fe5ceb95f7a8bfb7ac2262580287ac1a8391e5362431", "signature": false, "impliedFormat": 1}, {"version": "5fe23bd829e6be57d41929ac374ee9551ccc3c44cee893167b7b5b77be708014", "signature": false, "impliedFormat": 1}, {"version": "0a626484617019fcfbfc3c1bc1f9e84e2913f1adb73692aa9075817404fb41a1", "signature": false, "impliedFormat": 1}, {"version": "438c7513b1df91dcef49b13cd7a1c4720f91a36e88c1df731661608b7c055f10", "signature": false, "impliedFormat": 1}, {"version": "cf185cc4a9a6d397f416dd28cca95c227b29f0f27b160060a95c0e5e36cda865", "signature": false, "impliedFormat": 1}, {"version": "0086f3e4ad898fd7ca56bb223098acfacf3fa065595182aaf0f6c4a6a95e6fbd", "signature": false, "impliedFormat": 1}, {"version": "efaa078e392f9abda3ee8ade3f3762ab77f9c50b184e6883063a911742a4c96a", "signature": false, "impliedFormat": 1}, {"version": "54a8bb487e1dc04591a280e7a673cdfb272c83f61e28d8a64cf1ac2e63c35c51", "signature": false, "impliedFormat": 1}, {"version": "021a9498000497497fd693dd315325484c58a71b5929e2bbb91f419b04b24cea", "signature": false, "impliedFormat": 1}, {"version": "9385cdc09850950bc9b59cca445a3ceb6fcca32b54e7b626e746912e489e535e", "signature": false, "impliedFormat": 1}, {"version": "2894c56cad581928bb37607810af011764a2f511f575d28c9f4af0f2ef02d1ab", "signature": false, "impliedFormat": 1}, {"version": "0a72186f94215d020cb386f7dca81d7495ab6c17066eb07d0f44a5bf33c1b21a", "signature": false, "impliedFormat": 1}, {"version": "84124384abae2f6f66b7fbfc03862d0c2c0b71b826f7dbf42c8085d31f1d3f95", "signature": false, "impliedFormat": 1}, {"version": "63a8e96f65a22604eae82737e409d1536e69a467bb738bec505f4f97cce9d878", "signature": false, "impliedFormat": 1}, {"version": "3fd78152a7031315478f159c6a5872c712ece6f01212c78ea82aef21cb0726e2", "signature": false, "impliedFormat": 1}, {"version": "b01bd582a6e41457bc56e6f0f9de4cb17f33f5f3843a7cf8210ac9c18472fb0f", "signature": false, "impliedFormat": 1}, {"version": "58b49e5c1def740360b5ae22ae2405cfac295fee74abd88d74ac4ea42502dc03", "signature": false, "impliedFormat": 1}, {"version": "512fc15cca3a35b8dbbf6e23fe9d07e6f87ad03c895acffd3087ce09f352aad0", "signature": false, "impliedFormat": 1}, {"version": "9a0946d15a005832e432ea0cd4da71b57797efb25b755cc07f32274296d62355", "signature": false, "impliedFormat": 1}, {"version": "a52ff6c0a149e9f370372fc3c715d7f2beee1f3bab7980e271a7ab7d313ec677", "signature": false, "impliedFormat": 1}, {"version": "fd933f824347f9edd919618a76cdb6a0c0085c538115d9a287fa0c7f59957ab3", "signature": false, "impliedFormat": 1}, {"version": "6ac6715916fa75a1f7ebdfeacac09513b4d904b667d827b7535e84ff59679aff", "signature": false, "impliedFormat": 1}, {"version": "6a1aa3e55bdc50503956c5cd09ae4cd72e3072692d742816f65c66ca14f4dfdd", "signature": false, "impliedFormat": 1}, {"version": "ab75cfd9c4f93ffd601f7ca1753d6a9d953bbedfbd7a5b3f0436ac8a1de60dfa", "signature": false, "impliedFormat": 1}, {"version": "f95180f03d827525ca4f990f49e17ec67198c316dd000afbe564655141f725cd", "signature": false, "impliedFormat": 1}, {"version": "b73cbf0a72c8800cf8f96a9acfe94f3ad32ca71342a8908b8ae484d61113f647", "signature": false, "impliedFormat": 1}, {"version": "bae6dd176832f6423966647382c0d7ba9e63f8c167522f09a982f086cd4e8b23", "signature": false, "impliedFormat": 1}, {"version": "1364f64d2fb03bbb514edc42224abd576c064f89be6a990136774ecdd881a1da", "signature": false, "impliedFormat": 1}, {"version": "c9958eb32126a3843deedda8c22fb97024aa5d6dd588b90af2d7f2bfac540f23", "signature": false, "impliedFormat": 1}, {"version": "950fb67a59be4c2dbe69a5786292e60a5cb0e8612e0e223537784c731af55db1", "signature": false, "impliedFormat": 1}, {"version": "e927c2c13c4eaf0a7f17e6022eee8519eb29ef42c4c13a31e81a611ab8c95577", "signature": false, "impliedFormat": 1}, {"version": "07ca44e8d8288e69afdec7a31fa408ce6ab90d4f3d620006701d5544646da6aa", "signature": false, "impliedFormat": 1}, {"version": "70246ad95ad8a22bdfe806cb5d383a26c0c6e58e7207ab9c431f1cb175aca657", "signature": false, "impliedFormat": 1}, {"version": "f00f3aa5d64ff46e600648b55a79dcd1333458f7a10da2ed594d9f0a44b76d0b", "signature": false, "impliedFormat": 1}, {"version": "772d8d5eb158b6c92412c03228bd9902ccb1457d7a705b8129814a5d1a6308fc", "signature": false, "impliedFormat": 1}, {"version": "4e4475fba4ed93a72f167b061cd94a2e171b82695c56de9899275e880e06ba41", "signature": false, "impliedFormat": 1}, {"version": "97c5f5d580ab2e4decd0a3135204050f9b97cd7908c5a8fbc041eadede79b2fa", "signature": false, "impliedFormat": 1}, {"version": "c99a3a5f2215d5b9d735aa04cec6e61ed079d8c0263248e298ffe4604d4d0624", "signature": false, "impliedFormat": 1}, {"version": "49b2375c586882c3ac7f57eba86680ff9742a8d8cb2fe25fe54d1b9673690d41", "signature": false, "impliedFormat": 1}, {"version": "802e797bcab5663b2c9f63f51bdf67eff7c41bc64c0fd65e6da3e7941359e2f7", "signature": false, "impliedFormat": 1}, {"version": "847e160d709c74cc714fbe1f99c41d3425b74cd47b1be133df1623cd87014089", "signature": false, "impliedFormat": 1}, {"version": "3ecfccf916fea7c6c34394413b55eb70e817a73e39b4417d6573e523784e3f8e", "signature": false, "impliedFormat": 1}, {"version": "5cdc27fbc5c166fc5c763a30ac21cbac9859dc5ba795d3230db6d4e52a1965bb", "signature": false, "impliedFormat": 1}, {"version": "6459054aabb306821a043e02b89d54da508e3a6966601a41e71c166e4ea1474f", "signature": false, "impliedFormat": 1}, {"version": "f416c9c3eee9d47ff49132c34f96b9180e50485d435d5748f0e8b72521d28d2e", "signature": false, "impliedFormat": 1}, {"version": "05c97cddbaf99978f83d96de2d8af86aded9332592f08ce4a284d72d0952c391", "signature": false, "impliedFormat": 1}, {"version": "14e5cdec6f8ae82dfd0694e64903a0a54abdfe37e1d966de3d4128362acbf35f", "signature": false, "impliedFormat": 1}, {"version": "bbc183d2d69f4b59fd4dd8799ffdf4eb91173d1c4ad71cce91a3811c021bf80c", "signature": false, "impliedFormat": 1}, {"version": "7b6ff760c8a240b40dab6e4419b989f06a5b782f4710d2967e67c695ef3e93c4", "signature": false, "impliedFormat": 1}, {"version": "8dbc4134a4b3623fc476be5f36de35c40f2768e2e3d9ed437e0d5f1c4cd850f6", "signature": false, "impliedFormat": 1}, {"version": "4e06330a84dec7287f7ebdd64978f41a9f70a668d3b5edc69d5d4a50b9b376bb", "signature": false, "impliedFormat": 1}, {"version": "65bfa72967fbe9fc33353e1ac03f0480aa2e2ea346d61ff3ea997dfd850f641a", "signature": false, "impliedFormat": 1}, {"version": "c06f0bb92d1a1a5a6c6e4b5389a5664d96d09c31673296cb7da5fe945d54d786", "signature": false, "impliedFormat": 1}, {"version": "f974e4a06953682a2c15d5bd5114c0284d5abf8bc0fe4da25cb9159427b70072", "signature": false, "impliedFormat": 1}, {"version": "872caaa31423f4345983d643e4649fb30f548e9883a334d6d1c5fff68ede22d4", "signature": false, "impliedFormat": 1}, {"version": "94404c4a878fe291e7578a2a80264c6f18e9f1933fbb57e48f0eb368672e389c", "signature": false, "impliedFormat": 1}, {"version": "5c1b7f03aa88be854bc15810bfd5bd5a1943c5a7620e1c53eddd2a013996343e", "signature": false, "impliedFormat": 1}, {"version": "09dfc64fcd6a2785867f2368419859a6cc5a8d4e73cbe2538f205b1642eb0f51", "signature": false, "impliedFormat": 1}, {"version": "bcf6f0a323653e72199105a9316d91463ad4744c546d1271310818b8cef7c608", "signature": false, "impliedFormat": 1}, {"version": "01aa917531e116485beca44a14970834687b857757159769c16b228eb1e49c5f", "signature": false, "impliedFormat": 1}, {"version": "351475f9c874c62f9b45b1f0dc7e2704e80dfd5f1af83a3a9f841f9dfe5b2912", "signature": false, "impliedFormat": 1}, {"version": "ac457ad39e531b7649e7b40ee5847606eac64e236efd76c5d12db95bf4eacd17", "signature": false, "impliedFormat": 1}, {"version": "187a6fdbdecb972510b7555f3caacb44b58415da8d5825d03a583c4b73fde4cf", "signature": false, "impliedFormat": 1}, {"version": "d4c3250105a612202289b3a266bb7e323db144f6b9414f9dea85c531c098b811", "signature": false, "impliedFormat": 1}, {"version": "95b444b8c311f2084f0fb51c616163f950fb2e35f4eaa07878f313a2d36c98a4", "signature": false, "impliedFormat": 1}, {"version": "741067675daa6d4334a2dc80a4452ca3850e89d5852e330db7cb2b5f867173b1", "signature": false, "impliedFormat": 1}, {"version": "f8acecec1114f11690956e007d920044799aefeb3cece9e7f4b1f8a1d542b2c9", "signature": false, "impliedFormat": 1}, {"version": "178071ccd043967a58c5d1a032db0ddf9bd139e7920766b537d9783e88eb615e", "signature": false, "impliedFormat": 1}, {"version": "3a17f09634c50cce884721f54fd9e7b98e03ac505889c560876291fcf8a09e90", "signature": false, "impliedFormat": 1}, {"version": "32531dfbb0cdc4525296648f53b2b5c39b64282791e2a8c765712e49e6461046", "signature": false, "impliedFormat": 1}, {"version": "0ce1b2237c1c3df49748d61568160d780d7b26693bd9feb3acb0744a152cd86d", "signature": false, "impliedFormat": 1}, {"version": "e489985388e2c71d3542612685b4a7db326922b57ac880f299da7026a4e8a117", "signature": false, "impliedFormat": 1}, {"version": "5cad4158616d7793296dd41e22e1257440910ea8d01c7b75045d4dfb20c5a41a", "signature": false, "impliedFormat": 1}, {"version": "04d3aad777b6af5bd000bfc409907a159fe77e190b9d368da4ba649cdc28d39e", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74efc1d6523bd57eb159c18d805db4ead810626bc5bc7002a2c7f483044b2e0f", "signature": false, "impliedFormat": 1}, {"version": "19252079538942a69be1645e153f7dbbc1ef56b4f983c633bf31fe26aeac32cd", "signature": false, "impliedFormat": 1}, {"version": "bc11f3ac00ac060462597add171220aed628c393f2782ac75dd29ff1e0db871c", "signature": false, "impliedFormat": 1}, {"version": "616775f16134fa9d01fc677ad3f76e68c051a056c22ab552c64cc281a9686790", "signature": false, "impliedFormat": 1}, {"version": "65c24a8baa2cca1de069a0ba9fba82a173690f52d7e2d0f1f7542d59d5eb4db0", "signature": false, "impliedFormat": 1}, {"version": "f9fe6af238339a0e5f7563acee3178f51db37f32a2e7c09f85273098cee7ec49", "signature": false, "impliedFormat": 1}, {"version": "3b0b1d352b8d2e47f1c4df4fb0678702aee071155b12ef0185fce9eb4fa4af1e", "signature": false, "impliedFormat": 1}, {"version": "77e71242e71ebf8528c5802993697878f0533db8f2299b4d36aa015bae08a79c", "signature": false, "impliedFormat": 1}, {"version": "a344403e7a7384e0e7093942533d309194ad0a53eca2a3100c0b0ab4d3932773", "signature": false, "impliedFormat": 1}, {"version": "b7fff2d004c5879cae335db8f954eb1d61242d9f2d28515e67902032723caeab", "signature": false, "impliedFormat": 1}, {"version": "5f3dc10ae646f375776b4e028d2bed039a93eebbba105694d8b910feebbe8b9c", "signature": false, "impliedFormat": 1}, {"version": "bb18bf4a61a17b4a6199eb3938ecfa4a59eb7c40843ad4a82b975ab6f7e3d925", "signature": false, "impliedFormat": 1}, {"version": "4545c1a1ceca170d5d83452dd7c4994644c35cf676a671412601689d9a62da35", "signature": false, "impliedFormat": 1}, {"version": "e9b6fc05f536dfddcdc65dbcf04e09391b1c968ab967382e48924f5cb90d88e1", "signature": false, "impliedFormat": 1}, {"version": "a2d648d333cf67b9aeac5d81a1a379d563a8ffa91ddd61c6179f68de724260ff", "signature": false, "impliedFormat": 1}, {"version": "2b664c3cc544d0e35276e1fb2d4989f7d4b4027ffc64da34ec83a6ccf2e5c528", "signature": false, "impliedFormat": 1}, {"version": "a3f41ed1b4f2fc3049394b945a68ae4fdefd49fa1739c32f149d32c0545d67f5", "signature": false, "impliedFormat": 1}, {"version": "3cd8f0464e0939b47bfccbb9bb474a6d87d57210e304029cd8eb59c63a81935d", "signature": false, "impliedFormat": 1}, {"version": "47699512e6d8bebf7be488182427189f999affe3addc1c87c882d36b7f2d0b0e", "signature": false, "impliedFormat": 1}, {"version": "3026abd48e5e312f2328629ede6e0f770d21c3cd32cee705c450e589d015ee09", "signature": false, "impliedFormat": 1}, {"version": "8b140b398a6afbd17cc97c38aea5274b2f7f39b1ae5b62952cfe65bf493e3e75", "signature": false, "impliedFormat": 1}, {"version": "7663d2c19ce5ef8288c790edba3d45af54e58c84f1b37b1249f6d49d962f3d91", "signature": false, "impliedFormat": 1}, {"version": "5cce3b975cdb72b57ae7de745b3c5de5790781ee88bcb41ba142f07c0fa02e97", "signature": false, "impliedFormat": 1}, {"version": "00bd6ebe607246b45296aa2b805bd6a58c859acecda154bfa91f5334d7c175c6", "signature": false, "impliedFormat": 1}, {"version": "ad036a85efcd9e5b4f7dd5c1a7362c8478f9a3b6c3554654ca24a29aa850a9c5", "signature": false, "impliedFormat": 1}, {"version": "fedebeae32c5cdd1a85b4e0504a01996e4a8adf3dfa72876920d3dd6e42978e7", "signature": false, "impliedFormat": 1}, {"version": "0d28b974a7605c4eda20c943b3fa9ae16cb452c1666fc9b8c341b879992c7612", "signature": false, "impliedFormat": 1}, {"version": "cdf21eee8007e339b1b9945abf4a7b44930b1d695cc528459e68a3adc39a622e", "signature": false, "impliedFormat": 1}, {"version": "db036c56f79186da50af66511d37d9fe77fa6793381927292d17f81f787bb195", "signature": false, "impliedFormat": 1}, {"version": "87ac2fb61e629e777f4d161dff534c2023ee15afd9cb3b1589b9b1f014e75c58", "signature": false, "impliedFormat": 1}, {"version": "13c8b4348db91e2f7d694adc17e7438e6776bc506d5c8f5de9ad9989707fa3fe", "signature": false, "impliedFormat": 1}, {"version": "3c1051617aa50b38e9efaabce25e10a5dd9b1f42e372ef0e8a674076a68742ed", "signature": false, "impliedFormat": 1}, {"version": "07a3e20cdcb0f1182f452c0410606711fbea922ca76929a41aacb01104bc0d27", "signature": false, "impliedFormat": 1}, {"version": "1de80059b8078ea5749941c9f863aa970b4735bdbb003be4925c853a8b6b4450", "signature": false, "impliedFormat": 1}, {"version": "1d079c37fa53e3c21ed3fa214a27507bda9991f2a41458705b19ed8c2b61173d", "signature": false, "impliedFormat": 1}, {"version": "4cd4b6b1279e9d744a3825cbd7757bbefe7f0708f3f1069179ad535f19e8ed2c", "signature": false, "impliedFormat": 1}, {"version": "5835a6e0d7cd2738e56b671af0e561e7c1b4fb77751383672f4b009f4e161d70", "signature": false, "impliedFormat": 1}, {"version": "c0eeaaa67c85c3bb6c52b629ebbfd3b2292dc67e8c0ffda2fc6cd2f78dc471e6", "signature": false, "impliedFormat": 1}, {"version": "4b7f74b772140395e7af67c4841be1ab867c11b3b82a51b1aeb692822b76c872", "signature": false, "impliedFormat": 1}, {"version": "27be6622e2922a1b412eb057faa854831b95db9db5035c3f6d4b677b902ab3b7", "signature": false, "impliedFormat": 1}, {"version": "b95a6f019095dd1d48fd04965b50dfd63e5743a6e75478343c46d2582a5132bf", "signature": false, "impliedFormat": 99}, {"version": "c2008605e78208cfa9cd70bd29856b72dda7ad89df5dc895920f8e10bcb9cd0a", "signature": false, "impliedFormat": 99}, {"version": "b97cb5616d2ab82a98ec9ada7b9e9cabb1f5da880ec50ea2b8dc5baa4cbf3c16", "signature": false, "impliedFormat": 99}, {"version": "d23df9ff06ae8bf1dcb7cc933e97ae7da418ac77749fecee758bb43a8d69f840", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "040c71dde2c406f869ad2f41e8d4ce579cc60c8dbe5aa0dd8962ac943b846572", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3586f5ea3cc27083a17bd5c9059ede9421d587286d5a47f4341a4c2d00e4fa91", "signature": false, "impliedFormat": 1}, {"version": "a6df929821e62f4719551f7955b9f42c0cd53c1370aec2dd322e24196a7dfe33", "signature": false, "impliedFormat": 1}, {"version": "b789bf89eb19c777ed1e956dbad0925ca795701552d22e68fd130a032008b9f9", "signature": false, "impliedFormat": 1}, {"version": "e462a655754db9df18b4a657454a7b6a88717ffded4e89403b2b3a47c6603fc3", "signature": false}, {"version": "ec2f5819760220cdc4658dc22d5e512a1bb6ec72fa662e55e139ea70a68aef46", "signature": false}, {"version": "1d7ee0c4eb734d59b6d962bc9151f6330895067cd3058ce6a3cd95347ef5c6e8", "signature": false, "impliedFormat": 99}, {"version": "b57d734a47e4a491e0e698936e012cd9008526e2bd6a46dd7b78b557545b31df", "signature": false}, {"version": "c0d6d887021adcd63368403ed96acd5341b4c07f65b60b5de87e3001911ab95d", "signature": false}, {"version": "9971931daaf18158fc38266e838d56eb5d9d1f13360b1181bb4735a05f534c03", "signature": false, "impliedFormat": 99}, {"version": "50cf7a23fc93928995caec8d7956206990f82113beeb6b3242dae8124edc3ca0", "signature": false, "impliedFormat": 99}, {"version": "62443d6fbad6830cc1ec6a96ccb346b9c8fac320d954f7ba14ec84ac489c89e5", "signature": false, "impliedFormat": 99}, {"version": "bd85074aed3cfd83d906643c80cda912732d688b0a20791cd8df5b7ff0eba59e", "signature": false, "impliedFormat": 99}, {"version": "909e8848dd7005329d4841027b51d09efcfb131e14268b091e830ab7adea9849", "signature": false, "impliedFormat": 99}, {"version": "0c5b705d31420477189618154d1b6a9bb62a34fa6055f56ade1a316f6adb6b3a", "signature": false, "impliedFormat": 99}, {"version": "352031ac2e53031b69a09355e09ad7d95361edf32cc827cfe2417d80247a5a50", "signature": false, "impliedFormat": 99}, {"version": "853b8bdb5da8c8e5d31e4d715a8057d8e96059d6774b13545c3616ed216b890c", "signature": false, "impliedFormat": 99}, {"version": "b9bd72693123f4548f67d5ba060cedc22755606d3bd63bb1d719970086799965", "signature": false, "impliedFormat": 99}, {"version": "9bd5be6049c58f5a7a1699c3c8c4db44d634f2a861de445dda907011167317b1", "signature": false, "impliedFormat": 99}, {"version": "1af42015f6353472dd424dcaa8b6909dfe90ce03978e1755e356780ff4ed0eb5", "signature": false, "impliedFormat": 99}, {"version": "c363b57a3dfab561bfe884baacf8568eea085bd5e11ccf0992fac67537717d90", "signature": false, "impliedFormat": 99}, {"version": "1757a53a602a8991886070f7ba4d81258d70e8dca133b256ae6a1a9f08cd73b3", "signature": false, "impliedFormat": 99}, {"version": "084c09a35a9611e1777c02343c11ab8b1be48eb4895bbe6da90222979940b4a6", "signature": false, "impliedFormat": 99}, {"version": "4b3049a2c849f0217ff4def308637931661461c329e4cf36aeb31db34c4c0c64", "signature": false, "impliedFormat": 99}, {"version": "6245aa515481727f994d1cf7adfc71e36b5fc48216a92d7e932274cee3268000", "signature": false, "impliedFormat": 99}, {"version": "d542fb814a8ceb7eb858ecd5a41434274c45a7d511b9d46feb36d83b437b08d5", "signature": false, "impliedFormat": 99}, {"version": "998d9f1da9ec63fca4cc1acb3def64f03d6bd1df2da1519d9249c80cfe8fece6", "signature": false, "impliedFormat": 99}, {"version": "b7d9ca4e3248f643fa86ff11872623fdc8ed2c6009836bec0e38b163b6faed0c", "signature": false, "impliedFormat": 99}, {"version": "ac7a28ab421ea564271e1a9de78d70d68c65fab5cbb6d5c5568afcf50496dd61", "signature": false, "impliedFormat": 99}, {"version": "d4f7a7a5f66b9bc6fbfd53fa08dcf8007ff752064df816da05edfa35abd2c97c", "signature": false, "impliedFormat": 99}, {"version": "1f38ecf63dead74c85180bf18376dc6bc152522ef3aedf7b588cadbbd5877506", "signature": false, "impliedFormat": 99}, {"version": "24af06c15fba5a7447d97bcacbcc46997c3b023e059c040740f1c6d477929142", "signature": false, "impliedFormat": 99}, {"version": "facde2bec0f59cf92f4635ece51b2c3fa2d0a3bbb67458d24af61e7e6b8f003c", "signature": false, "impliedFormat": 99}, {"version": "4669194e4ca5f7c160833bbb198f25681e629418a6326aba08cf0891821bfe8f", "signature": false, "impliedFormat": 99}, {"version": "f919471289119d2e8f71aba81869b01f30f790e8322cf5aa7e7dee8c8dadd00a", "signature": false, "impliedFormat": 99}, {"version": "a95cd11c5c8bc03eab4011f8e339a48f9a87293e90c0bf3e9003d7a6f833f557", "signature": false, "impliedFormat": 99}, {"version": "e9bc0db0144701fab1e98c4d595a293c7c840d209b389144142f0adbc36b5ec2", "signature": false, "impliedFormat": 99}, {"version": "b1f00d7e185339b76f12179fa934088e28a92eb705f512fbe813107f0e2e2eb8", "signature": false, "impliedFormat": 99}, {"version": "b28a60406a3d803788bc1335f4fc5459b06b6cfe3716fcdb59ed75c9e08970c2", "signature": false}, {"version": "1a8a2c59ef5d3469dad1fbeb6ad963d4fa28f36541693a804153c856a1710e71", "signature": false}, {"version": "e41123a9f0110c799cfeaf56109f67ab0cb9ea091b851811ff44d16c70c3fea2", "signature": false}, {"version": "b094656b421a7c5e8bd8b2a2733c3a22c9a3d868c90664579df97e9899898434", "signature": false}, {"version": "278d062eaaf2f56ec64a2ace4316d9c96e61a224d0d675e1a0ede568e20c2b26", "signature": false}, {"version": "d6481a40a66c864ff7cc5abb3bf78c6bd0608011c145f8fb0d944c9871718a0b", "signature": false}, {"version": "001ce58a981502018a7f313186df9ee2b143012269593d08a005592ee014e155", "signature": false}, {"version": "c927a1208c521bb012030d0ed1395c39cef2b0cf217495b429d733e64a74bed4", "signature": false}, {"version": "4c3b8de3c02b1e4b1423d482abd745cbd16f658e05edbe878c5cf74d7bba111c", "signature": false}, {"version": "a7986a40940685b77fdbe71122487337e14aa758d51b11f1107f37af62b99ce1", "signature": false}, {"version": "74dfa565a45712c8ae9624bf3992a6c862bc082186461d739dd2eeb315eb6545", "signature": false}, {"version": "99ba5b5d5f7b8de9af31c4ff4f623ab98f8eb28ace3b1a723cb643bc3b49f6a7", "signature": false}, {"version": "9abb44d93b3569a2940f5f6286a2b3b78c6991ac172a3d9c39e5ffee082eb956", "signature": false}, {"version": "dfb8a95ebd79cca55302743f86943c4bf4e52022627c873a1cc85429c4ada74f", "signature": false}, {"version": "7c061e2d07cf311dbbfabb113a77da3a51f9f519518e69f0637661652249ef8e", "signature": false}, {"version": "d04f947114fa00a20ee3c3182bb2863c30869df93293cc673f200defadbd69d9", "signature": false, "impliedFormat": 1}, {"version": "4c629a21fb1b4f2428660f662d5fef6282e359d369f9e5ec5fd6ac197c1906ee", "signature": false, "impliedFormat": 1}, {"version": "785926dee839d0b3f5e479615d5653d77f6a9ef8aa4eea5bbdce2703c860b254", "signature": false, "impliedFormat": 1}, {"version": "66d5c68894bb2975727cd550b53cd6f9d99f7cb77cb0cbecdd4af1c9332b01dd", "signature": false, "impliedFormat": 1}, {"version": "a25e1d0a7361b34c12d07305c1ee1cb79ef14572e92d587464218d25edf29b73", "signature": false, "impliedFormat": 1}, {"version": "77cbca5dde926b51f8508b91761611e5c4bdf65ffee8cac9604639a41c50d686", "signature": false}, {"version": "b31d148551b6d51a7bfac348e27f69baecb477402422f15904b4eed77a1de811", "signature": false}, {"version": "4c93bb71bb61f213b3844db22000d7935e5dcb1cf1490f231616a963d6bd5832", "signature": false}, {"version": "e515cd00f0e9c197e8ad2fab9df4c9f5b4addb9c50072cb6d4223e3e2409e42a", "signature": false}, {"version": "94e1809e45577bad3b58a5e808cb7d76855fef467cdac314f05ebe060242b992", "signature": false}, {"version": "207764c6e73aad189cbe0662cfe9823d6532d31a4fb94adefdfe436850654327", "signature": false, "impliedFormat": 99}, {"version": "5c557f3e68bf69e00f1fddfe0743bce42afe8c1873478a36756850ab5ea088af", "signature": false}, {"version": "e5db22ed1c3877609dcfdbe809ae5106ef628f8ca776df98280a6e6acae931e2", "signature": false}, {"version": "a66e37fbecb69a24fdbadb602c9adc1add456a753246b83c1e1b8da0dafceb36", "signature": false}, {"version": "01066e29c88987c3215085c446ade095d059bcc23fb90f9a646039c413452569", "signature": false}, {"version": "6ad2b242d158ee8f7c455ede9378f43d63d4f6bbef8f282e7b9c93f1725fce54", "signature": false}, {"version": "78b1733f0c9081b502b5d3d5a42c39b54640824d02dcd72735e82c1b09e4445b", "signature": false}, {"version": "98314c52745b16739c2b381063b1f0977cdd40795a9b85ade782533890cd2dd7", "signature": false}, {"version": "dc57fab39c48d0e0396b49b016b3cd396d6d52ef179baedae6200badbe569652", "signature": false}, {"version": "e1ce19af186b56837ccd23771d8a14113b0652de6e29e25ba345c196826d7ea9", "signature": false}, {"version": "c6503e2a43dd580ece2f344c044d112c18cfe24a237c4f328b4514e50121c809", "signature": false}, {"version": "c8e857b6224783e90301f09988fb3c237fe24f4ebf04778d0cbe8147a26fffe7", "signature": false, "impliedFormat": 1}, {"version": "df33f22efcbdd885a1ea377b014e0c1dfbe2e42d184d85b26ea38db8ee7834c4", "signature": false, "impliedFormat": 1}, {"version": "f400febd2140549f95c47b2b9a45841c495dfeb51cc1639950fa307cd06a7213", "signature": false, "impliedFormat": 1}, {"version": "7048016c91c6203433420b9e16db56eec9c3f5d5a1301398e9907ac1fed63b58", "signature": false, "impliedFormat": 1}, {"version": "a4234645829a455706bf2d7b85642ee3c96bfe1cfddc9918e25bac9ce2062465", "signature": false, "impliedFormat": 1}, {"version": "9ff2d17592dec933b2b9e423fab8b8bc20feed486f16d35c75edd77c061de6e3", "signature": false, "impliedFormat": 1}, {"version": "fe9fc5b80b53a1982fe8fc0f14a002941b471213717536987d0cf4093a0c90a0", "signature": false, "impliedFormat": 1}, {"version": "4921f21de15ba1e7d1d5c83cf17466d30d4371bc9acf0c2c98015ebc646702ef", "signature": false, "impliedFormat": 1}, {"version": "f728f13a2965aacfb75807a27837509c2ab20a4bb7b0c9242e9b5ca2e5576d22", "signature": false, "impliedFormat": 1}, {"version": "c340ac804b0c549d62956f78a877dda3b150e79954be0673e1fc55f4a415f118", "signature": false, "impliedFormat": 1}, {"version": "2bfe95f5f0ea1a7928d7495c4f3df92cdc7b24872f50b4584e90350255181839", "signature": false, "impliedFormat": 1}, {"version": "9dfe677f6d3a486eebe1101b4cf6d4ec1c4f9ee24cc5b5391f27b1a519c926f7", "signature": false, "impliedFormat": 1}, {"version": "2766c9a60df883b515c418a938f3c8fd932241c89aba12aedf418e02a73017ce", "signature": false, "impliedFormat": 1}, {"version": "394967bc5f7707312a95cd7da0e5b30b736b7ab2f25817a8fea2d73b9398d102", "signature": false, "impliedFormat": 1}, {"version": "014a4afcc1674f40c7d77ca215e68bb3b0a254c2c925bcaa9932b6fb8f1ccd4e", "signature": false, "impliedFormat": 1}, {"version": "f816538db9388ac17bd354cf38d52da6c01d9a83f0589b3ff579af80cff0c8c6", "signature": false, "impliedFormat": 1}, {"version": "d2e0c04dce50f51b98ee32fd461dfa6e416a4b703c3d6d7e7fb7e68eca57a8de", "signature": false, "impliedFormat": 1}, {"version": "a8995e0a2eae0cdcd287dca4cf468ea640a270967ed32678d6fbf89e9f56d76d", "signature": false, "impliedFormat": 1}, {"version": "b151ad192b8e11b5ca8234d589abd2ae9c3fc229cdbe2651e9599f104fe5aa6b", "signature": false, "impliedFormat": 1}, {"version": "c37f352ab276b3cd4117f29e4cc70ed8ac911f3d63758ca45202a1a052fa9d00", "signature": false, "impliedFormat": 1}, {"version": "c97ffd10ec4e8d2ae3da391ca8a7ff71b745594588acc5d5bdef9c6da3e221bc", "signature": false, "impliedFormat": 1}, {"version": "74c373c562b48a0bde3ee68ac563403883b81cabe15c5ada4642a559cbd5d04e", "signature": false, "impliedFormat": 1}, {"version": "d42fe36f52e0ae09274753ed0fdedb32c42c2ad6ad247c81e6bd9982d1762004", "signature": false, "impliedFormat": 1}, {"version": "87f162804c7a5615d3ea9bdb2c828cd1d1f8378d5e2a9c3be1bd45c12f1fc1a5", "signature": false, "impliedFormat": 1}, {"version": "ccb92f285e2f3a3462262945fa59506aebe6ec569e9fec223d45d41c7c6cd447", "signature": false, "impliedFormat": 1}, {"version": "04e45000cf1381e6a84196aa01ca811ab192ca0a09debacc9e75dcfc6777bae1", "signature": false, "impliedFormat": 1}, {"version": "566007f48fa4cc7d29e4cb5cce9c315ccd52b72300d2d45ab0c639889e42d455", "signature": false, "impliedFormat": 1}, {"version": "4c2f8fb8a8f4afce6e05b9c554c012eb50147084933d78f7d218108740afd803", "signature": false, "impliedFormat": 1}, {"version": "6f72b3ebad0276cfcc7291fd2aefd1fbbd229487ec1acbbad03e798e8760e02e", "signature": false, "impliedFormat": 1}, {"version": "096681898d7131c1183f164ccfec478d99a9efa3744a1b6617116bc6713ed7be", "signature": false, "impliedFormat": 1}, {"version": "2c9626288e967ebb03ec2bc27ea504f6f829b1686f65b86fd5074d53e0160d70", "signature": false, "impliedFormat": 1}, {"version": "4de35fb3800a92324c59c1d2ed28a4dc1284d507d27ef2eed680c2f9ebb32cd2", "signature": false, "impliedFormat": 1}, {"version": "4c3cccf01f76ca4292746b6dfebd6df4382eb7a05315724116feacecf952f106", "signature": false, "impliedFormat": 1}, {"version": "492d1d21f79a8fa084e9dfd8fab89247301a49f1a0c12765b99c30a0ad8629ff", "signature": false, "impliedFormat": 1}, {"version": "69872cabf40dd4399939184cd7c5e47da62a9df811d3f56d193a437817a85b21", "signature": false, "impliedFormat": 1}, {"version": "19d00382e69115eeb1214d9b865030b61ec14f1bd5e91fb6e2b75acf5a6bef80", "signature": false, "impliedFormat": 1}, {"version": "3c3045d2661ef44458559f6bd48ebb47ccdfcbc513d859dc60c5e18e0544ac87", "signature": false, "impliedFormat": 1}, {"version": "e1de43a7fb0dda59dd9ed398fa306abdcb99da16b54edd3c7dc5e1a45d7e91df", "signature": false, "impliedFormat": 1}, {"version": "8301449ecbf03d5f893c298863fb66d97f1becb31f157276bdba7c708174a5be", "signature": false, "impliedFormat": 1}, {"version": "a556bdee2de2416a026022aeb260b5d684da34e322b5a95c7503143e51787b4f", "signature": false, "impliedFormat": 1}, {"version": "e8bc04f55c1b3da172412955b2785de54f2e1f2c9cb8949c0748ff143525310e", "signature": false, "impliedFormat": 1}, {"version": "683ad3639d8a96cfc782d672c44797d13c735ca9792d6c57e2fa5ada89e18e0c", "signature": false, "impliedFormat": 1}, {"version": "25b171a82c55909032e85448d89f8409e045a24a2b0458080bf304845b29b6ba", "signature": false, "impliedFormat": 1}, {"version": "ce25751e5374e1f13100276ecf2e2e8aac4d4c7229f762b3dc206639640954b8", "signature": false, "impliedFormat": 1}, {"version": "2f0a5a8ef5c6f5866d3caf04151422d05e64765ee250a7e9defc62908cfe73af", "signature": false, "impliedFormat": 1}, {"version": "79726fbe0854724f5bc3f16d4e40c0b320bbaa7a6296d1d782d70909f3b3a2eb", "signature": false, "impliedFormat": 1}, {"version": "6d391889910947acbe7d110271463ef74e7f65ae372d355756b1a6b0a987168d", "signature": false, "impliedFormat": 1}, {"version": "b3dadc705ad865a3acd5b40561ac0dcbce38fa28872ecb903eb586bd64cfa8b6", "signature": false, "impliedFormat": 1}, {"version": "8181adc6c7145eb6b2596249f3a2e1ff2fa7ebc604e73fe583f98c4b40916d6a", "signature": false, "impliedFormat": 1}, {"version": "dc84bb520982504eb30b09b870b32be8eccff2cd9beb963efd6a78971ae104b6", "signature": false, "impliedFormat": 1}, {"version": "bafdca74b47f54e116a9f2d589d39f1c677c777198b96a677a2d2f628b43c8f5", "signature": false, "impliedFormat": 1}, {"version": "9ccc168fc7cb696b5f60f216c72881db1f6c2d8e39eadd6c68130711f8eddd19", "signature": false, "impliedFormat": 1}, {"version": "6187a2dae6a9d910f272bfae324625437343f43a6ff48a28a5c5dd5e9cfc2d5f", "signature": false, "impliedFormat": 1}, {"version": "f063f87a44b1e92948bd5ef6db5b8cadef75218126e75ff02df83196e2b43c4b", "signature": false, "impliedFormat": 1}, {"version": "333df4996910e46b00aa9b7c8be938f6c5c99bfbf3a306596e56af9fff485acb", "signature": false, "impliedFormat": 1}, {"version": "deaf2e9bfb510a40e9413d5e940f96bf5a98a144b4e09a0e512efe12bfe10e9b", "signature": false, "impliedFormat": 1}, {"version": "de2395fb1d7aa90b75e52395ca02441e3a5ec66aa4283fb9ced22e05c8591159", "signature": false, "impliedFormat": 1}, {"version": "64be79c9e846ee074b3a6fb3becdbb7ac2b0386e1e1c680e43984ec8e2c2bbb9", "signature": false, "impliedFormat": 1}, {"version": "9c09e723f7747efc123e19f0ced5f3e144bbc3f40a6e1644a8c23437c4e3527f", "signature": false, "impliedFormat": 1}, {"version": "36fc129c8e3ad288656ea0e9ba0112728c7ec9507c75c6a3bce6d66f821a31d5", "signature": false, "impliedFormat": 1}, {"version": "3771470dde36546305e0431b0f107e2175d94e11f09b116611156f134364127e", "signature": false, "impliedFormat": 1}, {"version": "18c6715ca6b4304a314ff9adb864bd9266fc73813efd33d2992a7c6a8c6e7f73", "signature": false, "impliedFormat": 1}, {"version": "90cde8ac2173d2008c51996e52db2113e7a277718689f59cd3507f934ced2ac2", "signature": false, "impliedFormat": 1}, {"version": "69d01aac664fe15d1f3135885cd9652cca6d7d3591787124ae88c6264140f4b1", "signature": false, "impliedFormat": 1}, {"version": "55ab3dd3c8452b12f9097653247c83d49530b7ea5fe2cb9ef887434e366aee8c", "signature": false, "impliedFormat": 1}, {"version": "abd2ce77050bfd6da9017f3e4d7661e11f5dc1c5323b780587829c49fcac0d26", "signature": false, "impliedFormat": 1}, {"version": "d9dfcbbd2f1229ce6216cb36c23d106487a66f44d72e68fd9b6cb21186b360cd", "signature": false, "impliedFormat": 1}, {"version": "244abd05ca8a96a813bf46ddb76c46675427dd3a13434d06d55e477021a876ef", "signature": false, "impliedFormat": 1}, {"version": "5298f6656d93b1e49cf9c7828306b8aefc0aa39ac56c0a1226f1d4fba50a2019", "signature": false, "impliedFormat": 1}, {"version": "93268ed85b0177943983c9e62986795dcb4db5226732883e43c6008a24078d7f", "signature": false, "impliedFormat": 1}, {"version": "843fa59ad0b6b285865b336b2cbc71cdc471e0076a43d773d580cb8ba2d7030d", "signature": false, "impliedFormat": 1}, {"version": "aa2d452401748a5b296bf6c362b9788418b0ab09ee35f87a89ba6b3daa929872", "signature": false, "impliedFormat": 1}, {"version": "a4ef3c3f6f0aadacac6b21320d0d5d77236360e755183802e307afd38f1cbcc9", "signature": false, "impliedFormat": 1}, {"version": "853b1daed2861381ddda861a0450ce031c280d04caec035cc7433872643871c6", "signature": false, "impliedFormat": 1}, {"version": "1058ed9becf0c63ba0a5f56caaafbfd0bf79edf2159c2f2f2fe39a423ae548ae", "signature": false, "impliedFormat": 1}, {"version": "8b6eab9a4a523909ee1c698a10d332c544aa1fb363f482fe60f79c4d59ca2662", "signature": false, "impliedFormat": 1}, {"version": "f2b2c244b10a8e87192b8730ed5b413623bf9ea59f2bf7322545da5ae6eae54b", "signature": false, "impliedFormat": 1}, {"version": "92bbeada67d476b858679032b2c7b260b65dbccc42a27d0084953767d1a8cf46", "signature": false, "impliedFormat": 1}, {"version": "545afad55926e207ac8bdd9b44bb68f0bbffc5314e1f3889d4a9ad020ea10445", "signature": false, "impliedFormat": 1}, {"version": "4c8ef63125ed4d1eef8154ec9da0b6b7ca9effdf4fa5a53ab74a9d73c9754ff5", "signature": false, "impliedFormat": 1}, {"version": "e76a7e0b4f2f08e2bef00eacc036515b176020ab6b0313380dd7a5bd557a17f0", "signature": false, "impliedFormat": 1}, {"version": "fabd983e4148e2dce2a817c8c5cdbbc9cf7540445c2126a88f4bf9c3e29562b2", "signature": false, "impliedFormat": 1}, {"version": "a80c5c5bab0eb6cc1b3276ac276e5b618ead5de62ec8b0e419ea5259af0a9355", "signature": false, "impliedFormat": 1}, {"version": "d8cf5ded7dd2d5ce6c4e77f4e72e3e1d74bb953940a93d3291fb79158e1afc6e", "signature": false, "impliedFormat": 1}, {"version": "bdb10c13a7ababaae91932d0957ef01cd8a789979cd0b606a2106d198848b16c", "signature": false, "impliedFormat": 1}, {"version": "0fd3f9fed4dd35b1b07c18b4c3f612b7542c91835ad8a26e0e83d905709543dc", "signature": false, "impliedFormat": 1}, {"version": "441b5f5ac4619df9dbf436ecdb9f0bbaacf8696e6fdb2f81c6f5b1db76f5a1c0", "signature": false, "impliedFormat": 1}, {"version": "5d2284728400ee7b4fd1acd69e48d649d4056916cc70950a0000e5d70a32a750", "signature": false, "impliedFormat": 1}, {"version": "27ef186120f9e7ee90686aa7ad5163eb5c7f4cdeb19bb87850c4a5fe4b8e05e8", "signature": false, "impliedFormat": 1}, {"version": "4f1f9e056e0c9d23031367b4c7e7eedffb3e1ed58e64befc90749ca4dd9363ee", "signature": false, "impliedFormat": 1}, {"version": "2b0ccf76bcf10f61612135f951a74327ea0a2d5a80f397b767e0e0b08cdac265", "signature": false, "impliedFormat": 1}, {"version": "4e42e643f05a7fa69581a1a697a1cf967d9b2657dd9dd66e59d90500ec053ba0", "signature": false, "impliedFormat": 1}, {"version": "0ea8485dc0bb7d2a258a93b16305e17fb5be9f877a9df88de7023a9821c537ab", "signature": false, "impliedFormat": 1}, {"version": "5c221ba5333b775cef94d4a30076cc30730cceba649e9d30c5a7224a698c8825", "signature": false, "impliedFormat": 1}, {"version": "cb61ba4d5b5e39ecafe74ad7d88dc8e67defcffe15fb9216addee0fa06d5df38", "signature": false, "impliedFormat": 1}, {"version": "d83e8f0c10477fb4a7729a51aaad853cee81e0e332581dd2244da09e5526b5ff", "signature": false, "impliedFormat": 1}, {"version": "c8933a5b693306696e78315dca1fa57f6f5493fed44cd90aa2d4a4d354dd6516", "signature": false, "impliedFormat": 1}, {"version": "af8e2bf3df20cd2e6b8d744dd83499e174609d0c88864af3f30cd43671e719f5", "signature": false, "impliedFormat": 1}, {"version": "4186fd8b51535399c7ad1edc08f9c4ebb2a9e8e327b131cc1f950c5dfbb0c358", "signature": false, "impliedFormat": 1}, {"version": "b92965f503f55830702062f9e0832fabfbded49ff28728686a6fd84aa32f454d", "signature": false, "impliedFormat": 1}, {"version": "172dbc7933ff46ba3b2efe8b5c7828fd4f0d45c08755df8200213b6055d57f2e", "signature": false, "impliedFormat": 1}, {"version": "89e2ec7ed42725f89fa537c38f20144782bec6c5710e467a46a647647c8255cf", "signature": false, "impliedFormat": 1}, {"version": "5165882999957fa041e423a4fb64627dcb310bf50183af70a6ee8e10a584b0c3", "signature": false, "impliedFormat": 1}, {"version": "390997d64e1e5721fa807aa9e05c97086f58627170d9a7ed84b127126a3e5202", "signature": false, "impliedFormat": 1}, {"version": "00cf8ed9b47860a5f8cc0a65d7a41f85a7026f68162057728abc9249943a8629", "signature": false, "impliedFormat": 1}, {"version": "fc8b086c99f6d721eae8125a96833e0ba1762d00b80aad1d55c7a8b59d007466", "signature": false, "impliedFormat": 1}, {"version": "ff72c74ccdc5570c4a75a93e605a5586596444d96048d52c72f322da183c556d", "signature": false, "impliedFormat": 1}, {"version": "b8755448066177191edcd0b7e19e7fe44d69ed6dc97b16a420b7aa9070e2b850", "signature": false, "impliedFormat": 1}, {"version": "822a0c843f492ad2dc815080f24d4ddac4817a9df0de8cd35830e88fbbafbbe4", "signature": false, "impliedFormat": 1}, {"version": "467865324b9f66a1b8f68d9350c5aa0e749eec499e4863fe017b16ea8bcaccdf", "signature": false, "impliedFormat": 1}, {"version": "863bd77d5546877e19594759a901cc7b75da8d27336d4351e54413ec12032d09", "signature": false, "impliedFormat": 1}, {"version": "a17a62c94da321c0bf2315c35033e313daf1298a75aa43a01a4daf6937980c01", "signature": false, "impliedFormat": 1}, {"version": "851271a09d3c2db3eab80d64beb468d775a9818df06a826ba58925c900231ccb", "signature": false, "impliedFormat": 1}, {"version": "da2c95cd1f0f9cc19f3dd599b4c8fb0930eccb78a5c73f683e7ea98262d2f55e", "signature": false, "impliedFormat": 1}, {"version": "e40d3ca85fb1362763067506784635aa28863640cf7cf9be9e8c1c521c0fbbd5", "signature": false, "impliedFormat": 1}, {"version": "77a2f84e19aca9d03efdf0c484aba8daad3fd23c70b72e63aca78fadf71b448d", "signature": false, "impliedFormat": 1}, {"version": "00c5b6248c69e66729e5c4acb239db849b1497d7eb111fed3eba979432461ebf", "signature": false, "impliedFormat": 1}, {"version": "8e13abf75e9394f3a4b1d0b3f99468e15f4c7e2115153d2a1ca3c0de035bad1c", "signature": false, "impliedFormat": 1}, {"version": "07097dab1c068118806fecb8544aba3cca30965d0864b1998af1bee326a9990c", "signature": false, "impliedFormat": 1}, {"version": "c490ca6eb9149c28e4f2def6acb1bc058d160edb40fd249cf2a70c206a8cfecc", "signature": false, "impliedFormat": 1}, {"version": "7c9aab9a76abba65aa6389e41707d57ea0288dac9a8b6359465dcb462d2cfaa1", "signature": false, "impliedFormat": 1}, {"version": "97fbe30fd1b61b26f807ae1c78b681b0999af71cd9604c08a1d45e44690ca0c2", "signature": false, "impliedFormat": 1}, {"version": "ef91bf45a3d149db0b9e4e612ed1400c35f6a3d2a09669d1441add612d5f16b8", "signature": false, "impliedFormat": 1}, {"version": "dacebdc0353168f259724bccfd273b892e883baf36cf3dee21cf4178f3ef9ea0", "signature": false, "impliedFormat": 1}, {"version": "5416fb031a72377c3c17faa2041428a5f19f9d46a70b645dda6e3293fd0ca8ce", "signature": false, "impliedFormat": 1}, {"version": "95611472fd03e0992070caa3a5387133e76a079719994d237947f6bcf67f9bca", "signature": false, "impliedFormat": 1}, {"version": "6141d19bfa7698f362e84460856ace80a1eac3eab1956b188427988f4cd8e750", "signature": false, "impliedFormat": 1}, {"version": "1acded787e1fc09fd56c004d3ba5b719916c06b61976338a92a2f04ec05cba5c", "signature": false, "impliedFormat": 1}, {"version": "8fb0d41cd90f47b9148e4a474fb03484d9af1735871321a2f57f456e40a7e319", "signature": false, "impliedFormat": 1}, {"version": "a25cd4cf54bcdd109dd46274e2369fc1cad6d74350b5642441d2b9eef515c3bf", "signature": false, "impliedFormat": 1}, {"version": "af4b9f16e50a0ae803745150e4c091e86ab95f3dac649286af28505258f7a189", "signature": false, "impliedFormat": 1}, {"version": "3d209a6c3c53366b3bcb72dcf04a7ceda57362cae6ac47dbb783321934a0c5ad", "signature": false, "impliedFormat": 1}, {"version": "4766770027d93a5ad1d4cc880cce405b4c6f67c64303ab34b347d6428eb783f2", "signature": false, "impliedFormat": 1}, {"version": "43d2bec085f0fab54d7b9dfa3f5c5ce65e30da6a19d82ed37d1d41867682f86e", "signature": false, "impliedFormat": 1}, {"version": "e5efb9781a0ef18d60cbb8afa261489efd260d87642c095cacba0b09b2684fcf", "signature": false, "impliedFormat": 1}, {"version": "775ca7538a2f9bc674ebe5f3cb8aa8fa346ef4c1faec4c5b13b4784a744854dc", "signature": false, "impliedFormat": 1}, {"version": "c0037c7c6fb8031f7047a1ccdb381762862b48429e9ab07bac8fc35fc5b5dd14", "signature": false, "impliedFormat": 1}, {"version": "af4db63c6e4d55df1ad7f3dabdde31bc30555debf1cd6b79ea65a36c52bf199c", "signature": false, "impliedFormat": 1}, {"version": "d291ffc234a58061b8192f74422f2e51fb87f6d10e82c30a555bccf9641b3e38", "signature": false, "impliedFormat": 1}, {"version": "6d683695e9765b29165bb0823f88755211d48949f0b95a9a4236802afddf41e1", "signature": false, "impliedFormat": 1}, {"version": "8fcd568ba937d867544cd8e726f35a515690ad041387fdebc93d820c8720e08c", "signature": false, "impliedFormat": 1}, {"version": "81a0ff507ece65e130c1dd870ba79b8337c1fd345db7b154a2749282c994d2d5", "signature": false, "impliedFormat": 1}, {"version": "64e2ffc72047548fa3c04095abb9dab48e2eaac169161fd2ed3564dea0c67e57", "signature": false, "impliedFormat": 1}, {"version": "b525d2fc6b694512a877219ebba25d5fa244f99253a5bbe6c6421f8d71b1c806", "signature": false, "impliedFormat": 1}, {"version": "d695f0d65f5fba0e275cf7801399575c272b86e7bf8e70133f8fc03517305b1d", "signature": false, "impliedFormat": 1}, {"version": "0836f15e5e7dcad64fd50d49a39267da34371d1c2b803b38dffcfabcd2ff604e", "signature": false, "impliedFormat": 1}, {"version": "56eff313f885482d44e4aa7cefdd55f7d0d92a91c1ddf9cd73c533abc36f4dff", "signature": false, "impliedFormat": 1}, {"version": "022ff6b725f6ab95b1c4d229893b3047002a9c1fab6798c8fe63797ec1e63dc5", "signature": false, "impliedFormat": 1}, {"version": "5e64d04301aa6ae6bf0f3435d07804889342873ab2875a16c827db9e6543002d", "signature": false, "impliedFormat": 1}, {"version": "0b8c3effe0c65129d493be140da1a83eb61a1e83481d441dd2bc359a926b453e", "signature": false, "impliedFormat": 1}, {"version": "0816c977ef73d99cbb134427a83f91ca6f7fe00eb7544118320d613a85da6879", "signature": false, "impliedFormat": 1}, {"version": "068db2994f5926e888462b0852ada2c24f2cb50028f034f475407957ca51c6cd", "signature": false, "impliedFormat": 1}, {"version": "59106b469557319ad26f40f054861be3fd2cf09911c3b66df280b9340a1d9caf", "signature": false, "impliedFormat": 1}, {"version": "69e8e2dc21b0636f671485867555439facd68ee9e234fc9190c3b42e7f1a74e9", "signature": false, "impliedFormat": 1}, {"version": "5fb0c0cae187f6554769cd4ff36575ddbc43078a4fdf9b17a5c0c25dfa9a9f2b", "signature": false, "impliedFormat": 1}, {"version": "918d99a7aa4b7f5edf2cdcb33c163837a892f43b9e22c10634d61d0a28fc09a2", "signature": false, "impliedFormat": 1}, {"version": "097b0d1e237bfcc97411fcae19a484a717fd4055a48e98ade5cc28b26afd21f6", "signature": false, "impliedFormat": 1}, {"version": "5fb0eef64cb75951f7ae2dc6a704aa0567a25a39a616a5dd10ba7cfbfcf73b78", "signature": false, "impliedFormat": 1}, {"version": "0a649cbc59a47f224d0494a6d5167a803ed049f995ade8423c7cb62bb6a38b64", "signature": false, "impliedFormat": 1}, {"version": "68e25d1a79523b18fae630ca57100ce2dff6c5023376a2f57e9d0d07e1b9b8ef", "signature": false, "impliedFormat": 1}, {"version": "1a505f408bc7d484553b7701f712dc52e1174648baff7d6c9c1f38b5cb83b772", "signature": false, "impliedFormat": 1}, {"version": "b19badf31df455f10cf44fda9f6a0e0b42d6e970ac122b66c5da5d683fa270d4", "signature": false, "impliedFormat": 1}, {"version": "71b6fe5c85eb877c3e3ed2f142b95a69f97905c34f11fc6d9052a4317e7f6bae", "signature": false, "impliedFormat": 1}, {"version": "bd55536c0f989f59af6ca66cbc8121485f978f4e07c3df1688623c5f898058c6", "signature": false, "impliedFormat": 1}, {"version": "dcb868c613ccd06b1a3ff56ee235e5987820c0c8bbd77fedc9af4dcfdd4c54bf", "signature": false, "impliedFormat": 1}, {"version": "f3d1b3cd130e3cd67fe8e06256deb5d678243c6976ea498c81a48e542efb7529", "signature": false, "impliedFormat": 1}, {"version": "772b881836efbdceb7ae8d3ae038f14ec83444397d8429b866312dcd78714dde", "signature": false, "impliedFormat": 1}, {"version": "314d516eb3bf1eda07e898935edcbd1e74739493c8ad444e82181f8a020eef2c", "signature": false, "impliedFormat": 1}, {"version": "8cfced8e57c64563f91e90a76a6df2d8f934c90a425327a9ed5393bc88c27d97", "signature": false, "impliedFormat": 1}, {"version": "67bd754a8775c81794c9fc84b1a1e9fca44a402fa7d93fcdad4ba2d37737d929", "signature": false, "impliedFormat": 1}, {"version": "5128e32c57068eb09d5189eb68681ca7d0e5e4b0cdedecbef9c67689f0970876", "signature": false, "impliedFormat": 1}, {"version": "7fcdedd29146e5a2a6c86eda652f8485a1eeda1b8646825bbf729023f6ea6013", "signature": false, "impliedFormat": 1}, {"version": "86b9b361ce8ea1d9f04e15bbe49e5ac72e5f97d8cfa8592930d32f267729a201", "signature": false, "impliedFormat": 1}, {"version": "671f5e3a931c2737f8dfa43b34c4a320eca27fc6584ecef890ddd7374cee5cb7", "signature": false, "impliedFormat": 1}, {"version": "ff213315eebd3ff05e01b383f704d79d8139aad5cb0d6a13c082f2e29625adbc", "signature": false, "impliedFormat": 1}, {"version": "83ed351a10ef17b7811d3c06fc2775e36b6911278326d55da8d1eef8ff2f29df", "signature": false, "impliedFormat": 1}, {"version": "2f5f146f1d6c04cf89ae0e9b4cf2b064b2ce4319ba6a5bf18ab8fb29db1cfd1a", "signature": false, "impliedFormat": 1}, {"version": "7fc2b96a8465725bf774bd490c383edd5ee3dfe0d38c13551d082cae2de4041e", "signature": false, "impliedFormat": 1}, {"version": "9eaeb6696e4218cb5bded9ee27c3e95589ad4af1fd4b97ccdca43eadd62c94d5", "signature": false, "impliedFormat": 1}, {"version": "fd580a99cb9bb84288da00eea67dce300bdef06d4da2a727c0fc466d2922dca2", "signature": false, "impliedFormat": 1}, {"version": "b82809d4468b6ba4d72437adaab7ca273547c59974e954c48f655a4b1bdca429", "signature": false, "impliedFormat": 1}, {"version": "c6455d4ed4f7337bcb885c61372c7d9b03991995ed73e29023bad502d1336f0a", "signature": false, "impliedFormat": 1}, {"version": "b5e6f0491b5a2002eb9b1146165cf915ee58e0fddf7f2adb5f2aa4bc44b4fb83", "signature": false, "impliedFormat": 1}, {"version": "f534aef095a62fb82f57768fc52995d3e58d95e0a1671b0256a4704802aee818", "signature": false, "impliedFormat": 1}, {"version": "cdc6f1d471882782cdac7442dbdad65aede5f749c09799a84918bd916eb6d6db", "signature": false, "impliedFormat": 1}, {"version": "2475197472c609662f09660e3964a86aa355cea0e671653656800690bb508b7c", "signature": false, "impliedFormat": 1}, {"version": "b4067760d0447747d82b6848b640168d656d0b916c3add2ec94c3c4dea92fc9f", "signature": false, "impliedFormat": 1}, {"version": "c6c591a17f9c0c2821baf15f775f5c7d6dd4a0786365ee9c182d7a97e38ad96a", "signature": false, "impliedFormat": 1}, {"version": "ede44ddf9d274a859e9f1f34333d5f0e8cf2167c3265f81d5280d37b872b4552", "signature": false, "impliedFormat": 1}, {"version": "6317aba53c9152998bb1f8bd593f55730084d05c00c774ff72a3aa4d687a6dbb", "signature": false, "impliedFormat": 1}, {"version": "26f1bd15980b19d925be98afde3918a6a181435b87e9b7c70d15726ecbfff0e5", "signature": false, "impliedFormat": 1}, {"version": "57af4faf6847adff5048f82929b9a7d44619d482f571534539ae96a59bb29d3a", "signature": false, "impliedFormat": 1}, {"version": "874770f851ac64a93aaddfb86a2f901f158711911fee14a98a67fe32533ee48b", "signature": false, "impliedFormat": 1}, {"version": "3d933e519ad9cc8cf811124f50d0bc14223cdea9f17adf155f11d190ceb2a6c8", "signature": false, "impliedFormat": 1}, {"version": "d5dfce61a7bf994d2cb711af824efa4de9afa5854d34e6725b9c69d925b6b2dc", "signature": false, "impliedFormat": 1}, {"version": "f77d1e10417bf43f8fa5d18916935f342d4d443e371206ede7239faaf9abbbb8", "signature": false, "impliedFormat": 1}, {"version": "c94e0b8815b72ba924c6b8aa666b25903d949a7ab0d38ed84e4bf65da3d06a3b", "signature": false, "impliedFormat": 1}, {"version": "15db84e660fdcd8468f23973ab83c31d7fd28bdddb30b0aed16cfa051aafe900", "signature": false, "impliedFormat": 1}, {"version": "7c01cbfe181c0e10044831b899de6c2eec4fba32de1f1cca12742d2333c1345b", "signature": false, "impliedFormat": 1}, {"version": "62cb1636513ef26d3ea83fb5d2369cf8569d04aa30d8fd7f5327d0e10841635d", "signature": false, "impliedFormat": 1}, {"version": "8282a076b07dc3dc6b2265377627ab3860cb6a1bcbae85a5a4006dec4c9f0066", "signature": false, "impliedFormat": 1}, {"version": "b273c241dd08c6276fd35be413c64508ae50f847fa052bf7781799b51da8e9e9", "signature": false, "impliedFormat": 1}, {"version": "3bc0bbef6d7fb63002fe80167db350b9677cfce5872c0cc7ecec42ba8248ded6", "signature": false, "impliedFormat": 1}, {"version": "4880c6a85442934b81f3b1a92cb6b43df36f8c1b56b6822eb8cbc8c10c438462", "signature": false, "impliedFormat": 1}, {"version": "1bfdd8c1710a3d1654746ca17f512f4a162968a28e1be1a3a1fdd2a8e5bf385f", "signature": false, "impliedFormat": 1}, {"version": "5405aedafdf272dde53b89036199aaed20d81ddc5ec4bea0cb1ab40232fff3fe", "signature": false, "impliedFormat": 1}, {"version": "db2ee45168db78cc83a4368546e0959318374d7256cbd5fa5692a430d5830a59", "signature": false, "impliedFormat": 1}, {"version": "49993b0eaa14d6db6c334ef0e8b1440c06fee2a21ffd4dea64178880bd3d45a2", "signature": false, "impliedFormat": 1}, {"version": "fb9d9dc0a51cb4014d0e5d5f230ec06ffc4eb6caae6eecfe82ea672b7f3c6967", "signature": false, "impliedFormat": 1}, {"version": "84f44079a0793547d3a629feb8f37d8ef6d07cb5bb5fdeefd887f89e9be871f6", "signature": false, "impliedFormat": 1}, {"version": "295c5ec088a1bfc286e8dbdc9807958588979988cd7a74ad32be774a6f6ea512", "signature": false, "impliedFormat": 1}, {"version": "f15129c62ed04410ac0a3326ae6fa5ef7229bbb1b0cbfa252b5c558505a38253", "signature": false, "impliedFormat": 1}, {"version": "4bf500d9a554d43cb9133d60f1b3f58ca98b0f794486d1377f3effc551b40faf", "signature": false, "impliedFormat": 1}, {"version": "536f6a9208c89eb8f0a5eeda629175b0fa62ccd22e387af7f35297fa2af6897d", "signature": false, "impliedFormat": 1}, {"version": "8c95fe5a655ea1c78f0335f8da58e70d98e72fe915987c3b61c6df49d6e276d1", "signature": false, "impliedFormat": 1}, {"version": "4bd434d3055d1b4588f9d7522d44c43611341de7227db9718a700703c608e822", "signature": false, "impliedFormat": 1}, {"version": "935507b695f420fddff2d41ddc12ff3935931a3f26d6aa65afbb276bfdf51cb4", "signature": false, "impliedFormat": 1}, {"version": "e851c14c9dbe365592f5084c76d4b801e2f80302f82cebbe7c2b86095b3ae08a", "signature": false, "impliedFormat": 1}, {"version": "b5c90d931d285d9d1c4cb92d71f2719e28caaa9ca062072d0bb3b69300b436c2", "signature": false, "impliedFormat": 1}, {"version": "40b3e953e9ea51a86a1e5b60a2355eeb780f2f8ce895ece252910d3e0a033a16", "signature": false, "impliedFormat": 1}, {"version": "0264b432aace8398f174e819a0fc4dc196d5aed49ae65aae071fc2ec8e6dc029", "signature": false, "impliedFormat": 1}, {"version": "3b29bb23855a1924264c3a30b5c73b00c52a57c2ffb5f91c48c9572e71048f19", "signature": false, "impliedFormat": 1}, {"version": "8b9b2e76db07d8926bcc432c9bdfb38af390568951b39fe122d8251b954f9ed2", "signature": false, "impliedFormat": 1}, {"version": "96e85c6fa102741a25418ab2c8f740c994e27ea86fd6518a17ec01a84b64dd5c", "signature": false, "impliedFormat": 1}, {"version": "9525b28a4fa959c8d8c7d6815f842f78c67b40def9160afdced5c9daf14cd4a8", "signature": false, "impliedFormat": 1}, {"version": "0e59a6944a52f52138315b6658fb1d217fa017b7abec12006c491d51e07fb56d", "signature": false, "impliedFormat": 1}, {"version": "cfa8acfeb9d68702aa6249b7295ca73ea598e441f014cd4184b6e2a3ea9a275c", "signature": false, "impliedFormat": 1}, {"version": "21b0c616f61cd6699135a34a500f7df30022abf9358ba612f10668ea3c988e00", "signature": false, "impliedFormat": 1}, {"version": "9ad1d0b171f7bb9f484ad156e97f0d8e760a5fee13e342831669c7b2d1137a30", "signature": false, "impliedFormat": 1}, {"version": "7ccadd4ba126bb2c0564bfb85ddd7d084aa5f2880cc2d0149fbe183fd5ceb6d1", "signature": false, "impliedFormat": 1}, {"version": "ebbde5a8a356a1547ac6ecdfba7547036a5ada116011cb96634c32df1cf69084", "signature": false, "impliedFormat": 1}, {"version": "e703eded767e3a944ac1f7c58c201a0821da1d68c88d6ba94bb985a347c53e42", "signature": false, "impliedFormat": 1}, {"version": "99953f3f1f9deae755b97ed3f43ce2bee2ae1324c21c1e5fa9285c0fe7b5077f", "signature": false, "impliedFormat": 1}, {"version": "2afd452bfa6ebaacbead1ca5d8ab6eda3064d1ea7df60f2f8a2e8e69b40259e9", "signature": false, "impliedFormat": 1}, {"version": "dae0f3382477d65621b86a085bdb0caabf49e6980e9f50ee1506b7466c4d678d", "signature": false, "impliedFormat": 1}, {"version": "e5793b3f4cbd73c841790264db591d3abe9bd09128302a2901fedd2353ab24d5", "signature": false, "impliedFormat": 1}, {"version": "41ed74193a13f64a53705a83e243235920fd58d4b115b4a9f5d122362cda7662", "signature": false, "impliedFormat": 1}, {"version": "478e31b207faa7110b04f6a406240f26b06243eb2d2cff3234c3fc8dd075bf6c", "signature": false, "impliedFormat": 1}, {"version": "ea917cdbfb87d11cd2f8b03e357b22b1114d0ba39ce4ce52b1a4f0dc10c6c20a", "signature": false, "impliedFormat": 1}, {"version": "3ef0c5634d9aabee346f9ba056c1c5d977f2e811f6d13c082614c9062cd4b624", "signature": false, "impliedFormat": 1}, {"version": "1ddb49c7f8fc4b9e4da2d5ddca91b4e2763fe7d17aa79940bd60406f3e2739bd", "signature": false, "impliedFormat": 1}, {"version": "d5b01eab562dc40986a5ceb908519dc7f02a7ded2bcb74318317a75714dbc54c", "signature": false, "impliedFormat": 1}, {"version": "b19ef44e991aa150a19a9f84be1fd1c4d86496241300fd904216762246700623", "signature": false, "impliedFormat": 1}, {"version": "87df6cf2565a88dae3ec50e403e9ef6b434ad3e34d922fe11924299018b38e58", "signature": false, "impliedFormat": 1}, {"version": "9d999d30b52fb0b916f7a64c468f6d5c7a994e0c1ef74d363562e9bda3cb8b99", "signature": false, "impliedFormat": 1}, {"version": "9b1b05f88ded21046391276ff60d2d987bf160d77b40399e07b7bdbfe2e38b31", "signature": false, "impliedFormat": 1}, {"version": "628177f7eb0314f0189e4d90f663233606b3936ce391c7f98da46105ae402c65", "signature": false, "impliedFormat": 1}, {"version": "3c80bf6873eb3b95cd590aab8eb1612f0f7cef6a30b3f49535844f7cecd99351", "signature": false, "impliedFormat": 1}, {"version": "da367ede4ebd5ff4cb1cf9e6bc8eb35848b23c57c22c53360e53dc772c7be8f9", "signature": false, "impliedFormat": 1}, {"version": "4337acbd8896efb7e7d8d6e0eca78607fc7c1a9ad2bb228240f13f97b3492f1f", "signature": false, "impliedFormat": 1}, {"version": "505c7800f8195961302dee715870b7212bdfb667e5e47de76447151dd35a40f1", "signature": false, "impliedFormat": 1}, {"version": "cf5a3eed6cd493d198b0c1eacf70486d8bd527fc411d57660caf2c93b5ea0fb6", "signature": false, "impliedFormat": 1}, {"version": "900e344adae3c65076c9ba4ee1a77c6db19fb0c7e54d7ce23c28ff8d272cba26", "signature": false, "impliedFormat": 1}, {"version": "bcc5186a38d1eecf60b2c4d1e3eb9abd8ab91cb492f384a9d2ed7bcda2abd0d5", "signature": false, "impliedFormat": 1}, {"version": "0ec1b41954fea9def7d9d87e0f3beea2ba3ec5b7beb769f308cfe32ad2968669", "signature": false, "impliedFormat": 1}, {"version": "51189c085256f11da13b22792f1d7c928f8a8e9d9b6c7b38e956e72a51ef8219", "signature": false, "impliedFormat": 1}, {"version": "504f509e23f2ab3a8018533925c034a340fbce4af9e77a1f71a8ddffbe0c19fa", "signature": false, "impliedFormat": 1}, {"version": "635c049483e13e1dc8bee72dde300c40d350046cff59b202d41a12ec8c733d27", "signature": false, "impliedFormat": 1}, {"version": "7fd8d5f70ea745e1a0338de7aaacd9bd6ff086ce6de75dcf91749c77d1e23831", "signature": false, "impliedFormat": 1}, {"version": "78d2a7795bfd2be490937e8b01968a0acca8a6bdf5933570bc013806049d4175", "signature": false, "impliedFormat": 1}, {"version": "db49833b6e9aa54b535076f40615349a7465005367a787b50ba7b92421e26442", "signature": false, "impliedFormat": 1}, {"version": "6a936fc917de40c44ca81331ee7d7a71dc30ae1895871e7be7b6ed85d96cc41f", "signature": false, "impliedFormat": 1}, {"version": "bdd2a764cf87c4ab1efd7084597d1ca4ba17f6b6496553095ecca5a14b5d4278", "signature": false, "impliedFormat": 1}, {"version": "ddef8e6676fd572ee3de174ad28df05c7b3803542d7318482b8f98779ff25612", "signature": false, "impliedFormat": 1}, {"version": "34eae3bc7f5bfb515d2ec163ccd4b63fdb73ad7f66564707686d84f42a8b7c35", "signature": false, "impliedFormat": 1}, {"version": "d240d106cf9bc3c0efdb323d807b944ce16ac5d837ecef5b75f1e66d606b2a72", "signature": false, "impliedFormat": 1}, {"version": "639d5a26be297431e0bcc9f71f969fd7d84319fc03b5e1c672ea10fb0094c616", "signature": false, "impliedFormat": 1}, {"version": "770c3e6367c2802c027c0b1f86928f288e11ad77ac2f454d7f682460eab30a0c", "signature": false, "impliedFormat": 1}, {"version": "c9dd2760e0419a059cf733c38ef5d44eeca3fc647f9c201d88656e5040f5a3a7", "signature": false, "impliedFormat": 1}, {"version": "16766b8f3d1bba66ac8167e6407be6c490d4462e802f67c140b1174869db5b67", "signature": false, "impliedFormat": 1}, {"version": "f9267391788ac81ca54dfae32c5d86e99a19abaee9b172b2f8d98a3c2b578a2f", "signature": false, "impliedFormat": 1}, {"version": "92441638c0fa88072ef9f7b296a30e806bac70219ce2736ef33c8941259d9b70", "signature": false, "impliedFormat": 1}, {"version": "8774efbaf39f9ea3a0ff5b1c662c224babee5abb3d754796278e30eb2e51ae3c", "signature": false, "impliedFormat": 1}, {"version": "e634b47a7d3f9468572a7c9af1fe2f52687ee1afb23ba5568205a7a4c55662ef", "signature": false, "impliedFormat": 1}, {"version": "1cbef47ee169c717a1ef7ea91b15582c61ac721fd5f5671de95c3df9f026db9a", "signature": false, "impliedFormat": 1}, {"version": "0db0ee49f803c9b901dfe06be9c8fb6a1c05f98664ca34c68e0da575eae76f2b", "signature": false, "impliedFormat": 1}, {"version": "4b745fcadf040899979b6b26e24aca6d2fa2bbe52a919d67f717bfe0339354a3", "signature": false, "impliedFormat": 1}, {"version": "bc57f3550b3fd3b7d31b9a278d0b491dd45d170e37c4046a3105fdea9ebe5f89", "signature": false, "impliedFormat": 1}, {"version": "b5f7093d62a228669dd56edd0bcb86a0cf0b46db4816a3967b4632503c21b93c", "signature": false, "impliedFormat": 1}, {"version": "4d70bbb1f35f345b2c2e1b5c9b8174d5397bba76ffef12656bca16ce9a1830d3", "signature": false, "impliedFormat": 1}, {"version": "a004fc80aa8f78dfb1d47b0e098fe646e759311c276b6b27404f5e356528f22d", "signature": false, "impliedFormat": 1}, {"version": "c8933d9afe6c5ee7ecbeec5aa01f6b37d3c2be2f7dd203ee75ee4850164007cb", "signature": false, "impliedFormat": 1}, {"version": "b1129b38f1eea70951ece3ccd1cc3e1d094379b64d3958ba8ce55b0ec0083434", "signature": false, "impliedFormat": 1}, {"version": "b2bb10f992cfd1cf831eb005311a80f7f28bc14cfac5883f17e75f758d1354e1", "signature": false, "impliedFormat": 1}, {"version": "58b621b924324874a67e92d7626809fd4b72fc079ce909f6da7097654026af00", "signature": false, "impliedFormat": 1}, {"version": "149288ae23bb3b31ffe5cfb7eea669fc6872e41901d60be932af2581601fc70f", "signature": false, "impliedFormat": 1}, {"version": "01a0fd262c8fdc6c91078255c4fe2f8602fd4fe4c753b2eae88537585b21dddf", "signature": false, "impliedFormat": 1}, {"version": "deb69e6754a61784daadc35b318544b0aa69048ebfb142073c62b7f46bb1d5d0", "signature": false, "impliedFormat": 1}, {"version": "60eef77c9b5cec20516907628f849845975a8137773ddb0bcb53fc2ea7d28870", "signature": false, "impliedFormat": 1}, {"version": "67bcdcbd8cece34ae28180c636908af1b118fa9603d0d4b7dea877156d4de519", "signature": false, "impliedFormat": 1}, {"version": "5a1c2cee26d1f8d9bb15b334f5b2df7de27a3944bff9ccf71d3b69c588612bda", "signature": false, "impliedFormat": 1}, {"version": "a04d60b205af1f28461f3d2f5a8222ec2d8af54d436bc53a0460756e07e4207d", "signature": false, "impliedFormat": 1}, {"version": "14c85d4debb2e0c8939f81b85cb9ab4543f70c8fe53be5fb5caf1192677c8ca4", "signature": false, "impliedFormat": 1}, {"version": "c507cdc9757c048620ff08a85b9cf6278598eb1738d729fdbfa1e387a35e639a", "signature": false, "impliedFormat": 1}, {"version": "4a4807c3096f49a463476742e3b5d23ccf0e087e43c017891c332ae5b8ad667d", "signature": false, "impliedFormat": 1}, {"version": "c611af558c5d19fa477f1b03ceac7b0ae28fe5ad4f8bc61e8ad64c46f97e86e2", "signature": false, "impliedFormat": 1}, {"version": "0cec41f583efa1f1033a4d546d926ee949756f19040bb65807c5a3ab6f3b8449", "signature": false, "impliedFormat": 1}, {"version": "73b1eda15491d4f3052d6fac202190e76d6453fce832034bd29901cb198448b9", "signature": false, "impliedFormat": 1}, {"version": "08c66989383183f3d7c43346617c8f466bef28f1e3eb4da829316d548cdbdf80", "signature": false, "impliedFormat": 1}, {"version": "1f283476bbeaa589fe644fe6ba9da223baf118ecd4756863deae7362b246aff3", "signature": false, "impliedFormat": 1}, {"version": "0a8f91ace4d1803eb2a50079c9e233fb262b0027d19aa250eb7ecbf6319e52d6", "signature": false, "impliedFormat": 1}, {"version": "65bab52912be03b374ab591d73ee40aff3a465ac20bc0f2024b4c80ac5ce8397", "signature": false, "impliedFormat": 1}, {"version": "6a647bf0620a4a7777527c688c62636a503e8b4d5e680037503066dd2af6d0dd", "signature": false, "impliedFormat": 1}, {"version": "f1466e4d708815280c849956a506e132b7dc243907b9c8e07d52862e32dfcd91", "signature": false, "impliedFormat": 1}, {"version": "cb4b99f8e47f57df841c95fcb1afc28488a2b5442e3524f6261e611b86105331", "signature": false, "impliedFormat": 1}, {"version": "7c5fc61fc40a9f3aa3a09fd867536ff94a93b16f4ae99f1fb748fae6e13ae8bc", "signature": false, "impliedFormat": 1}, {"version": "473d9ca5b242db0471d418336f410922eadd290679914f37ef21ee26dbeee2b4", "signature": false, "impliedFormat": 1}, {"version": "2ffeb6ad0b074d1cfa3dc9671dad062b08129d1e8a8988b727dd2ce9fd4298d8", "signature": false, "impliedFormat": 1}, {"version": "fa1d4332a68d84300895af592811f65f5f1d725ed0664f17d5c215a63408b6b4", "signature": false, "impliedFormat": 1}, {"version": "7a09768c36d8b7d8e44b6085031712559362b28a54f133b803bed19408676cdf", "signature": false, "impliedFormat": 1}, {"version": "f0b807278b2619fbe0acb9833bd285acabbf31da3592da949f4668a2e4bcbcf0", "signature": false, "impliedFormat": 1}, {"version": "bc6419ca69c35169941d9d0f7a15c483a82ac601c3448257f29a1123bc2399e1", "signature": false, "impliedFormat": 1}, {"version": "45f530610645ca6e25621ce8e7b3cf6c28cd5988871bc68b3772488bd8e45c25", "signature": false, "impliedFormat": 1}, {"version": "2d3e715ca71765b491ae8bd76257e8ccfe97201c605dadc4e6532bb62e4f6eee", "signature": false, "impliedFormat": 1}, {"version": "c519419c11e61347181ba3b77e8d560d8cc7614b6231cacefe206b41474792d4", "signature": false, "impliedFormat": 1}, {"version": "24823640771cf82865c3b1cb48a8a88119b69e56aef594171cc0570f35f60b8a", "signature": false, "impliedFormat": 1}, {"version": "30398045bda704d03d23e78a37095aa56e69ab2dd8bb7304b15df9e183b9800a", "signature": false, "impliedFormat": 1}, {"version": "9a816fe54ea736ecf02b6865c10157724cdb5ba3f57ead02d9216b2dd4bd0d5f", "signature": false, "impliedFormat": 1}, {"version": "a67582f2933f5b6faebba3484c99e78b529aa016369b768021726e400c93ddb8", "signature": false, "impliedFormat": 1}, {"version": "96cd7367cc076d36d9f10cbe34b91e94467caf9b64a7a0fe1c4f6c8287e0a1b5", "signature": false, "impliedFormat": 1}, {"version": "17c7be2c601e4b7e6292932997e491ff874418bef9ee6137e69ea6ef497e0e5d", "signature": false, "impliedFormat": 1}, {"version": "eb7ed3b69718cf40c1ab8ce9a0e917819e0ef0b7480ba2890cddbb94a1386b10", "signature": false, "impliedFormat": 1}, {"version": "7a7cec0720ee6d20e08fa9def697b149a94db1763bbec6e1ab5da8d7726ebddc", "signature": false, "impliedFormat": 1}, {"version": "c024677c477a9dd20e7aba894c2f3e6ef81c4076af932a7fc00c210543cd53bc", "signature": false, "impliedFormat": 1}, {"version": "7f31b6e6d0c03a34e462fdaaf2f7ab6daf85bed51fcaa61ee794aaa1c9b890ac", "signature": false, "impliedFormat": 1}, {"version": "c63a0a23b4e1568a8e8e05620741f988361a18b54dbcf85d40824c0fb8b3535a", "signature": false}, {"version": "c9d27775df3282c482e6b6c3c52cf8d124364ae61662b75c169a3078a93dd412", "signature": false}, {"version": "e7f2de2f3070129e05d3ecb2539180fb939ab406ff9a60f5e1041c17714d47f8", "signature": false}, {"version": "c83ea9c7e00367d32c90fa4e0a5372bf683865f1e3dc44c292e4c5e4a585a578", "signature": false}, {"version": "696f5e58a4cd6a9a9219d9e9985e471520bb59099077af0327091935d12e6b86", "signature": false}, {"version": "b0404b9f7c8ece46cb37c9c4ef08345a93320605e34096b5d718f180bef48390", "signature": false}, {"version": "7787cfe45050d8bc55fb0b99b69d8fb4a1c959a1e94b133da64825f8f152af98", "signature": false}, {"version": "1a6c6f8bf7862152c377ff237b81e968bc73e3f0dbcb3334be8d4c05eff054a4", "signature": false}, {"version": "1d58314b306b11abfcee9588ee47284cb75aa300e94213679232600d37bb522a", "signature": false}, {"version": "da2f355d7f3d79152ced3a08be79c2b93b4bdac69d02985ce52e17130a0dc8b3", "signature": false}, {"version": "79e83cad7de1618ee9c9951a284826d1472ea52fffca165ddd38e95a5cb82756", "signature": false}, {"version": "8a4cf39811032a617091084e4ad903b326ba6b86f82ba1f66095af933bf30af9", "signature": false}, {"version": "630818abe7e97017c695392ab273444c6f8c61d0e27d311d8374d05b57f527d6", "signature": false}, {"version": "c74cebb231380bcd9e2ed3a220e2609a55e0d97e70a73d6e0fd448edd3a0e4a3", "signature": false}, {"version": "d514598475f601371cd91e3b14b11e1edda033f40259b801985866aad1eb7458", "signature": false}, {"version": "247e71351675281e335a3aaa8a500a48ff4af2eca9cc4faea4e62b31c8a262cc", "signature": false}, {"version": "3600371cb9a2e4a586eb671652fec5c59567d1e9eef837ff17e0eaafbdc9d5c7", "signature": false}, {"version": "81dabe6cf7d769ab8427eb0fcf2140c96f8b4b8180567a2a31853c940e423c1d", "signature": false}, {"version": "b355bbf30a5a6a80d31dfacd5b868b08e8e274fa7f46488a0cc2dcf0a88bff36", "signature": false}, {"version": "6ac80d6b180d01a23df5bf9505b9f65b545b60ac826b47791e540ba6c0ba02f0", "signature": false}, {"version": "3de93020d72f4310f2aa2730ad3d0969f89e5e86b67936f8c073bfea15f5e010", "signature": false}, {"version": "851f35d929c668e903be72f92b9596bd8da5ce62f27760e6f89aee898e365b57", "signature": false}, {"version": "f73bdc274a07e4b17235301fe8e8d46248bc839f84b2c461acff9c71cf563975", "signature": false}, {"version": "7f1d87093df0860664fd588394987b96839f84014b621c7fcd8ca2f8b91eb686", "signature": false}, {"version": "550c122865bfe822b3f637d395897a0076c9cefd51588f933acd4ab859510a13", "signature": false}, {"version": "da13f0fc32b1d475ed96e23c9b6d0f31fa9c602540dc94fd1123120e0d0fcad1", "signature": false}, {"version": "c6f6b02afebca90f75277a5c345d1e66c13482d6c03db432ba8849352d161747", "signature": false}, {"version": "ba3d38667da86a635409952acc1d14dcfef32b55983c604e9e4687f479478160", "signature": false}, {"version": "12c3ca316841dcffc822b0929315e2ce2afd2b6971305f32c096fb09c044f801", "signature": false}, {"version": "8b9f245db2fb4280ebd2296947648ffaa1df320e66ba65dbff951fa6585563d7", "signature": false}, {"version": "427b51589d90e5449d9bb4870b40d7bffef8321e23ebddc32a9ca9245030a1e4", "signature": false, "impliedFormat": 1}, {"version": "56a7aa3eff0ade6d5b2e50920a8c89c6e0f82a8414c1ad8f3979b50acf64bd61", "signature": false}, {"version": "9702002ab02a1833ce9aee8ac09eb57e2541a9f12f301f26894bb2edcba44706", "signature": false}, {"version": "3c943361a719a654db0dc2bc94782888e809ef41b3691221cf5555a31394e33e", "signature": false}, {"version": "0e4ffff0a04f225e6edc049b2cf861bab284ed15c671c7e40ba1b1af2bea4ad2", "signature": false}, {"version": "1428c42cae763b0805baa231784eae4e1f8e704e9414fa8f0a46d850d156232b", "signature": false}, {"version": "4896588110cb6fd91132e4e9bca7731ec94c015067a9823baa21b4d728ff2283", "signature": false}, {"version": "94daddc51703bb7b67e87992a980734d52603fcbba0d738ac67be3e7fdcfed63", "signature": false}, {"version": "cb972e063c819cb2e661de84a6e90c2584906f2927594420734402d935a03ba5", "signature": false}, {"version": "177888dd274294d8797b20eba053b66a60d549470723a8190371dd7db023044e", "signature": false}, {"version": "6e5177872432c9fc9d17df8d925c16fbe185c0a81296e9ecef8682be962edcbf", "signature": false}, {"version": "464364831849298e0a5574f19aaa8f2ec5ddb9d245cda4c552dbc0f31f4300f2", "signature": false}, {"version": "e33cbaa13e783dbde4b89c83b4635ff53c82c7fdd6b2741ae0ee06177511b2f9", "signature": false}, {"version": "b7d3b373feb8a302e8cc4c541975511039e38c260867a3aaade04ead38f3a526", "signature": false}, {"version": "d0d89ebf7a1754b6d520a76359e3373e2ae14610cab0a9e0824761792e40106a", "signature": false}, {"version": "78cfc53e2e90a5c1676998674674b45d27ab9306f4a94bba03cd40c56b7efef2", "signature": false}, {"version": "703b0560f838b0ad12a5e6f36ea9050a1035c57886f058a235f655ed7bbd48fe", "signature": false}, {"version": "a625428b548d478191ab4aca9111bc7e9abc3eb1c17a8c9cc9050c5c33742938", "signature": false}, {"version": "a98750036f961e713cc15de1a6ded42672dd00e8e8e758063ac7da1a3a0dbb7d", "signature": false}, {"version": "34237bc00ab9f7782745d17e8e0f0462b4b62335d43494d107f47fc705006534", "signature": false}, {"version": "b0b2267f826c3124d76ca5174992cac3d67f45b6f4e9e064493849ce5675ac0e", "signature": false}, {"version": "ae647970c29c0c2d6e644fd9f812718e0cd40cab36ed78a417000b282f452a66", "signature": false}, {"version": "d65e4bd246a0d50336d982fcbe426a8fac08baec5ad89b858d38356de69921f7", "signature": false}, {"version": "00bedb92ef40524a567a7866889e8fae06ea4644a6f09ec6dbb49e08bb6fba4b", "signature": false}, {"version": "a6bfe148a25bcd130784f67bd2b6680570d71b20d9f0d484a3314b9f2bc119d6", "signature": false}, {"version": "53d2626432e63fec72f82cdc15454938c67d9c7c9e59595cd368f39356320bc6", "signature": false}, {"version": "4f4588ff57f848b707de2c92335d7ab27395279f484cedcbe3b6251b15b40a31", "signature": false}, {"version": "aea5188baf956f8cfd03cc5521fff8944cea9d0091da33fcc5d02eae2aabb5ed", "signature": false}, {"version": "f8551d99adb9e927a0cfa570dff1b3230436ebc94d19a77ce8150382fb48dd85", "signature": false}, {"version": "df5e635648d4466c6b55a1e2cec8464d88a4193b7558dfda1b07ad154b514cbc", "signature": false}, {"version": "2772dcdb3600a37d46edb2e8cada6f0353161c0211f449d2a465d664b4e9fefd", "signature": false}, {"version": "b6020abf893d8e9bcab64923c9c8ce8fe9ae80d11594d0ae490cce5b92588bf2", "signature": false}, {"version": "e65216c25e9afd47b27edad97d44ba1b19c8c8f06ca46a6e605b1e5b43db2b77", "signature": false}, {"version": "0fe2ba09e4a7e5eef06ad20d12ffb31fe764eb3f99e3e429fd3e727924b536c5", "signature": false}, {"version": "c511fa74be7ca01988bd1b700b2a2c0e552f1b3726e6f862ad453fb76a5dbcc5", "signature": false}, {"version": "416129514bd878ccd797782e2906b5e2634353304ea924496903aa08c78ce222", "signature": false}, {"version": "f77c8d322dfd625eda9ce0d4cfc94cd20c7b2976f82e08dd81f0359327a4bb04", "signature": false}, {"version": "0324fbf002293ebca97b0eee4081d9cea0f9949d10b166d674d014739eaf138d", "signature": false}, {"version": "4bc877e129f6baa0e463eb77e072513708ff8be35f53f58fe3de24eaa6c48ac3", "signature": false}, {"version": "0e3838fe752a2cda512b4197b8b201eea875200dfdbcbe4acf127e474bd59b24", "signature": false}, {"version": "e24b020b452811aa40cfb5ba279d11857612970a06f84d6447664461a0c90750", "signature": false}, {"version": "0ffff6b1150547a379f5431a856e8dfe5e13b8a63d786167742b66a33dc14c51", "signature": false}, {"version": "bbe7c495e12b6fe4b5ef0da72d82344801e5100c27d1bcc0b1356e9e1395127e", "signature": false}, {"version": "c3edd3396d1294371059d41049112a6128ff81f32b3dccb3547a38efdec1a41e", "signature": false}, {"version": "73fcd170fdf07c1b812c83ac591384409efe0d82e697b5f0575a618e60364c69", "signature": false}, {"version": "cde586a63dc3759cdb62423fc5c20cf9bfd81bf61ccee4375001c7f59752653c", "signature": false}, {"version": "3911de3c66dd5e3730e6181b85b97e7b120818e510378a16f50407a01aa7e768", "signature": false}, {"version": "53425e0564d0ff69561b1a2ff5d6d5ec798356bc2960a58c6f84be0563e850db", "signature": false}, {"version": "a31f38d08a399cc055ce589e851329df0768787edacc551c20bc78588bee868d", "signature": false}, {"version": "6b573e0f71387c820231e809475c45f894632e3fdad7309d7cf1d2d98742cbae", "signature": false}, {"version": "1d55a3aca36ea51d5d723244848d9eb481fa58fe1dfe501e0eb6204f62725ebe", "signature": false}, {"version": "69f5a99e03498a73a26c7624aa80710cfc635a337fd8419138861b9dd8bf5bc3", "signature": false}, {"version": "131fc900fd35225e8ea9311131ea7bda36add78361347f00fa15ffd593d4351e", "signature": false}, {"version": "7e3d79f5d2e5422a430e2928b7bee5062b1062523b91e65783fb4fe76cee4540", "signature": false}, {"version": "d320bf32df07bccaada5364b3a63c52504bee612c5773f66ec2d36c9bada41e2", "signature": false}, {"version": "424a52fb4619f63af3a88d57e18f30db54ffd43cc19f3e81a50172b94bedaef4", "signature": false}, {"version": "b7b8386e4477ae9db3bb97688223112c6c58efcbbb320dd1e0945c4df23a3089", "signature": false}, {"version": "c9e92f2b808a5c53383e8afce2f8c7af69763978f4ef87d7572712b95817a01e", "signature": false}, {"version": "a1e36576a129e3a227d28566cd5447500de714b7e6e490e1b1bfe632b6b114d4", "signature": false}, {"version": "185350a358c878bf57b11c5f7a595d0e2c32e4b7cd22da3cfb35ce3c68506d00", "signature": false}, {"version": "06a6bbb16b75aab37762cff2d21ac22939e89e248475afd40bafd47f3bf58056", "signature": false}, {"version": "f66e2c6f787e827826668c17d0fe64f14d7534f7706268346db34ccca5e212c0", "signature": false}, {"version": "83ed0b04bca8cc787d388728255bd44fe55243ff7849d7be51bca99759d77ec0", "signature": false}, {"version": "688cfb712013e387143fa701c203d2dca93e0bd878502f8b7b144cd390a21bc4", "signature": false}, {"version": "aabd396a87c92096ab214622643a59ac44c57e65ef759a4a4e15dc39fd83d807", "signature": false}, {"version": "7ffac5d368f45435ed9d4703393a2985b03ae5bf98411ea1bdbfe5ecb0807918", "signature": false}, {"version": "79bd03209beea1eeeeb77e643a22894b21628628249e79d67a78c5b658242429", "signature": false}, {"version": "ce12fcc427f745af1fe5ca6595b486f2043fd32b430a9b4e6677fc1f768256c7", "signature": false}, {"version": "c19012befc7fa0dca216cd574620b15da1cf4ad2b62957d835ba6ccdbb1a9c27", "signature": false, "impliedFormat": 99}, {"version": "cc0048f62d66e974d5c563bcc0b94476e8a005406ed07ef41e8693316b2e31bd", "signature": false, "impliedFormat": 99}, {"version": "4dcdbdbc992d114e52247e2f960b05cf9d65d3142114bf08552b18938cb3d56b", "signature": false, "impliedFormat": 1}, {"version": "b75d56703daaffcb31a7cdebf190856e07739a9481f01c2919f95bde99be9424", "signature": false, "impliedFormat": 99}, {"version": "ddb5454371b8da3a72ec536ad319f9f4e0a9851ffa961ae174484296a88a70db", "signature": false, "impliedFormat": 1}, {"version": "fb7c8a2d7e2b50ada1e15b223d3bb83690bd34fd764aa0e009918549e440db1d", "signature": false, "impliedFormat": 1}, {"version": "b75d56703daaffcb31a7cdebf190856e07739a9481f01c2919f95bde99be9424", "signature": false, "impliedFormat": 99}, {"version": "9c909c17f69f125976e5c320eded3e693890d21b18cbc4caa246ec4fda260dcd", "signature": false, "impliedFormat": 1}, {"version": "7915d50018073244a9bcb3621e79b8e0ad4eedfb6b053fc945cad60c983bb11b", "signature": false, "impliedFormat": 1}, {"version": "ea7b47bc357858506e6161065b1a8997cfbc5d1dcdf233966da9d01d74721ef8", "signature": false, "impliedFormat": 1}, {"version": "50444daaee4bf4ad85ad8eb52e3ad5c6bba420aad9e2a800043a78f4d8bc436c", "signature": false, "impliedFormat": 99}, {"version": "1fa33d8db2a9d2a7dbfb7a24718cccbcde8364d10cce29b1a7eea4cf3a530cbb", "signature": false, "impliedFormat": 1}, {"version": "b75d56703daaffcb31a7cdebf190856e07739a9481f01c2919f95bde99be9424", "signature": false, "impliedFormat": 99}, {"version": "90300bef1c0e2523c97fdd178b9d50e3f39646ade67faab69be4e445937c862a", "signature": false, "impliedFormat": 1}, {"version": "381437930df37907c030519b23ffea4d8113f46e4431a70bfe008a0c43c63648", "signature": false, "impliedFormat": 1}, {"version": "695cbb89013bc9e87fb24b0df020fe605c54f0ab5c267b5bf0490ed097044197", "signature": false, "impliedFormat": 1}, {"version": "f43780383543bfcdc0a2ee850375e1f03d94bdb1b85091d5b11bb8b2023c8b49", "signature": false, "impliedFormat": 1}, {"version": "303638e9e9378e3cce14c10a276251b2b6baea811f882b0adb6d8b7e44a8245e", "signature": false, "impliedFormat": 1}, {"version": "93fc1a008c4786aa9970b7a4c56295bef4d39c243af63cbfcbd5548ca4fdd535", "signature": false, "impliedFormat": 1}, {"version": "6b91aca1948fd92e4fb32e91e94955e7b7c12fb8cbc0a40eb55f1808886e53e8", "signature": false, "impliedFormat": 1}, {"version": "1e197b6e669b8ece0a68c684af9a4394d8c47e58eaa040391cbdadcc1b5020a0", "signature": false, "impliedFormat": 1}, {"version": "fccfc90c19498513d5c4b9c705706660eba9eb493bc38cdc16a11e9d384cd086", "signature": false, "impliedFormat": 1}, {"version": "b288bbe96ea05e353f008a4d445fb8589a82f2a1c4d4d0bdfc283a19020dc96f", "signature": false, "impliedFormat": 1}, {"version": "b75d56703daaffcb31a7cdebf190856e07739a9481f01c2919f95bde99be9424", "signature": false, "impliedFormat": 99}, {"version": "cf32b34fb9148d541c100a83fd3d204ced00c76871b4811d53b710ff15a948a1", "signature": false, "impliedFormat": 1}, {"version": "6940a178bd12acca76e270f0b0c4d907b9cc469d28833bd59a3276f11668591e", "signature": false, "impliedFormat": 1}, {"version": "b189e328b0f8cfffbaa9b705d5b1d7ff21a58d2910614d449ae052bd6f6977f1", "signature": false, "impliedFormat": 1}, {"version": "ea7b47bc357858506e6161065b1a8997cfbc5d1dcdf233966da9d01d74721ef8", "signature": false, "impliedFormat": 1}, {"version": "9dc9c7a268e5b2caa79a5a5040a86ba5ddf1cba20d8715ceaf2b76f79ee444fc", "signature": false, "impliedFormat": 99}, {"version": "84920f743c6fe02da67c1aeab9bd4e2d377ad96197e9960cb0e7738b8584ad0c", "signature": false, "impliedFormat": 99}, {"version": "c048b081418f530417dd4193b47890bc734711378df819f0ff217144f6775afa", "signature": false, "impliedFormat": 99}, {"version": "e6332e193ef43377d724d8f6efa5e2b36b5ea70389cad57e8a5176e8035ceac8", "signature": false, "impliedFormat": 99}, {"version": "bf0b1e6c1bb4930244203a593c6db7aed456e635c31aba73ee2102c55998861f", "signature": false, "impliedFormat": 99}, {"version": "5c21ec7196196aa797c5bcaa3bbd55f80091b4f793438947e9802376b3538927", "signature": false, "impliedFormat": 99}, {"version": "1f653a61528e5e86b4f6e754134fee266e67a1a63b951baccc4a7f138321e7e6", "signature": false, "impliedFormat": 99}, {"version": "76e3666a9f4495c6d15035095a9bb678a4c3e20014dc8eb9c8df8dc091ec8981", "signature": false, "impliedFormat": 99}, {"version": "055bc641ca1f1eed76df9bc84ec55aaff34e65d364fea6ae7f274ba301726768", "signature": false, "impliedFormat": 99}, {"version": "22ebe7ce1ddc8ee5e70f28c41930c63401e178c637d628b9af9f7a9c456e86b0", "signature": false, "impliedFormat": 99}, {"version": "041c4afbee0a17614e9d4a8aa4385ffbbbfa1a5d5148c9aab0dce964be1af0d6", "signature": false, "impliedFormat": 99}, {"version": "00d259e465df20202e848bf8d192056919e460a3de20aa14f59d523d3af38b29", "signature": false, "impliedFormat": 99}, {"version": "9cbb746b8d46880874f6a8f8c64dfa925ec0cf70412d4ad5e00a8756c82edf3c", "signature": false, "impliedFormat": 99}, {"version": "fd23901347e68e39f7043fc6787b2af6c7094d6c7ef6038ee909cfe26da625c1", "signature": false, "impliedFormat": 99}, {"version": "818a39ff71deaab13a1aa427802c76d3976c365302ddd862810da9e428c8ebb1", "signature": false, "impliedFormat": 99}, {"version": "ef3a6a6b54ff97244df620aa06d7df4d5474d0274617e265e041246c1b7d05c9", "signature": false, "impliedFormat": 99}, {"version": "881c9f22c8d6ffc25b57cc4cf60cc27576d979a8d54ce85dd740d83b0571a088", "signature": false, "impliedFormat": 99}, {"version": "3be840cd66eea7fddebcbc83265943f7f0029a8bff513919fb78450400054dba", "signature": false, "impliedFormat": 99}, {"version": "4904ff0e4bda91f1b7e50a3738c91f393345de5f7e5d0fea9da581e42ec92fb3", "signature": false, "impliedFormat": 99}, {"version": "5f6442d0a9bbb961b58f45d09690a034734aeea01f2875cb0e7ec31aa3676ef7", "signature": false, "impliedFormat": 99}, {"version": "6511839e63105744b3bb8b340791218b253bdae80c7d57c288dcc85bc6f91317", "signature": false, "impliedFormat": 99}, {"version": "14890b158c9bf9f4f6ccb8c8c071881439aea4301bbf5988fecd23f220e8156e", "signature": false, "impliedFormat": 99}, {"version": "3f01edcdc9641acfb6689126d9506248d3a3afe3e4a23e2f7588988ba693f349", "signature": false, "impliedFormat": 99}, {"version": "a12f75a9a3aefb304abb528b2898c085356d4876e77ccd2dd1c708bd660041cd", "signature": false, "impliedFormat": 99}, {"version": "6ac1b4401d51471ae0d6b6bcce637e550eb78d75b1cfe993b6eaca9898d74976", "signature": false, "impliedFormat": 99}, {"version": "aaba5744f8794b7cebab915aa45ca71d322bb2086d7c7aec6e858c313bf6cc69", "signature": false, "impliedFormat": 99}, {"version": "894395299a4761cd4e38c20bf17bfce27a3cbdc2650054e5fc28e692fddc4b4c", "signature": false, "impliedFormat": 99}, {"version": "7568f6aaaf6b62b7f3f72ebd07bbabd95749a0f969dfb15e7789d4a3c8e080a1", "signature": false, "impliedFormat": 99}, {"version": "039d7ce09e9246c255c7acc1c00ba3afe7e98b4767547ccb6b77274109f8a5c1", "signature": false, "impliedFormat": 99}, {"version": "b4b9514c90add4b59499251f760f01aa7fdaacb02894ff0d885286094cef8c2a", "signature": false, "impliedFormat": 99}, {"version": "f670e23ac2377ed32187f39d02be707c9c0cd61e95786a6ba49ea7f860baa50d", "signature": false, "impliedFormat": 99}, {"version": "25f27d8da6c42f1622b0b01fc5c78f48c79c645e10c4849fc8c5521faa9ace29", "signature": false, "impliedFormat": 99}, {"version": "54e17510b0440980e3bc8ce141c9b922adb6c8e77ee81c443870bf684679255a", "signature": false, "impliedFormat": 99}, {"version": "3e9e2f295358fa46f10faa524be6e99a42114752b0e195ae997f550968ea481f", "signature": false, "impliedFormat": 99}, {"version": "74cf1308a1f0de094f0e8567541b0a0e126426ec2eb4ef68c9cd97fa4d0d9272", "signature": false, "impliedFormat": 99}, {"version": "dcd1e783bde43c7d570ce309cc21e9d9d7b3110491aef9c5c5ce87c6a53f7e5d", "signature": false, "impliedFormat": 99}, {"version": "08bc14542d8d34fd138945413e31ecf65668e029f966b5aab5b25e8e421efead", "signature": false, "impliedFormat": 99}, {"version": "17648a898be56a6a9c4a6305e84ba220bc76d4355f0f55696726f1eb1fcd6d4d", "signature": false, "impliedFormat": 99}, {"version": "cc6c1ade000cc9b7f8c79d8bdddb145950bbe7d404e5b3b938537a0bbfba73bd", "signature": false, "impliedFormat": 99}, {"version": "eb97def43c2617552f76eb367e7f5531127fa03fdf991ef12cf5ae8fcc52c7ed", "signature": false, "impliedFormat": 99}, {"version": "f49bde1443de7aaf05371f049ee0710619bde1b7bb7042192512e5cab672b3fc", "signature": false, "impliedFormat": 99}, {"version": "a704c8b701194cc47d333b093f87db332694b124e304fb0167be09ff3304d353", "signature": false, "impliedFormat": 99}, {"version": "358f8d33b436d21a7c313f02e900b805eb1c6abda3d675f703ada38eea3b92d5", "signature": false, "impliedFormat": 99}, {"version": "dbcf8b1a2d94e9a1f0fa3fd5152114a14f83d8dba8d3f8dd773be476adac937f", "signature": false, "impliedFormat": 99}, {"version": "ee63e60be6f56e08cf8d7b5ab750078fc6d08f69cdf70ee43fd0693d10c65d2f", "signature": false, "impliedFormat": 99}, {"version": "4807b8b139747bd82ef181b5eaf8676c1f9012be0ad91feb1173bd57f08aaac8", "signature": false, "impliedFormat": 99}, {"version": "ceee442c1035bd941c9fbddbab08fce2e34d1e23d79d56a48c0444bb45d705b7", "signature": false, "impliedFormat": 99}, {"version": "fb9bcb4ee14feca03c05eaff9f1eb826bb1e75bade5e64f98c65ecc79b910949", "signature": false, "impliedFormat": 99}, {"version": "f8ee6c9ecf3a39cb551db7d6f0aea157cd272ac477c561331efd734a13b34134", "signature": false, "impliedFormat": 99}, {"version": "f72af7f1a38a5b8ae564be5eb68a8c25e5cf9cf4c567ddfa471a481425369c79", "signature": false, "impliedFormat": 99}, {"version": "aef37af42cec810a643f24ba90f2f7d55c3e05ec5e31adca4c3318e578822aa6", "signature": false, "impliedFormat": 99}, {"version": "ce35f35a8d59172dbf5cd945c253512114d6020e7dd30d399d372e473eff2515", "signature": false, "impliedFormat": 99}, {"version": "e9e8a6bbb3819df983667e1bbf9c993e954c009f575c1f5d2063d55c1af47d1a", "signature": false, "impliedFormat": 99}, {"version": "fc1eda40a6dc0e283ac8d75cec0082f6cc49c517ae608d2413e872ef2f5c2e84", "signature": false, "impliedFormat": 99}, {"version": "bf0b1e6c1bb4930244203a593c6db7aed456e635c31aba73ee2102c55998861f", "signature": false, "impliedFormat": 99}, {"version": "44993fcc19de9502ac3f58734809acbe0b7af3f5cca12761dc33d9a77cf02d1b", "signature": false, "impliedFormat": 99}, {"version": "d172b164580892e56129985557aaf73b4e45279e4e0774e1df53282e6fd89427", "signature": false, "impliedFormat": 99}, {"version": "1e1e240fa12ec7975ee7c9803e2e3751399820b4435f476ecfe22656809916f9", "signature": false, "impliedFormat": 99}, {"version": "68f1a4ec2937052ae0dd18407eb8d1b579708970ced79c6e7cfe4a93d0a00385", "signature": false, "impliedFormat": 99}, {"version": "efe0fabfc89403ce6a4a8b1fe3a7633f1161b7e10d9824299560f2d15e4e606e", "signature": false, "impliedFormat": 99}, {"version": "64c4a5d1bb65e93416fb1ca1d08210dcce25d6d8d1208039a58e4379a647bd76", "signature": false, "impliedFormat": 99}, {"version": "e84f2065c605965fd1d44de2cddf0509dce060b4d9e79c01a884a0899fe877db", "signature": false, "impliedFormat": 99}, {"version": "b0df9d1b07f9ffc72ac128e5a05da99af0e3a8a19a08d8defc26678c0e30c25c", "signature": false, "impliedFormat": 99}, {"version": "16725a633f5f5c1cd82e2baf4b0ae521da7f6055339f837bf2695bc3fd44373f", "signature": false, "impliedFormat": 99}, {"version": "664104ab990ca5d100a69e159f9f8874551d94a187db834309af14fee2d64f4e", "signature": false, "impliedFormat": 99}, {"version": "542e50c2dca6d24f5cb9cb2b7a5c07d450850af21ef253838bb2bbfb175a3e8c", "signature": false, "impliedFormat": 99}, {"version": "6ee3000708f3add1fe74964fd6ea6b1f5abf82151481babb96f7905a763ad5d8", "signature": false, "impliedFormat": 99}, {"version": "93640558bd78d5f98d7bf455d07e79f700efbe2f9826958d4b2acdcafbb5ba89", "signature": false, "impliedFormat": 99}, {"version": "fd8b58b771380655281dca6ed40019cd8ecd639ef6ec74baa91662ca0e0ae458", "signature": false, "impliedFormat": 99}, {"version": "6a73dc1806928e57c21fc51d00f40e4e92f17dc6b31ddfa95365a837651587c0", "signature": false, "impliedFormat": 99}, {"version": "ce35f35a8d59172dbf5cd945c253512114d6020e7dd30d399d372e473eff2515", "signature": false, "impliedFormat": 99}, {"version": "97912ca64fedc028914d9f1585e30d98a1e1e46a426a06f2190024067b8a534f", "signature": false, "impliedFormat": 99}, {"version": "a9b65aa46a4613eef2bef431366d8f5f166e8226c6fae3688c67ca102c3d6a79", "signature": false, "impliedFormat": 99}, {"version": "5fbfad634244c213e44e6b3e8e7936ccfb74bf163750dfbd1464140d8230497e", "signature": false, "impliedFormat": 99}, {"version": "0caecd57de90295669dd561bf9f0e4c4478434e14e0741c2b0fbed44e38563eb", "signature": false, "impliedFormat": 99}, {"version": "bb125cb4f8a3155a5dec027913e615c6b7f1000f0c600de19798ac4f0c8a6c5b", "signature": false, "impliedFormat": 99}, {"version": "78c0f55d5519d39233daf5562c5704a0322dd7abcc1e72afb015cac550be32d3", "signature": false, "impliedFormat": 99}, {"version": "95f1e94151a3a45c139a9efb748888d1af359521f6c96e7e644e070913fafc31", "signature": false, "impliedFormat": 99}, {"version": "f72af7f1a38a5b8ae564be5eb68a8c25e5cf9cf4c567ddfa471a481425369c79", "signature": false, "impliedFormat": 99}, {"version": "205d330174cc427f3002517bae08e2cf8b8e134cfe086cc80fe18a07efeca799", "signature": false, "impliedFormat": 99}, {"version": "93d7cf0d29aa72f51299e10d738149a77bb92d42473d3145428cdfedcaf8efa3", "signature": false, "impliedFormat": 99}, {"version": "03535e283a156874e32846037dc86e32c53995db4e077d392a8b17c6f26e4f8d", "signature": false, "impliedFormat": 99}, {"version": "d8f104b12bb1e0ee5690c50f3d6100f71c24145687190a5f2d5ba7b52538d57e", "signature": false, "impliedFormat": 99}, {"version": "aff2d01dbf009d2dc7c5aa71d32930d4783463a08527775e834e2e37bbed5b4a", "signature": false, "impliedFormat": 99}, {"version": "c63356e770e4fa3fd4d6cff5e804e557fafaef2bad6f5b81291d15b1ff21da8e", "signature": false, "impliedFormat": 99}, {"version": "47457637fa208f3d77e4b03a8f117a418a8ead3486995dbe0d9f915e967c9070", "signature": false, "impliedFormat": 99}, {"version": "87621a249f7a938e9d270b70e560b78b55552eafd08ddf71d2fbd80913699488", "signature": false, "impliedFormat": 99}, {"version": "8c40fdc32e3fab434b704c3bd731a12d479a061fdc72f42f665f4b0c287ad7e4", "signature": false, "impliedFormat": 99}, {"version": "400402da2b06f5acd7940db2ee5507784fdab53354062fcddfe4934f3ac04340", "signature": false, "impliedFormat": 99}, {"version": "3e80aeb2dad64ce73bb62a404e1db152fd73bd5849b1777d444939d0c1cfc287", "signature": false, "impliedFormat": 99}, {"version": "61f825380b5ff41a275f6d0cedd145a073524cc24b4963f82c4348574325768c", "signature": false, "impliedFormat": 99}, {"version": "d457f5d460966fee473f543e400f8e0784ca9875ce6aecd48b7ff0f6351a04d1", "signature": false, "impliedFormat": 99}, {"version": "b41d3caa8c0839223be817bfedea85bfcf1e682182d51414fd11d9ccaf83792f", "signature": false, "impliedFormat": 99}, {"version": "2b5637680ce53987f0335180e79a9dd639ccfa8f20d46332195dcf11c02e9bb7", "signature": false, "impliedFormat": 99}, {"version": "08bee5ad21bf8bf6d1e66f9bcbcf1c790c1873ae5d63068c02567c357ae619fc", "signature": false, "impliedFormat": 99}, {"version": "2e76803b80712451178add529e574c5b6acfa0ef4ff169dc5f8a4dfabb43704a", "signature": false, "impliedFormat": 99}, {"version": "931c8729cf2295582ad36e56947aa4253a554135800a5ae3c719e2937061319f", "signature": false, "impliedFormat": 99}, {"version": "949ccc4add0506d70be23ded8fe17702ce7ecad3f6b9b2948d12be7b7621c008", "signature": false, "impliedFormat": 99}, {"version": "8b5aa4aceca84ffb115eaa92eb511db532a380715fbe40e0f2691399f59779c4", "signature": false, "impliedFormat": 99}, {"version": "fa161dc810c98f507b7c8fe8d1cc978ef6cecfd05a91a0897b272ff3d424f53e", "signature": false, "impliedFormat": 99}, {"version": "04498bab7aa04819b6f85e0a833cac9a90d2c225449e62a500e0d969a980a0f5", "signature": false, "impliedFormat": 99}, {"version": "6378847b2becc1fd081eaae8ada8632a1e82a6fb68223b4b4b6db1f6b3783709", "signature": false, "impliedFormat": 99}, {"version": "953be5c29962c02b750c81742c6c8e3ec88f0dca93b490ae0c25d06ec09a336b", "signature": false, "impliedFormat": 99}, {"version": "93c47ea71b8ac6043e85e16a7f5a12fdf28283e0c3e64818b24ef77339dde953", "signature": false, "impliedFormat": 99}, {"version": "d0ebe2f759e4811f5157b9a1e1920458dbc5d4566fce7af6c6a777abcc31d7d0", "signature": false, "impliedFormat": 99}, {"version": "0a5c9fcea7d8dfde5b22c26763cf7c8822a99ba7774b87d4faa63fe165f371d3", "signature": false, "impliedFormat": 99}, {"version": "79e012a9efce1afb73f1d04c643326f3a90ecad76274b8b099711300f475c561", "signature": false, "impliedFormat": 99}, {"version": "cd80c1f39858c9aaf24cb6cf109d90b16470b4c4af5b712b350e6e18b08c1d7e", "signature": false, "impliedFormat": 99}, {"version": "d31e7c5b91a9310f9ace7e2c19e72ba501236af707639fe184d592b6f3aa612d", "signature": false, "impliedFormat": 99}, {"version": "ef0a3e581b336ec4522badc01575daa324a63e76b7317ceda2ef887a5168e2e2", "signature": false, "impliedFormat": 99}, {"version": "5a3458dfcbd3d376e91a57ff64ae747c34f8ca1b503b1be1a84f490b56da1638", "signature": false, "impliedFormat": 99}, {"version": "684fed66904651fd676b78ec044da251651f4dfaedb163df74b2280013d5cd5f", "signature": false, "impliedFormat": 99}, {"version": "78156ec80b86cc8f8651968051ed8f9eb4b2f02559500365ee12c689c2febd9e", "signature": false, "impliedFormat": 99}, {"version": "0383ff8743bc48551085aa9b40fa96327e857764fc0b8e4657b06db1b0068f79", "signature": false, "impliedFormat": 99}, {"version": "da84ac2614990bb98cc8921995af5c6e99cdea1eae3d92692ef6d4a152e9df68", "signature": false, "impliedFormat": 99}, {"version": "df9ca548acc13813971b2a578514bfb3383fffc0f3d88cc2b49150accf4cf090", "signature": false, "impliedFormat": 99}, {"version": "e463bccc0c9e8e19113e8f5684fa1e0d357fd66cbc7a495a3c4854442268ab0b", "signature": false, "impliedFormat": 99}, {"version": "01104176c1be6e4db2f152e17202e2752e01dd7dce8bf1fbfcbc85a54acd25f0", "signature": false, "impliedFormat": 99}, {"version": "2e415d3626693f39e40f19ad427f6ad173dc4bde2a7c4ef6a655f30d052b61b0", "signature": false, "impliedFormat": 99}, {"version": "496b4dd6da860c392c036aab07f706f623902707e0af1cef271eb9a6a827aa44", "signature": false, "impliedFormat": 99}, {"version": "c98069496e78eba403f51c1a7d582ae0e0f338e2d63b6417e561c9f56cbe88c6", "signature": false, "impliedFormat": 99}, {"version": "89e6832e87186cf2f1924ccbbdf510db4ed9d45271b332a1cb1ed659eaa0c874", "signature": false, "impliedFormat": 99}, {"version": "4b0e0173e248db6eab5b9402044f2f1a2d086e99d9d8af6c4a7f46f52cb6d787", "signature": false, "impliedFormat": 99}, {"version": "8d56ae9f7cac9011b44edb4905ad58cb57d12199ca56fd23a16c5714b15d368b", "signature": false, "impliedFormat": 99}, {"version": "a39d68209be7cdeb86ea872366f7c9f3578e657dde3eb1489012833c87028ff3", "signature": false, "impliedFormat": 99}, {"version": "8fc83926d2b5737ff691660774a9ab5829b5fb77d9a382eb97bb2786b8b2a661", "signature": false, "impliedFormat": 99}, {"version": "c5e59270f3237a2bf344ac83ab3095f30c0ad8f3f07e41f266e662ce544520c5", "signature": false, "impliedFormat": 99}, {"version": "63d8897302acaf122123a675c9e4875a1fc7d82bbc62a54949d595119b1ad049", "signature": false, "impliedFormat": 99}, {"version": "1bfb743c928bfe9fbf9ce88bdfaf8235edb1d3ea0b5ab446603d71c4ac87d802", "signature": false, "impliedFormat": 99}, {"version": "b6e92e897f1bd0dab01bb0f64bd70956f574c7752f7bbdc7f107460a074b707d", "signature": false, "impliedFormat": 99}, {"version": "6841d50aae775f444751e244f756085d8fcf34f94ff6647aafe8041b63fc89fe", "signature": false, "impliedFormat": 99}, {"version": "a3c33f57bb6ce04191478ea23a17293d382cddb7aee7b56bb5aed3ca49c7fa60", "signature": false, "impliedFormat": 99}, {"version": "c9bfc8572556f746686beb2ac476f999356253c4b3fcba189327b25b30c47801", "signature": false, "impliedFormat": 99}, {"version": "2d0bedabb6ca97235d746f5e1dd974c4975e8833985f6efb82a995afa06fea38", "signature": false, "impliedFormat": 99}, {"version": "6af214e64dbf7c599257f7f0851cb57b267c6eef97dbca04b1f2d204ac571fdb", "signature": false, "impliedFormat": 99}, {"version": "58617876087d1660ff295d2d76c325e50a42e5fd9bb7dfd9d02963ef80c8fced", "signature": false, "impliedFormat": 99}, {"version": "ac84c9b0786abb646dfce8480f6ebf83370a47a45d8bd7e2bc705f1069bc71b5", "signature": false, "impliedFormat": 99}, {"version": "d0fa8bcd9d99495de67ccbc3124de850e514f3eea0dc0c40f927ea8511bf8e8b", "signature": false, "impliedFormat": 99}, {"version": "504d56c1b6bbbe20409104ad2388b9f70d0e5f7091846e39674987c0b05af7fc", "signature": false, "impliedFormat": 99}, {"version": "98c33da6fd946601b36415c760e677c1faed100c361fee8c45565d8d6a00aca1", "signature": false, "impliedFormat": 99}, {"version": "afabd37daf4bc1b2604caedd796ec9deb277d7f3f1927ecea80cc9eeda678518", "signature": false, "impliedFormat": 99}, {"version": "1cd9c44575b349148a044fb300d2dade101e663dc7556b7c0b9aa4494dc88de7", "signature": false, "impliedFormat": 99}, {"version": "c59eee5e50312900effee1403fa07d9386e95dfaf20411a854729acdf6787629", "signature": false, "impliedFormat": 99}, {"version": "8c8b35b1251978c2156c04db23ce6b842f48db71d39b42dd3c537dfa099e5ef9", "signature": false, "impliedFormat": 99}, {"version": "0001579790ad5940cb4f59fbdf96b540a867b3d2c36624426aaa4fbcea1a4a1f", "signature": false, "impliedFormat": 99}, {"version": "9b571fa31a14b8e1e8e7412743e6000be66b7d350358938c1e42bcd18701c31f", "signature": false, "impliedFormat": 99}, {"version": "9a14a6f51a079956ce0a7ee0826c7898825dea24be60e10802e18b46f142efc3", "signature": false, "impliedFormat": 99}, {"version": "f2f1772f08149a999525bb78ffa3d504a851162d8dfbc7e9b8039baf42eb20bd", "signature": false, "impliedFormat": 99}, {"version": "f0410c617e9f6d332d7b860a1c3a679f7fa3e00e89699dfbc6b4f563b12b350c", "signature": false, "impliedFormat": 99}, {"version": "ace1cb8ad5d6a8cec49a1d4c26757bea48fb6612e0f6ca99581253b5893eaae2", "signature": false, "impliedFormat": 99}, {"version": "8cb9b25afdb7e0662b488c04b05ab598c5e52fd8a605628788080a163b583923", "signature": false, "impliedFormat": 99}, {"version": "b6b726231178cb2695b8a83519d4fa50a03e800fa9b2dd75193a56bf6cb58a08", "signature": false, "impliedFormat": 99}, {"version": "70a29119482d358ab4f28d28ee2dcd05d6cbf8e678068855d016e10a9256ec12", "signature": false, "impliedFormat": 1}, {"version": "869ac759ae8f304536d609082732cb025a08dcc38237fe619caf3fcdd41dde6f", "signature": false, "impliedFormat": 1}, {"version": "0ea900fe6565f9133e06bce92e3e9a4b5a69234e83d40b7df2e1752b8d2b5002", "signature": false, "impliedFormat": 1}, {"version": "e5408f95ca9ac5997c0fea772d68b1bf390e16c2a8cad62858553409f2b12412", "signature": false, "impliedFormat": 1}, {"version": "3c1332a48695617fc5c8a1aead8f09758c2e73018bd139882283fb5a5b8536a6", "signature": false, "impliedFormat": 1}, {"version": "9260b03453970e98ce9b1ad851275acd9c7d213c26c7d86bae096e8e9db4e62b", "signature": false, "impliedFormat": 1}, {"version": "083838d2f5fea0c28f02ce67087101f43bd6e8697c51fd48029261653095080c", "signature": false, "impliedFormat": 1}, {"version": "969132719f0f5822e669f6da7bd58ea0eb47f7899c1db854f8f06379f753b365", "signature": false, "impliedFormat": 1}, {"version": "94ca5d43ff6f9dc8b1812b0770b761392e6eac1948d99d2da443dc63c32b2ec1", "signature": false, "impliedFormat": 1}, {"version": "2cbc88cf54c50e74ee5642c12217e6fd5415e1b35232d5666d53418bae210b3b", "signature": false, "impliedFormat": 1}, {"version": "ccb226557417c606f8b1bba85d178f4bcea3f8ae67b0e86292709a634a1d389d", "signature": false, "impliedFormat": 1}, {"version": "5ea98f44cc9de1fe05d037afe4813f3dcd3a8c5de43bdd7db24624a364fad8e6", "signature": false, "impliedFormat": 1}, {"version": "5260a62a7d326565c7b42293ed427e4186b9d43d6f160f50e134a18385970d02", "signature": false, "impliedFormat": 1}, {"version": "0b3fc2d2d41ad187962c43cb38117d0aee0d3d515c8a6750aaea467da76b42aa", "signature": false, "impliedFormat": 1}, {"version": "ed219f328224100dad91505388453a8c24a97367d1bc13dcec82c72ab13012b7", "signature": false, "impliedFormat": 1}, {"version": "6847b17c96eb44634daa112849db0c9ade344fe23e6ced190b7eeb862beca9f4", "signature": false, "impliedFormat": 1}, {"version": "d479a5128f27f63b58d57a61e062bd68fa43b684271449a73a4d3e3666a599a7", "signature": false, "impliedFormat": 1}, {"version": "6f308b141358ac799edc3e83e887441852205dc1348310d30b62c69438b93ca0", "signature": false, "impliedFormat": 1}, {"version": "64f588374cff45a495d9da0722e88fa7c4a77b7024ea17750a7c947fb8f08e98", "signature": false, "impliedFormat": 99}, {"version": "5ca32089fa4a40b1369f085635aadc4bf853bc4ea4dd49eac0779bf9f62423a3", "signature": false, "impliedFormat": 99}, {"version": "5a46f69508e086a0f63d8fb15717422e9ea54d1813be3798c2220bbd9c8ef43c", "signature": false, "impliedFormat": 99}, {"version": "21e29420bf5da1147cf6ebcd8cd85afa21dc3cbf04aee331a042ae6f94c1fa63", "signature": false, "impliedFormat": 99}, {"version": "71e67299f77ff5da289ee428bb85157485f4a1d335c1b311288262ca04736b85", "signature": false, "impliedFormat": 99}, {"version": "5df08c4af12b3ec3b3e6afeadd08eaaadcdc2825f50335de914b505ee3252964", "signature": false, "impliedFormat": 99}, {"version": "9bab9e8d65ff83bceec753685598d1d522ca1735a2983eb8c881dc8389b6c008", "signature": false, "impliedFormat": 99}, {"version": "0356b906e53157425c8beb4e5673c71fa80d88e1cd32759d4bd57e59698ef88f", "signature": false, "impliedFormat": 99}, {"version": "e72c8e9bc1e2c9a55f6755f85150c3f63d63c3e13fa047656404402b22ae249e", "signature": false, "impliedFormat": 99}, {"version": "edca1f05d978d3c2feae191a82e34710dd8fedb83a24c2fab15373be5be8a378", "signature": false, "impliedFormat": 99}, {"version": "36ac04ebfefc210ab3c0148cbfc451f3434e9ca7048b19827a98247875923176", "signature": false, "impliedFormat": 99}, {"version": "4e152e1b7f2d588e6279ed5ee1815770a12e32913f06a9191f0f3cd60b01aaac", "signature": false, "impliedFormat": 99}, {"version": "d44ad42a40c4e84bcccc9a5db198f86afa6196d42e152cedbe09d513bff01fb5", "signature": false, "impliedFormat": 99}, {"version": "4f20bc9c75b4515c25c3de1cc6c5391972991a25136b796f8c6601a809e80796", "signature": false, "impliedFormat": 99}, {"version": "c9652370233cf3285567f8d84c6c1f59c6b5aa85104b2f2f3ade43ff01f058d2", "signature": false, "impliedFormat": 99}, {"version": "2670ba717e7b90210f244401d5fe6f729cf879cb2938b6536c9c118371ef24a2", "signature": false, "impliedFormat": 99}, {"version": "2e86a352fce1cf1df7be54b242d65c5efa3d66a445a60b2a0f7c33a60ed76eeb", "signature": false, "impliedFormat": 99}, {"version": "9b3abc22bb11e450c1c77674d11719e4eeebf980315470587cfd461d1d407606", "signature": false, "impliedFormat": 99}, {"version": "02e6668da999217b040e0d8d6e41daa96d7f59eda7bd9dc9156378584116b296", "signature": false, "impliedFormat": 99}, {"version": "7c52a6d05a6e68269e63bc63fad6e869368a141ad23a20e2350c831dc499c5f2", "signature": false, "impliedFormat": 1}, {"version": "556261268d31864a619459b9bfece0058e468456ff0ce569fbea916e6b543910", "signature": false, "impliedFormat": 99}, {"version": "827508bd5aee3a424eb2e91965c5ef78e2ec95585b4074399179b70d8f66524c", "signature": false, "impliedFormat": 99}, {"version": "97bc3fd65336434e6330e0a9141807cbde8ba4045989809632f70ba93f70f6d3", "signature": false, "impliedFormat": 99}, {"version": "d5bcc410b5ab12693f89a3c477f8dba724d881d87498adfa8ed292869b393c7e", "signature": false, "impliedFormat": 99}, {"version": "eedc9017d949f60aecbefa1c093f6d70bdb1dea65f5c50ceaf1e1fb30be978f4", "signature": false, "impliedFormat": 99}, {"version": "9f313a2d30d03a9954269fa7c7f5cca86ffe2ae6c1ea14741c3e2794aa805806", "signature": false, "impliedFormat": 99}, {"version": "2c4945c48f529153672e10dc7b67f414ac7e7678bfcd5d6b79842ae28a330002", "signature": false, "impliedFormat": 99}, {"version": "24ec3cb8a40752890fde2a1d694c43bbb0fe1eb0d1e61793373564be5d4c6585", "signature": false, "impliedFormat": 99}, {"version": "ef83f22620073b4b9e666191044faad4f2b3a5b4bb87e8487b8200bcc75102df", "signature": false, "impliedFormat": 99}, {"version": "99cec35e19fac2229b5c6ba317476fd2694f15a0e9a9d38f146c5f5edfe3ada3", "signature": false, "impliedFormat": 99}, {"version": "57678f3f59c73a29d71ade5be0f1ec6c5f737aef206ad61905ca5cde0c7d7445", "signature": false, "impliedFormat": 99}, {"version": "98ab624c4bb847ffac693acecf770154c9763eeb7228e28b873aa2d2ec9eacc4", "signature": false, "impliedFormat": 99}, {"version": "6d26c9ddd47ab86552f4d06e7bf051661237856cc0e5cf75d634853bbd562166", "signature": false, "impliedFormat": 99}, {"version": "136769a51b1415d74b8c03b74e9bf38e629177447065b897694072606bb26f92", "signature": false, "impliedFormat": 99}, {"version": "0a202409812f7dd20d61ded10a6984b79882fe264c76364dc53dca951a28c737", "signature": false, "impliedFormat": 99}, {"version": "06d5971c8b4a3bc00bf57f4332d3bfd92636dd4abda4fa0357c7c1dd496b1407", "signature": false, "impliedFormat": 99}, {"version": "ee67a800e8ec7418a1aac731c3e54759ece60a5aaa4c61a3daaaffea3360dd76", "signature": false, "impliedFormat": 99}, {"version": "719f559f65d32823f1db11af17b4ee08fbb19d5acd4b6feb7b6610ccc83af179", "signature": false, "impliedFormat": 99}, {"version": "432d66aa77c1e6059106ae63b5609793c1aeadc644282bf39d552afc83ee2ac6", "signature": false, "impliedFormat": 99}, {"version": "4c36226ba094c4b73a1ac45ca38815698eb2089101fc707de511bbe51dc0e6e5", "signature": false, "impliedFormat": 99}, {"version": "458a584e7898e910be8bb52341daf8466ed1d363a967f240bc082e549cfcbb69", "signature": false, "impliedFormat": 99}, {"version": "218daa4b2d1f8f6d3c4f022acce45b10b65d04086a1ab74ea7a135814521627d", "signature": false, "impliedFormat": 99}, {"version": "7f7b3faa89da29e2f52f73f7f2dd37b40c7d1e6dd8b820be1f9603bbd37080a0", "signature": false, "impliedFormat": 99}, {"version": "30d4591edcd78009f16185869f1a832b6ff00b42927d16892ede106f7b03081a", "signature": false, "impliedFormat": 99}, {"version": "6c80a54d4b2be32868d3dee7c69cbba3243d7150da9e0f3820a86f988047c9da", "signature": false, "impliedFormat": 99}, {"version": "8a50a838343a8ee7318f5a4a33defa84d325cb035ff67d4cef3f04cc3dbd7c72", "signature": false, "impliedFormat": 99}, {"version": "93f0399b89384f652cb73f597865e287b69db239dbb52c044a6844cb44a45b1b", "signature": false, "impliedFormat": 99}, {"version": "8ce4ebea4cd4077327faecb7a29805b4e6080bc6b9bac23df6adc601b0a12a18", "signature": false, "impliedFormat": 99}, {"version": "9553bb2ddc97cadf255d6056236f335fb3d0b34cd3ff34ef7dc170d0004d8f05", "signature": false, "impliedFormat": 99}, {"version": "522651983601a3d0a24eb8104086714d8e9a958810503275e45cd6ff263cf416", "signature": false, "impliedFormat": 99}, {"version": "a8f9fed7dba6d9a5c6ed93b7c8e02c892c184c8153639a6ab3ce30ffe30c43c2", "signature": false, "impliedFormat": 99}, {"version": "ddec04cd05ab7614a2d51c3fbafa772b47cec4d7d6be80c1de8d37e4366692d1", "signature": false, "impliedFormat": 99}, {"version": "a28d089808860737ef08c33c36db5e3db57ec5c5fd41acdbeb0f0d1d8f7a1519", "signature": false, "impliedFormat": 99}, {"version": "c921f5db48373afab4577ce6dbd5dcff50c41a0f34aaf4529808affc733f75a2", "signature": false, "impliedFormat": 99}, {"version": "51b1dce48fa5bde70b49e5586d0bf7ba3371e172df994fd6401bba8b436fb852", "signature": false, "impliedFormat": 99}, {"version": "09a2cc054e9070ff418f718c410e0065a56447a91e4770d619b58142b7ca7800", "signature": false, "impliedFormat": 99}, {"version": "f54905bfbb9932598ef1baa355769ea8e96e3783e4736b0d31723a520eba38fd", "signature": false, "impliedFormat": 99}, {"version": "aec5756720255bd7045409db869db09031ce31003dc654175f552d59b196313f", "signature": false, "impliedFormat": 99}, {"version": "86892d5bcae518db21850a892aa682878f77bc6ff1fe096f5f706c91e547cde3", "signature": false, "impliedFormat": 99}, {"version": "6852847a05178fce73d3c8b6388e0b5cb23bac202845c426387762b9fcf8970e", "signature": false, "impliedFormat": 99}, {"version": "d22c80b0d938d2a571dbe1707606222fb97bd1d4bbb46fe42e326bdee6545ca3", "signature": false, "impliedFormat": 99}, {"version": "4053a0866f10634083ba91f2166420b1c29a2509b64803bd192f50baeb221265", "signature": false, "impliedFormat": 99}, {"version": "74941adf0115a098f810cc363996a95da17e6847267bc29c9d519bf8b0838b98", "signature": false, "impliedFormat": 99}, {"version": "8b5762f3138b2894db972d51cb539f1ff2bf6b231129667cb89962d4711f9c70", "signature": false, "impliedFormat": 99}, {"version": "ffa366f1f2b7ccf00d170f120836a57cc74e8548e3e72b41bd0cee00dab9dd2a", "signature": false, "impliedFormat": 99}, {"version": "b445ac5a35ce7b38f5d806a88ee4d6b3d1a3a5638243c5a246727af90a9925f9", "signature": false, "impliedFormat": 99}, {"version": "aa94cdb0dbaac5ab520157f991bdcdc953c2fbb0436cb4ef6252bba926429a34", "signature": false, "impliedFormat": 99}, {"version": "063fcb0a3805a0ccccc409d58eb166d7642bed8f35ea56af67e520d3fede1101", "signature": false, "impliedFormat": 99}, {"version": "664ea2d1a61cbe738cf3a4cbe619f775868a97d06398cfe2867173356786988a", "signature": false, "impliedFormat": 99}, {"version": "408f9b4fac8c35efc9da748f2b221efbd565a26d3b45c7b7e3899bd6be5c257a", "signature": false, "impliedFormat": 99}, {"version": "24fa0edbfe31c7c0e96f168d9e7338f9fa0e1015550300e3c47079cedc18528d", "signature": false, "impliedFormat": 99}, {"version": "060bc6464f23a8cfe35ff7b91a3ca4ad918b4f760a96e666453ea093b412a336", "signature": false, "impliedFormat": 99}, {"version": "057a6bc4d8d4ebc4817ad261915f898cf589b62194058913ed9eb4c25f14544f", "signature": false, "impliedFormat": 99}, {"version": "a458726e9fbf25d67d7eb9dbba3909f2654a475f162a97227e592b79b1e6cf68", "signature": false, "impliedFormat": 99}, {"version": "90eb37365f7f73460de47970a44dbf4760990badf21b3223e8ce0207ed874903", "signature": false, "impliedFormat": 99}, {"version": "3127a03a881f78c9145d7db821295531e8c577a8a0738847e70af2b6ad9778f3", "signature": false, "impliedFormat": 99}, {"version": "cefe8670acf41bb5cc2726613785261a6b912c729b0423ed5daadd48a268e7d8", "signature": false, "impliedFormat": 99}, {"version": "1a35bd51a28387166ff9069b79c5b1b45d917efc33381368083a645c78aa5006", "signature": false, "impliedFormat": 99}, {"version": "17e18b0edde7e814a13e0208d2db3f5a6fbe189671b57caef288e39f1f1b9602", "signature": false, "impliedFormat": 99}, {"version": "57afd9ed037a00dd2715e6128c9f305f287c9b29d9c7f556e4daa074d35a90e5", "signature": false, "impliedFormat": 99}, {"version": "221c6bb2c1152e37f7254d5a167f11ffd57f12c734e970ea15cdc59a97f2038e", "signature": false, "impliedFormat": 99}, {"version": "4220b6bb9febf019e09d875d52fe611225de4c5574412a4c1a62c324e4a82401", "signature": false, "impliedFormat": 99}, {"version": "5b6c6c22a039478fa3bc034d6d91d10c0e4d20af1829d986b78a85232cbe0d2f", "signature": false, "impliedFormat": 99}, {"version": "ac67258368872db1e2d5a8fd53fa649fe31c5abe6f62786fd4bc6e6ad51ccb9d", "signature": false, "impliedFormat": 99}, {"version": "a2568a7262a7c222ffdbe3b9296fe725a3aa6037d3792815af50923bb669b7fe", "signature": false, "impliedFormat": 99}, {"version": "1397759591619d547cbcaea8d94cca1ed29e9f6f13beffaffe9f9307e5955861", "signature": false, "impliedFormat": 99}, {"version": "77381f3914dde6135f903652e311c5bb8053dae28607f519a3716ead90429f85", "signature": false, "impliedFormat": 99}, {"version": "761bfb2da76dd72beaa61c09770aa2d4e90fd2a8c8e38f79203cde259d4ed4c6", "signature": false, "impliedFormat": 99}, {"version": "788ec71568d441083686e3c32d5238de15aab63b59481f9b91174d8b4fb71100", "signature": false, "impliedFormat": 99}, {"version": "d77ee71e3052258d3b9afcc8e921ca84f96d460bab31ac752e6237454c5d5cc3", "signature": false, "impliedFormat": 99}, {"version": "6d9b1602e3d14e16b782dec30666f2e42d287d6a5345fb7ae52111f9a1e1f92d", "signature": false, "impliedFormat": 99}, {"version": "e537ea67b8894b0ebb941bce267e16f9eb0719ab8ff37f0653d12f200339f2ea", "signature": false, "impliedFormat": 99}, {"version": "07c9867e04c1628c47fde22389e075d615795c6b7c66ea90af6c281810699d0a", "signature": false, "impliedFormat": 99}, {"version": "f5349612ec61213715349174adb060d1361fa1713a3d8d23dd1630dacd942b11", "signature": false, "impliedFormat": 99}, {"version": "ed7fc0cc7db9eee422f099e3a14c7a624afa3fcfab25d6b39da9315cfb262b6a", "signature": false, "impliedFormat": 99}, {"version": "23abf55ba0b7a59b9bfd17491675b818fc178c581686840a7aef27e45205383c", "signature": false, "impliedFormat": 99}, {"version": "06d3015b06f1f22899905d74207c52e54c051f0466975156de9067ceb884ee47", "signature": false, "impliedFormat": 99}, {"version": "21714b0d8f7fdd7be1e233d4eb2daa87d2f4ee3e41a363362276fefcc2bd45aa", "signature": false, "impliedFormat": 99}, {"version": "3ecd423076cd6107967e1b9187f38919490d790b258df54e8a6572a93ded5f96", "signature": false, "impliedFormat": 99}, {"version": "015edc4dd049b299c563701125cd50d16d9605e9927824f8371a428993c25def", "signature": false, "impliedFormat": 99}, {"version": "f84ebeaa3d5b14a9fb6b8349330e371f706f48317b1524e3968ca13c8eab2ff6", "signature": false, "impliedFormat": 99}, {"version": "242258092f0ed6960f328b9d7a455c6559c7253c6b57b08883a2fb859c4cfdbb", "signature": false, "impliedFormat": 99}, {"version": "d3002aa3f7fcaf5921ebf891a2556ff5a95885d20f0f169b12f0428e4bf80bb1", "signature": false, "impliedFormat": 99}, {"version": "848ac64950a137510b1f47d87cb0f1fe15c7eb06c8e1c2823ae63f413430653c", "signature": false, "impliedFormat": 99}, {"version": "cbd768cb4e86fa0057ca6db0359749dde395eacf2eb9dafc86b903ff1477d213", "signature": false, "impliedFormat": 99}, {"version": "158ac44ea9ca9ecb8fd4036e5eb874be58eee082be92b73ef6f4dc9be3654715", "signature": false, "impliedFormat": 99}, {"version": "31f800e9c3607ff0e370bd5a2b73501567dfcf03b7c7c9c9e8927c10a0467efd", "signature": false, "impliedFormat": 99}, {"version": "75624353ffcf91bb2b7911b44075d19a7b9283670f2a78938c17e82e50d1c0f3", "signature": false, "impliedFormat": 99}, {"version": "c43841a8e135fc3a96ae46e5403a46a3ed686ba983f4f0ef142b1f776269147c", "signature": false, "impliedFormat": 99}, {"version": "f54bb4e54d36037ae537835edc7d64caff0e33b34fac0a2c3e035a418258ab62", "signature": false, "impliedFormat": 99}, {"version": "725e63c5518a0ca69dc44c12dc4cde29218e4bfd8088368ec67836f394cfc7a4", "signature": false, "impliedFormat": 99}, {"version": "a0231312762c8f9446ccb79c88227acdd9d2ee4f8cb3a459eda57029562470e5", "signature": false, "impliedFormat": 99}, {"version": "a6c16d7e6060828143259e5ce1ad0228e3a34e2ff2cf35d2300adc78b6fcb130", "signature": false, "impliedFormat": 99}, {"version": "de9ff289e55588add27a015cc023023660d6b8a21da1a64baa237d0f448b2e96", "signature": false, "impliedFormat": 99}, {"version": "43b90372f7b73615b1eca5549101e50835b885b44e862525f12ca22a55456a8b", "signature": false, "impliedFormat": 99}, {"version": "2f7d6f80dd8dd07edff2652926a4b8eeaedafb51775bea7c889afbc795d40b4f", "signature": false, "impliedFormat": 99}, {"version": "1a84b7fc795e6812ce4d63d7066dfd5292bfd2ccf52364b1fed7f599efa896d2", "signature": false, "impliedFormat": 99}, {"version": "9526eb9c30eb925dce99c5debe53d8478267f498fda60faf00b89cd129fcd7dd", "signature": false, "impliedFormat": 99}, {"version": "0528549bceed39a3d94c2bbefde7eab0778460dae5eef4ff71f04fcb8c8ec6f0", "signature": false, "impliedFormat": 99}, {"version": "17d424fb44cd45655049d153d11a71cb236155abb50d605e1d91c3736799004b", "signature": false, "impliedFormat": 99}, {"version": "96ebc724425e9aae600472cd4af10a11b0573a82cecd6c53581bcd235c869b37", "signature": false, "impliedFormat": 99}, {"version": "03ceff4db920e1831332a5a40c2eaf8056f221b9e3e672bc294ebc89537c9ff8", "signature": false, "impliedFormat": 99}, {"version": "ad030e8f3bae5badcd0e18837a3b637bf411c06ba3aa38c9b89bc6e016c67a35", "signature": false, "impliedFormat": 99}, {"version": "e7f31cf8377bd6a1779922371bd84d2427a6df910b3333a93f0c5168299cdece", "signature": false, "impliedFormat": 99}, {"version": "377862d812238033feb16a3174f3eca5449b5786727572fc546cb6f1e973adef", "signature": false, "impliedFormat": 99}, {"version": "e362bee8c7c56dad6c0f52b2d83316ed53c6aca843ccc4c1a88b7e55382e0b52", "signature": false, "impliedFormat": 99}, {"version": "2784077307c50f1342422e95f1a67f5cb9870ea04ad1a80ed4d99e9cec829980", "signature": false, "impliedFormat": 99}, {"version": "eb7e19c5a59896a08776f58b63212ebf6b4c52c24cb6f0574c8ad2e462fc1277", "signature": false, "impliedFormat": 99}, {"version": "c5676e6ff4ed5b0069a3dea05479e3a5abd938dedd4f5ca813f744728066fae8", "signature": false, "impliedFormat": 99}, {"version": "3b30055d700e379329817ad8469e061cfffb79dd0b6e66cdc3cabc5fe03da7d3", "signature": false, "impliedFormat": 99}, {"version": "7944d3987fda085b3b5a9325ec52f998d0172d4138fcdcbbff60e34b562656cc", "signature": false, "impliedFormat": 99}, {"version": "b944764dcffb404b05669dede7b7008e62b21a8f7c0cc1c021294490a99e555f", "signature": false, "impliedFormat": 99}, {"version": "e887a7a29bd7525556302dd1dae062cbc66ceced3565609b59920fe166910086", "signature": false, "impliedFormat": 99}, {"version": "503a8ac885749cc70864c0dfff99302888a41964e4a9fcaf83ab8d01eef3e458", "signature": false, "impliedFormat": 99}, {"version": "015b9884efeea4f3ffbf092e1c1d6eb69ade71d7d79833468e9c18e36545e142", "signature": false, "impliedFormat": 99}, {"version": "8637312eb67001e93cee29113dfcab695b3e12332a5f4d2fba22471d01978b3d", "signature": false, "impliedFormat": 99}, {"version": "8dfeb90bd8f28f690c724ee3c00d2d32ad633884e159fcfb5ce4e82ee5589c5c", "signature": false, "impliedFormat": 99}, {"version": "f21c7e7ba380996bc52cfbd4e23f037edc90b073fc4b34395c4f8167752da7f2", "signature": false, "impliedFormat": 99}, {"version": "f5df5c1a71732a42fdf23542b344d7069a4e0a68adbec151982b93571442b068", "signature": false, "impliedFormat": 99}, {"version": "b532dd989593d814d9bfcb3131b4331de4b35ade064427001676d1fff001ddd9", "signature": false, "impliedFormat": 99}, {"version": "49ebb1610637e76da9500d2def8f15c96c77b1bdc3560091d5d07ceb86c6be70", "signature": false, "impliedFormat": 99}, {"version": "3dad5f9d2442b6a1ee26187724f0a1ebdf9f89b5dff0fb3b8ba1eea11db6d7ba", "signature": false, "impliedFormat": 99}, {"version": "5fca4b593907fc70848e8590d14dba0cf0410e6c061e39c177835e700ad089bf", "signature": false, "impliedFormat": 99}, {"version": "aa76dec64917d5cb480593cd443b229f9ac8c3a983b88962bbc5afd89d0963ef", "signature": false, "impliedFormat": 99}, {"version": "4876014affafb8fe03898c335c396ec29ff29ec8ae3b50ad5ea5ff98c9323c8d", "signature": false, "impliedFormat": 99}, {"version": "255cfcfd791b6f0dfd44f17f8bf6d4dfd733b4a8fec6c15efed8013d794016c2", "signature": false, "impliedFormat": 99}, {"version": "420139e540c3461ff3a03158ba1a1d52e956aaf083c1a4b04069a8482e8978be", "signature": false, "impliedFormat": 99}, {"version": "d15d43b6b19a969858befe90f60009952298120dcaab7110cff78a388a50f7a0", "signature": false, "impliedFormat": 99}, {"version": "0cade822c5888722f9398f9e29781cfccb603d8844cb0273fd4ac8aa9a184193", "signature": false, "impliedFormat": 99}, {"version": "37b5ab7dcd9f3954013a12e1e873953d8be801cc3f97b4e5d9c4dc895d8fc4ac", "signature": false, "impliedFormat": 99}, {"version": "1277bf682a6d071861d20d2df102d950dedc15e49a96f211b1a4d2c87c83a912", "signature": false, "impliedFormat": 99}, {"version": "8cfe0fafb887fb38150159834ac34b3e91d883b250ba4e1154ce88ed057d9fe2", "signature": false, "impliedFormat": 99}, {"version": "ec69be923cb78bb128ea6fbf86555974d0f172a1f65b866d9bbbbc8e4dab82e5", "signature": false, "impliedFormat": 99}, {"version": "da5d2ad94cbe6ead090c5dabeb266eb81a958354e487442dfe8313beb467f99c", "signature": false, "impliedFormat": 99}, {"version": "1656706a594b924adfc45a7e9088c63caafb5c2ba689fce0d757d1ee5f016b17", "signature": false, "impliedFormat": 99}, {"version": "d274837eed0e7d29bfd55aaeb65147107ff57060c70cc977ec83868830fffe51", "signature": false, "impliedFormat": 99}, {"version": "a050ee6f9c5833d18643f86c0618ffe791cc15e7dd758f21738e305749e9b002", "signature": false, "impliedFormat": 99}, {"version": "baa0b19d4b1f69101d22cf17b011d4544343df50572a2ff7a56fa51a1182c299", "signature": false, "impliedFormat": 99}, {"version": "15e6e5a7d194e6a1d4852f2582c0b0f174e805c445cbd758fc9d2279374d5ae5", "signature": false, "impliedFormat": 99}, {"version": "bcaf57053cdd116527f18f99ed70085db39bed9a251510fcd6903e99df6910d2", "signature": false, "impliedFormat": 99}, {"version": "522ff1756b55a8c06ccc949b09b4cafe6fe922fbb1e2d780dc04e992673f6375", "signature": false, "impliedFormat": 99}, {"version": "6c583ae286739f214987efbbc2bc3222870c03a83b8af01fbb4e951c78a19cd6", "signature": false, "impliedFormat": 99}, {"version": "04ea39e4b3e1d6e56bc1f0bd0c7b19aeb4d35b678937b3ad54c63d44b44900c9", "signature": false, "impliedFormat": 99}, {"version": "7a54a284c5fb690b97ce715f0e7d861c3b150765751cb6bffd6c479c8d5b0313", "signature": false, "impliedFormat": 99}, {"version": "65ad93db7608fa525e362be30971ab55076ddae12db11d04a8e3ea4633ba7738", "signature": false, "impliedFormat": 99}, {"version": "d7fbd0ea7793a151d792f6ad7d7c9a9ab7dbc69d970d0d0e57b408cba59ab91c", "signature": false, "impliedFormat": 99}, {"version": "c59df2ff58c6adc907ed95ae1e0ddc2f6a123ca1189926dbafa3fae1fe8f40b5", "signature": false, "impliedFormat": 99}, {"version": "3e85dc80eee865fee0b9aed7bbe2707c38e2b36b0f9192f9202566a9be7c404e", "signature": false, "impliedFormat": 99}, {"version": "717c55229509a89e25c3c3a83a1de364e4db51be5002a738800f76f0ac168868", "signature": false, "impliedFormat": 99}, {"version": "c00bdc82363a765e8720a159a973486e03ec0c25da4d715e02afebd134bd622e", "signature": false, "impliedFormat": 99}, {"version": "e7165093ba33bad2ca7ee2865de7a0e7ca3b0480101c0cb75be7b024167d9e59", "signature": false, "impliedFormat": 99}, {"version": "ec4ec119f797f71ee6d8110930dad93c689a1683484171621a2702b873d8af1f", "signature": false, "impliedFormat": 99}, {"version": "1390e4de40d868b8e1d2619f6d0e95d0524b7ccdbf9a90c660e0b7230bd5ed19", "signature": false, "impliedFormat": 99}, {"version": "707a37c179d6ff79844ffe41d72350c775de3fe1a1e2ce2ff458cda9595cc75e", "signature": false, "impliedFormat": 99}, {"version": "09c6639e5622dc1693276f4c7684b0f0f4992d5c4e5c0769dd576e95c50635f7", "signature": false, "impliedFormat": 99}, {"version": "0af521e519e48440bd69f5683fd26542d478c8110c1bde2815a732ea790d5448", "signature": false, "impliedFormat": 99}, {"version": "af40e667287d9d2e79aec9af683744075a87c85424f518a70230af7aa8825844", "signature": false, "impliedFormat": 99}, {"version": "49062a955da1d4880135873f5c08988c920429c3785349ed1b4e112b9269d8f7", "signature": false, "impliedFormat": 99}, {"version": "334bc494ebf7f62684a30a916455dc63c6895784a74b07b835d28d0297785496", "signature": false, "impliedFormat": 99}, {"version": "de20f1cce0ab86efc45d9d7bdc100999fec7f369613d57cd8d44cdaec8e12958", "signature": false, "impliedFormat": 99}, {"version": "907467198cc07e6eac62f7eb2bcc7afc31e3ee433ae60000eca62213de971e6d", "signature": false, "impliedFormat": 99}, {"version": "4263e62ba6e779cd26752ab3fcfb42249d009efcf110bf7a69412c1f33582e22", "signature": false, "impliedFormat": 99}, {"version": "0afb4e75b4e9dfb1e331b026346fa429c72b3f76c2838ce448b5281b8d89eb9f", "signature": false, "impliedFormat": 99}, {"version": "a723cf11acbb7f1d9b620b90a5cdc50f60f9ac8c2ec7bb6f69751729093180b6", "signature": false, "impliedFormat": 99}, {"version": "019bfea6e0ea6051fe1d51f3d0671fccd704731d54ab218d9a8a42afcde54a41", "signature": false, "impliedFormat": 99}, {"version": "63646b3d3e6071e59c2ae0a3012529910593f6f55b0285c028798b700df1eaad", "signature": false, "impliedFormat": 99}, {"version": "3f854a9e492f56ef132efbc1bdc155896b97618a2c15eb06248bd88478303be2", "signature": false, "impliedFormat": 99}, {"version": "984d0fd8112e3cdde9bc9cf0875f69676cd5a150caabb228cf067741e1241add", "signature": false, "impliedFormat": 99}, {"version": "8235beb430cdab1e2c5244364de7f28ac109b3fac5e3b6def3bc9aa0fb7d1360", "signature": false, "impliedFormat": 99}, {"version": "6b95bc34efdbe1082609ab0a1522f30f4b79a906e479af1295d4aba7fa887f58", "signature": false, "impliedFormat": 99}, {"version": "c81e7a416c0e77487b511c0f345797d6323214968009b52dc8c2aa5c9faf7210", "signature": false, "impliedFormat": 99}, {"version": "f1f7004e9aadb6803b238c03a27971c5e1effdaf1c5d6dd9b3d688767f5563b2", "signature": false, "impliedFormat": 99}, {"version": "0d8ab497f53d6142282bacf32f1538fc607e267e058074286528126fd1c2db6c", "signature": false, "impliedFormat": 99}, {"version": "5b81a34a60401dac6213a45e2bbde3e57060ff06f847cb005337816ff2015189", "signature": false, "impliedFormat": 99}, {"version": "4b64c32b6dfd99fff8c7805de34e90dd20891dcbbb8e8fc406a3222f5c5bf346", "signature": false, "impliedFormat": 99}, {"version": "8ae43e29b6a1b72cec9bd415afd180de9a9d83423c7d7c8f4d61e090f85ad572", "signature": false, "impliedFormat": 99}, {"version": "f8449256f5c820606e9da9e5dcffd574d48981b8b6520c234b15f8a6bc3dfa70", "signature": false, "impliedFormat": 99}, {"version": "a61e72002ae43b8230b720eac472b287c2d6e492adaaeb7546570e1ede58c3ca", "signature": false, "impliedFormat": 99}, {"version": "3de403593b664a953f7b10950653129a6b70e97fbdbcc79ad8292cebd6602274", "signature": false, "impliedFormat": 99}, {"version": "35c011c44b69e88a5798bb61158c26e35ce74df571c095c029b29d182924c2f8", "signature": false, "impliedFormat": 99}, {"version": "14cb4ab32e33b9a279f3b62ef3ae69938583fcdb276b219d74d149e9106b7aeb", "signature": false, "impliedFormat": 99}, {"version": "c9bf49c427e33b552a03b20084624635957dc8468eca2a3d461f0582a011c5b8", "signature": false, "impliedFormat": 99}, {"version": "f4d2c3633596eb54d2bb659bc1c60da3d4157c74c6b6e19f8d27965da2b46bf4", "signature": false, "impliedFormat": 99}, {"version": "4a6091ca49cf40b7933e287a233de2c4666c4ac22c80aab2a0bf4a52b467c743", "signature": false, "impliedFormat": 99}, {"version": "53b2c7304bea0d35da3f158365ecd0794a49cbd8882ff2f7122f99a737854993", "signature": false, "impliedFormat": 99}, {"version": "d51c6abeb24e22093f26441b97eff90378ec9bd13979d0d59f5034a2296ef884", "signature": false, "impliedFormat": 99}, {"version": "6f40ad7380099493513c35be209c0b10a531c4e3bf3acf27d5400d030c59971a", "signature": false, "impliedFormat": 99}, {"version": "d2f0d9d92558f5e5406a561675e6437524bee447f554a8ba6f4dbdd627d0b2e5", "signature": false, "impliedFormat": 99}, {"version": "6a0189edf84211867754d862eebdc7b6f075d156b9301a9bebffd89f51ffb66c", "signature": false, "impliedFormat": 99}, {"version": "ef74f47c63b7a4d7a022c1f569f3ca9c14e3277e0385b037587665d69b96be7d", "signature": false, "impliedFormat": 99}, {"version": "4198bc4505f06500bd9b7db780972b9a301cc946896287e0c9da7d140849ea46", "signature": false, "impliedFormat": 99}, {"version": "6bbd5c8d318ee98ff37777e15fbaf08f1328fe71d72c931f082cb942e0a2cd17", "signature": false, "impliedFormat": 99}, {"version": "b4b440d99a10cbfd6272aac5bfd9aa9622b9c1f9c43f7d5cf79cb43825614958", "signature": false, "impliedFormat": 99}, {"version": "741587fb86739542002fd67fed070c07e34dbfd9bbfde95ca955144b861d00f3", "signature": false, "impliedFormat": 99}, {"version": "91691429b483822699b6c2ecdba19b9fc1ba352693db28fae12092b727400010", "signature": false, "impliedFormat": 99}, {"version": "6989d42d669be40f6591a8fdb8e705df5fec8968a38206f5a0047f47c230d1b2", "signature": false, "impliedFormat": 99}, {"version": "20b1db9c33a81af48e43140a540d51c87b6b20f608489fbbf7486c8f56ef0c87", "signature": false, "impliedFormat": 99}, {"version": "a534aae35e31df8c5dfae7d984612adca9d5641b59b49ead295066dee45b4dfe", "signature": false, "impliedFormat": 99}, {"version": "4960805d11b85af4fcff7d549c97447b2294d67d4ee2bbf00695184d5eb6b21e", "signature": false, "impliedFormat": 99}, {"version": "d0b1cdaa14a443a383bfe147dc579b4a836b73f8dfe2b3289e58e871fcad0bf8", "signature": false, "impliedFormat": 99}, {"version": "2546d813c0fcb88951aeeb0c59d42fcc188ca463a6b64045cc091cbe01737664", "signature": false, "impliedFormat": 99}, {"version": "f03eeb6a19310c90fca912e9d3d618bfe78a590e2386695ac4fb05511e6b9a44", "signature": false, "impliedFormat": 99}, {"version": "fda15a21c72487186d6e08d90b6d2554eda631c7bfa71c8805bde1d409f04c4f", "signature": false, "impliedFormat": 99}, {"version": "aad34743471540dc34740144e1dccc42c9b4a1522a8f60ea6f8bece95f226aa5", "signature": false, "impliedFormat": 99}, {"version": "c4feb5adb299f304513b63720b3caadca698d20eb5f2ba53f540609576399ed4", "signature": false, "impliedFormat": 99}, {"version": "3f6ff7fa12f7ae9e51fb3335767a23feb2042397ff6dd78836ab8380ce06b760", "signature": false, "impliedFormat": 99}, {"version": "e379f2cc178fbdfe06bd7575ed0c3019f06307503753d2e3833fa08cccdf765b", "signature": false, "impliedFormat": 99}, {"version": "05e7d52d0f13fc255dae1568da631c3b31ae36097bf4fa7fafa5d4fc0a902d2f", "signature": false, "impliedFormat": 99}, {"version": "b911ec34b809d0cc9bd3392c04f5fc4b7d29fc43635330ec94ddcb64aad6c32f", "signature": false, "impliedFormat": 99}, {"version": "7411280457182312e059b3e78910089b75f7694645c9caa75e0b2e3fb1e6e9c3", "signature": false, "impliedFormat": 99}, {"version": "035cdb01dc859990cc531611dd6c7bb0144f5c02a911b06e7dfbf3232ee0bc73", "signature": false, "impliedFormat": 99}, {"version": "15f23c7f87961ef45889ccb37db664270db9c7ceb127a4d3938521ed095504d2", "signature": false, "impliedFormat": 99}, {"version": "cce8976bec1dfccb5e48ed58df797a393e3c894397b40986884a173e3ef8fb51", "signature": false, "impliedFormat": 99}, {"version": "d1dfa8127d21751115a0a6ae3e0e0e41f70eabf45e23787ba2d327a14669e518", "signature": false, "impliedFormat": 99}, {"version": "ef87c5b95fbe2151e96c89e6c80ad7dcfa895a7001ea9c0cc258eca3eb84ae49", "signature": false, "impliedFormat": 99}, {"version": "2433129fe6d3d67b8268ba54abd4ab1c7c2f7a32444d4c6a68a9a10be06cc617", "signature": false, "impliedFormat": 99}, {"version": "e969d9b9fd9ca2e023ef701519ccd75e207dd52b92f9af22e15c04fea8e719c4", "signature": false, "impliedFormat": 99}, {"version": "18bdb597e29cc27e765330c5ab04ef4de75a9f019fd8c457f88ed777fef90774", "signature": false, "impliedFormat": 99}, {"version": "dd429b03ce8ba91ab6f204d6c2c7ca00fb3cff07b956da1ac8c60360da28d866", "signature": false, "impliedFormat": 99}, {"version": "b7a63ff548e03c363de65f81f7c31bf98f77b73f13054ece8ee2bc1c1ed9cf6b", "signature": false, "impliedFormat": 99}, {"version": "72a7c47fbcfd19b0814dd7555950d2726f1530daec8f0c98de3107cb6654eee6", "signature": false, "impliedFormat": 99}, {"version": "5f49779e856a15a93dbc55628c6dd22787c4729a6ecd4a3ef0226ce3efa54d6a", "signature": false, "impliedFormat": 99}, {"version": "bb836f3e3bb9cff93ea6cd392b5fcb88aae3d664d7c09171e6ffacc2f0a44759", "signature": false, "impliedFormat": 99}, {"version": "612f919817f17d0a4ab4dc0bb83f1af7b6fd3a810ab8265f3ba247619c90118a", "signature": false, "impliedFormat": 99}, {"version": "02d5344b11cf703ffd698f1874f5298d855ae6a91c3a2d42c3d95b70c2f4e6f7", "signature": false, "impliedFormat": 99}, {"version": "f6a02ec242fe847abb54511123ee93c58ff13d7b660bfb8a01eaf5edc39e8856", "signature": false, "impliedFormat": 99}, {"version": "4ed57726726e281f991b7419a8df5536aa8c1189bac3a0386ff590c8f16b7bc0", "signature": false, "impliedFormat": 99}, {"version": "8ead572121be169161fbafe5293a189110c391b15670753f1be62d6298a316da", "signature": false, "impliedFormat": 99}, {"version": "3801017d48638edbf32c445143b804711d2bc1a2ef51f0dceb25fe8a5b591bd5", "signature": false, "impliedFormat": 99}, {"version": "2d5537810389a683449de9b0896ca4b130b93a339d8d72836649f08cebd17f1d", "signature": false, "impliedFormat": 99}, {"version": "773f4ca58611a16eae2143575c1a01d738de48378dd2d11fc400be42ef2daca3", "signature": false, "impliedFormat": 99}, {"version": "558d19d1b6743e92b564bfbf3edf3501ed8bdb2d090181b4fe5003b884694c38", "signature": false, "impliedFormat": 99}, {"version": "9f74f3a8cb86c7035df458ac1964b046e71d75e156ca30e46b7237ccb5c88352", "signature": false, "impliedFormat": 99}, {"version": "bb4a8d5ccc79c02fd91468a00a6a60094b5faf91c69e510fbc4b84ce1f1a44e9", "signature": false, "impliedFormat": 99}, {"version": "a68d52626a14a314e2f910dc7e279bc087f066e60a78b259c3ab78a4cc1b2e4a", "signature": false, "impliedFormat": 99}, {"version": "c796c30eea1275679550236b6f00139fad4be671f5df058fc908156949d91e32", "signature": false, "impliedFormat": 99}, {"version": "405533464641522eab7fbdc2c249729514750d679d5905a84ad94b790787df9f", "signature": false, "impliedFormat": 99}, {"version": "ee2f8c4790ef349e7777b3faaf599823e82e3e59a4bfc2c67c3e1775d3bee50c", "signature": false, "impliedFormat": 99}, {"version": "8effb19bf88f12addeb45df0c5d05e0f6464612d3d6b34f1da8ca8c2c1c5cc12", "signature": false, "impliedFormat": 99}, {"version": "1e013d9eb6ae0803a2aca856d30da9cfc48c6448500544d8600cd1ef8549d311", "signature": false, "impliedFormat": 99}, {"version": "bec1c0e444418bd6b168ffb15b76b9441c761bb2d243c089fa6ea378b2cc72ef", "signature": false, "impliedFormat": 99}, {"version": "c5a21f137c70fdc46c5d643218989ae7d71199f3d6a30af86441dea65a458d5e", "signature": false, "impliedFormat": 99}, {"version": "5c7d1b8744a3c63cb23db59258fcee28ef638307c6862f51572805162a851b51", "signature": false, "impliedFormat": 99}, {"version": "448a88c8e7eda3d8999b7022cfe4dbd1cf586e71e21e999bdbbcdd436ac58b8d", "signature": false, "impliedFormat": 99}, {"version": "3d0a68c3aeea5142f8eeef68dffad223de6aff452d4ff16d7c41bbe327cd9f05", "signature": false, "impliedFormat": 99}, {"version": "ceec50190a9d3d13a8500a8e1d1b6f8f5a3f6be45dc8e9f983530d84dbd69cd7", "signature": false, "impliedFormat": 99}, {"version": "42b9d795a3152c6bb0f641da28297b91d5424cdbe936952ad18c20f501bed1f0", "signature": false, "impliedFormat": 99}, {"version": "37488fdc6ffd2d40cb049ddab8ba198c8e887dfe77510c6c83efb6de34e2fe68", "signature": false, "impliedFormat": 99}, {"version": "03f6241d183131e3118bc6196e3012eccec7df5a002b995be6ed3ad3bb7c8fd9", "signature": false, "impliedFormat": 99}, {"version": "661b89ea587a659596859486a0123a631c34b5057993284d60ef9b87c015797f", "signature": false, "impliedFormat": 99}, {"version": "0e6f5d456e1b73ad322c4b0bdcf10b0f9a8a0b75414d5b9e00d9f561a43874df", "signature": false, "impliedFormat": 99}, {"version": "56a8fb4c1e654942254ca0e64f667a75eeff9c3d4964ef7e759d03821ef09c94", "signature": false, "impliedFormat": 99}, {"version": "e72931e0fd3c01a2153527880a56b53a2fbbe198421809dc2a7c3a93ea74997f", "signature": false, "impliedFormat": 99}, {"version": "b70eb8f22c1217715e2c34d1a83a75d5fa024c32b1aef4b7c4db3f98645cb395", "signature": false, "impliedFormat": 99}, {"version": "bdf3308ab1c4bea0b7ac8432e5651fd55fbf1591496f0b5dfae649f8b0cbd145", "signature": false, "impliedFormat": 99}, {"version": "3a5b6c07dd61016f03d7d4b9b8714fc10e0ecfb2f358783449a6385b930409fd", "signature": false, "impliedFormat": 99}, {"version": "0b70dc15cd46f0b2f0d705744aa3dc4798b87f5113589ca5e1a7053af8edc756", "signature": false, "impliedFormat": 99}, {"version": "7687d8298fbd5d0859b84ec89fbd43fa591970639447cc7b0156670b2a4740f8", "signature": false, "impliedFormat": 99}, {"version": "ae1fc7ed3c72167972acd4f771883d14dd13d635c3b585606218ea4f9f5662c9", "signature": false, "impliedFormat": 99}, {"version": "69204d6d8f37d8ef16ef681b185c5aafc81d81afd5432a25912560f9909ed2bb", "signature": false, "impliedFormat": 99}, {"version": "3608e6f20899db55d817ab7a76390aea19b8e3bf7cb4becb5f3b70b833db038f", "signature": false, "impliedFormat": 99}, {"version": "434af61f55bf25916aba2d8abcec57ceeef35571daff914fe7b54aba771312c1", "signature": false, "impliedFormat": 99}, {"version": "3f31fbb79cd50033ef517ce3296f511ba8654758609015026227740f4892e187", "signature": false, "impliedFormat": 99}, {"version": "b6cbb9a7507ddfb4658eb5fc04835b24abdb18f9b1dcfc821ea8cb220c6b4a24", "signature": false, "impliedFormat": 99}, {"version": "590a91fe582b89a9bad5b5b4d1a6d9747c5287f6e1b23a2a57d1aa60c1a23180", "signature": false, "impliedFormat": 99}, {"version": "5aa8cb7c1bc385a9938b872f6b857ffd91a17cebe05c86a44f12666a37cdf1ce", "signature": false, "impliedFormat": 99}, {"version": "8867ef533f3a1b2d7e77051ee1c764c1942861544873ffd8773d52005a7b30e1", "signature": false, "impliedFormat": 99}, {"version": "157a1f916813abf3e1faadae34279ee65110d7dc8146711240196ce0e46cbcec", "signature": false, "impliedFormat": 99}, {"version": "7d0101529b77bd85692b2a831308a7534a478c60b95a1798c07e14d3a14e4b21", "signature": false, "impliedFormat": 99}, {"version": "8176d254d2942413a87cdf2cd5aa51932e5b91e33fcea3e0fdb29582005095ce", "signature": false, "impliedFormat": 99}, {"version": "19ea1b64d140b3fb5d1b699b09f1aaa60ebf32014f6dee279b96d92ca662d871", "signature": false, "impliedFormat": 99}, {"version": "b2d2ab3ab26f446cad62cc23ded652641a44deb9d19280550c74cc81c7cd4263", "signature": false, "impliedFormat": 99}, {"version": "1b7f1fee5d0df0a2a9e5c4e0f685561d75fed9820679f0eb1f87757a050b7bf6", "signature": false, "impliedFormat": 99}, {"version": "9afee2d40467087a6aed46b5fef0548c2a1351d533f2aafc68cb47694a81f7c2", "signature": false, "impliedFormat": 99}, {"version": "372c39fd10f96d006497fc2bf9d56d0a602119244ed46d087a2bd5bb037821d9", "signature": false, "impliedFormat": 99}, {"version": "82874ef5e1e686a1edebf547e58083dc1f2ca920100eb4f771d4b1b9ba0851b7", "signature": false, "impliedFormat": 99}, {"version": "d9e8f082189fbcd24d1c13275aaffebaf48c9222d20654d61ad7082f6f2df101", "signature": false, "impliedFormat": 99}, {"version": "8f2350543fe05a8d34952c3dae8f9781594751f5ef130384446a729e3dac7bff", "signature": false, "impliedFormat": 99}, {"version": "fc71808cf3e82c4b815b17870970038be40a83c23ea77a47c88bebd7a8a0d431", "signature": false, "impliedFormat": 99}, {"version": "87622b9b115ff00fdcb1ad2e5c0f6064249dd577cd94140d2429aed76218195d", "signature": false, "impliedFormat": 99}, {"version": "987a12239021ad858813841f22475f2a225d3333a2dfd9beb32222c9e2dc2505", "signature": false, "impliedFormat": 99}, {"version": "ed3f6a7fbdb2e7d6bc2636b3f56c08ed34d2ba80ad3c4d30f03a8b12298ba100", "signature": false, "impliedFormat": 99}, {"version": "097d4c89e60fa539682315762384d83801b9c8bc0f24f57a63d62319b6cb88f6", "signature": false, "impliedFormat": 99}, {"version": "ae868f126890affa478b4628684db9c084b00eaea3ac884ece0184e8f9b4041c", "signature": false, "impliedFormat": 99}, {"version": "0aa2fc9a3936aaed64b486dc8efcbd6c62e0afad81ffd72be408cb97867c0b16", "signature": false, "impliedFormat": 99}, {"version": "ee630d71a65d5026c4f4cb01b95eb5277bc9950c36897a3fe5d01409c312759c", "signature": false, "impliedFormat": 99}, {"version": "1caad517833757199ab3830587bca968433d3e1e485c518989e10a3b77f85b24", "signature": false, "impliedFormat": 99}, {"version": "9087d62992fb955a421851106b0e8c815f3e24120b95c56e8373d384e273e0e5", "signature": false, "impliedFormat": 99}, {"version": "f3c8a9af5feab30aaa5c170548fb0748dc2e7f7ce30aa0050555419bee0c05df", "signature": false, "impliedFormat": 99}, {"version": "ebdb84450ad6efa9a70dbb78f4c0f9a16888bd798eefc37f6cd04d2572206242", "signature": false, "impliedFormat": 99}, {"version": "f93d43b0832bc9f5e6a3ec0358bfee8dc2f44f748278f3e6a073220844e78c78", "signature": false, "impliedFormat": 99}, {"version": "a15b1957c98e891ab28b838335bb1deb557343bb4124a9975df71d3e523a8a46", "signature": false, "impliedFormat": 99}, {"version": "30d463e7ce174f7a529d3a832711f424c984cf517c08f59dbcd2ccd5b16bb6ea", "signature": false, "impliedFormat": 99}, {"version": "6767ab11a8cda8c0ac2ac7e2252bf7be2299410752049237a48d93c62a4a7195", "signature": false, "impliedFormat": 99}, {"version": "556ec31b542b318f82f9fbcbcea81d9c139ab820d4e32df8327b81843dc32234", "signature": false, "impliedFormat": 99}, {"version": "256cde5dd5a4f0ed7516ef587efd4bef006317e8daffc232974fac0efe47ecee", "signature": false, "impliedFormat": 99}, {"version": "53c4229dc8cd2aa22a2c58537514818d429b6972555241f821cd7e1701c42d38", "signature": false, "impliedFormat": 99}, {"version": "dbfcc3a90669180c15e0817815c5a9ac090b9473998ec0bedbfc3dc98fdafe12", "signature": false, "impliedFormat": 99}, {"version": "6745a82126e61c30cb5a8db54d35886159c53ac5a28f5a61d31fee282598f7c2", "signature": false, "impliedFormat": 99}, {"version": "be768a2f53e62d96a980aa56e02861472f7e974862730dd12fa26cb4bc50e348", "signature": false, "impliedFormat": 99}, {"version": "1ba993dfeec6dca5b138bc0370f561e5a220a367b7fc015a935e015ecc865aa4", "signature": false, "impliedFormat": 99}, {"version": "1bc5d66f065f14c9c6290f6fe09492e60d30901737b68a1e344f2d61ed001e96", "signature": false, "impliedFormat": 99}, {"version": "f3a27610d825a99ec583a666eacfb2f5cced7b452d0c3338815b0caa4639ca7e", "signature": false, "impliedFormat": 99}, {"version": "fe896af05f06c4c6257fdc8e8cad8a278c90d4b38ff6b70efc5b5e3ecc880bb4", "signature": false, "impliedFormat": 99}, {"version": "362db1b55e2006226b53ac79a8ddd5a12976bdd4531badad0ddff27b49817de2", "signature": false, "impliedFormat": 99}, {"version": "c3ff132ac57ce2706280f9e145befc0e7ee6060caebb32ff3022e9c154575876", "signature": false, "impliedFormat": 99}, {"version": "8c1e7fe0b90aeba2f3eab5fe6e5fd66e70ddb6cd998a1eda1c5cfdd6336ba94c", "signature": false, "impliedFormat": 99}, {"version": "a0f0701ce0a5be197aa18a41feea179f1e21a2991918ca26320753fd3cbc17d0", "signature": false, "impliedFormat": 99}, {"version": "89af4f75c1f204d678637102d01382e0b8b167e0b213a42a6fab2a64826e815d", "signature": false, "impliedFormat": 99}, {"version": "55eb256718c8258c829c4220a707904a8c4b3838599deace11c7bf72c19a1c12", "signature": false, "impliedFormat": 99}, {"version": "50d2f4d075114bd15852e0ae28244f897e8fb7109fdb4bb980cd0d3071ffa87e", "signature": false, "impliedFormat": 99}, {"version": "fb29fb3a2e3247167f4e699f19b47cbbe02e3137794c48d08ef6140c13a82a13", "signature": false, "impliedFormat": 99}, {"version": "b8b338b2581fe913b51078571e66b93f60e27089753bfcf0124cd0727684571c", "signature": false, "impliedFormat": 99}, {"version": "00287f47a7a9ab63f5e218d1db19923519e6761a3ae2ba9222d2c38a21a4bb35", "signature": false, "impliedFormat": 99}, {"version": "17f1776b27b2c29bebba486721f5d9319dd9b651b6e3be83de3fa216085e948e", "signature": false, "impliedFormat": 99}, {"version": "97fe89bab2cbd68a825b749e69b091cc01cdcbce11ea81dd9292b41a0067fb2c", "signature": false, "impliedFormat": 99}, {"version": "7468715152819058c1a2a27ea8688a7ae51f9800f1273e0815a60b53a0c023ac", "signature": false, "impliedFormat": 99}, {"version": "f253619c22ea40bf7cbe77923e570714f74ba32e33fd3af620a623867d94561f", "signature": false, "impliedFormat": 99}, {"version": "a9615353b037dab7ed7a5ba67807a7daa8c15cd433f627170360135ae30f7913", "signature": false, "impliedFormat": 99}, {"version": "9ddf47eb87c7613d5a5bbb577fe6ce87dd34f2c7681dede0ab9fa1d6bcaa7242", "signature": false, "impliedFormat": 99}, {"version": "57b00b8088284b7178fda7be8f5987d5edcdddfa10bd2f777c9910bbb7ac7e97", "signature": false, "impliedFormat": 99}, {"version": "eeca86e723c4dd548eaf507190e849b925fdc0788734afe84a4e5ad29ea518b6", "signature": false, "impliedFormat": 99}, {"version": "cf03afdf519792b0f8bcc22c984a5521c5d192c3f46b1caee9d645dc02cc076c", "signature": false, "impliedFormat": 99}, {"version": "8ef260aeed7f688a8c40f0a3480e8e4ff4c1406b0afc44544a8d0087c9f80cd2", "signature": false, "impliedFormat": 99}, {"version": "1074bad4ea7a4cd8088f39ebf5169e355510089d28ee7b775ba1ee5ddbd67a2b", "signature": false, "impliedFormat": 99}, {"version": "500265f07d0faf96f8b04ee1c9e0a77a8e5e1ae07b075adf58105c05db2687ac", "signature": false, "impliedFormat": 99}, {"version": "5eafb802b8483ae0fda85920af0802e633178c701f631ad85db80156054a3840", "signature": false, "impliedFormat": 99}, {"version": "d4326b0dc272b46b1ce13fce5b29331a705b1aaaf79c67dcd883fea74c713b81", "signature": false, "impliedFormat": 99}, {"version": "41edc9dcb80ada08b64177bd4405650842e2e17f86f2ba905e5a7395b660c1f6", "signature": false, "impliedFormat": 99}, {"version": "282c37fb44ceeb5bcfcf070f383314a1bc33b1c1f089f682f53e79b0bd90ce7b", "signature": false, "impliedFormat": 99}, {"version": "d702cd1aaf59322d1532b37530fc934e2bed5a875d3239dc1eecd275f8b76734", "signature": false, "impliedFormat": 99}, {"version": "57d5f16d751884e0a2e97ef772d1a24f256dd1b82b35397041d91baa85e4bd93", "signature": false, "impliedFormat": 99}, {"version": "d5851073cd5047ff38938d853a37c2d709d68a74017bd4df1010187f44541fa2", "signature": false, "impliedFormat": 99}, {"version": "2133317393eff9aa9778320a5c251349f5d0a3597715fa33eb08b6aa9c9deea6", "signature": false, "impliedFormat": 99}, {"version": "979fa80f9aa7e1f015e0a019a28baed03f69924db612889d1899b62b4439f8b7", "signature": false, "impliedFormat": 99}, {"version": "67cfa42620d86ad53914cfec05a9d8f90e43fb28fef9323275d25f6dde1d7790", "signature": false, "impliedFormat": 99}, {"version": "ec5c726ce278b542cff27f8c2a507166eefcb9ae2130ba3785b1c7e168a8f2a0", "signature": false, "impliedFormat": 99}, {"version": "08b4120029f17693ae31a695121c2a37fa1b7f98769aeaf4582ec7a7b25bb352", "signature": false, "impliedFormat": 99}, {"version": "cc5354e745ad65d3a07f67586f85565d332db8f83ab6119616d5dcd5e57bc3fe", "signature": false, "impliedFormat": 99}, {"version": "0be25ceb7bdfe3fa2597861b1c579897370ab1c936494ddb68fe55c85a07be73", "signature": false, "impliedFormat": 99}, {"version": "7a1f228faa5fa5b29b96c1ad04293e310a20c22ec1b83b5adbd1ee306625ddb1", "signature": false, "impliedFormat": 99}, {"version": "22d5c827159162dd95e53a3a67e0d84b61f08d549589ce83dc650ba2446e4055", "signature": false, "impliedFormat": 99}, {"version": "57ab97e8e4bfe6a726c44fa4982c63713e21ebaf407c314afd4e48c235ffb96c", "signature": false, "impliedFormat": 99}, {"version": "54ee6720ce787300bf050b24224405696295d9e2f3f42da366a0b62758835451", "signature": false, "impliedFormat": 99}, {"version": "af25c46e77f36f675d5bff643ca3b984304a46e7cfdf10f4531c0ad003299946", "signature": false, "impliedFormat": 99}, {"version": "fcd0755cfd48a03797014183580db6d6caa4f6b2c06b5eae2501e45754457deb", "signature": false, "impliedFormat": 99}, {"version": "49f2593f18dd90981d30b5d2712bfdf56318c3456f3776a83b23b120b8d0c065", "signature": false, "impliedFormat": 99}, {"version": "e6fbb74c785dade2e68168cfd141a4accab9c9ac5f3be344b8d116ae533cb7ff", "signature": false, "impliedFormat": 99}, {"version": "83eb2cbb1913c3adb9cbf391eacac9bb6ea2627737e4a3c0350d78bc8e1c040a", "signature": false, "impliedFormat": 99}, {"version": "7d206c70ec9860ce9d65dede8bcf731fe3828b34a566afe01000f0e8e0324b94", "signature": false, "impliedFormat": 99}, {"version": "697929cc709ce1a14bfa22637796c90de5a7deac1afc32d703aed10cd148230b", "signature": false, "impliedFormat": 99}, {"version": "a96c285e78d88334d074cc966ceadc5ed67608dfac9c6626a0f800288b692ccc", "signature": false, "impliedFormat": 99}, {"version": "c2bff621d611a1cc7e0cbf6f8bb2e5fd99930b159d80bfc721bd6e2f3ac1af50", "signature": false, "impliedFormat": 99}, {"version": "56e9483c87ffd60f3811152a21d9704384c6539b13fef717ddbf99c5d944c330", "signature": false, "impliedFormat": 99}, {"version": "5c06912ea08265c5b0b46e34ccb3c2082cd608bce26e80d9d810af2cc47fc990", "signature": false, "impliedFormat": 99}, {"version": "32f816bc6d64a56503bb2398846ba92f6e058d93a57ca8dba27790b8214fc88c", "signature": false, "impliedFormat": 99}, {"version": "99c9b803342e29e16248f6d03fccbc88f202c57852c4ef2f8f37407965cfbb6a", "signature": false, "impliedFormat": 99}, {"version": "9057244241137ab9d0f8e7b2419d26d6b5794c063ff2a390047ab733e17a84f6", "signature": false, "impliedFormat": 99}, {"version": "68a5d0c31d7f136af350c10d778043fabe5c94407495d9417fdf8e543ac277de", "signature": false, "impliedFormat": 99}, {"version": "afe62de8880caa0ca0cf59e8bb37d93f6d4d19d7ee887ec9b88cc5b79c2e2cad", "signature": false, "impliedFormat": 99}, {"version": "0c46d7c267ba59b302512de340f4c92b97764eafd086c5b13477fedfa953385d", "signature": false, "impliedFormat": 99}, {"version": "0f2e941fbb7fa25b52f407745686b2e905ec03225af1de5285dc8113cf9f38cc", "signature": false, "impliedFormat": 99}, {"version": "a12f3295a92f365c2919a9b128984c35486282b7de8f3ff81fc360b8f137aaa5", "signature": false, "impliedFormat": 99}, {"version": "80b3f9c2b731626233662c38a5c4ca60a1ae28775a031d59b105672ef1a3f934", "signature": false, "impliedFormat": 99}, {"version": "c326bb72f933aa18f366a29a27dfd4193749c4c077b0464bb31054134a84aa8b", "signature": false, "impliedFormat": 99}, {"version": "0222992caad46191f90e9a5987e0c92ca95c5bb631f8f953e4c92b700411321e", "signature": false, "impliedFormat": 99}, {"version": "fbb281974839d3fcc1fc0eb70b71f68688d9d2e3c719f7956f02ada2d03b0e2a", "signature": false, "impliedFormat": 99}, {"version": "f9c21a69d044828e19f2b9e202b4fb1a1de1927fdd7e7ff0c40d4f63ebcc9b42", "signature": false, "impliedFormat": 99}, {"version": "a23673b34f0d0b6d6925f8db11c21d27c98bcac58faef3e0e20805057737ed33", "signature": false}, {"version": "f697e4b5b03e13d98a4cd6fe8f51bd604022498463f8c4fca532213a439d58ea", "signature": false}, {"version": "3300e952b9da8ba9f9519fb895a3f5955dd3c7c4d68a884975ee21cc34696daa", "signature": false}, {"version": "bfd3e10679e684630d00e3dcfccdb7e7db827a3f3cd850757761afd1ac46204e", "signature": false}, {"version": "fe93c474ab38ac02e30e3af073412b4f92b740152cf3a751fdaee8cbea982341", "signature": false, "impliedFormat": 1}, {"version": "aa4feed67c9af19fa98fe02a12f424def3cdc41146fb87b8d8dab077ad9ceb3c", "signature": false, "impliedFormat": 1}, {"version": "1e00b8bf9e3766c958218cd6144ffe08418286f89ff44ba5a2cc830c03dd22c7", "signature": false, "impliedFormat": 1}, {"version": "7153c5dcbb126b34f10026c210296c3abf0fb76e93522c46a6f5ee461578b698", "signature": false}, {"version": "869ce09892a78746f32233bb8756ce9165b2dba06b75a8d87875f3f07a27f74d", "signature": false}, {"version": "77d009a99299a61593d1632c6b9458d7f9117c265cbee945f07dc179fbf460c8", "signature": false}, {"version": "351b19f3973c1a59e32bba706c5ba46d553af14e8b03556c1dd4e0bf28ef71a2", "signature": false}, {"version": "7b7cd790864f0d53cb83ba8da634ce5bb0279d29d970597d71b6e99da62524f1", "signature": false}, {"version": "d8b101ed42b4f2671bdc1ff26196935f4dbe0ad792350f11379437fec99fb914", "signature": false}, {"version": "82bb35b3a719230d8327e40317bf4c1568830caee4048763b74995943076a7ce", "signature": false}, {"version": "cc260bc725560b577c02e2c7dae8099d0203ea668cf900c72343bbfb8f786ca0", "signature": false}, {"version": "7b6e3aa99835ed096685a6ea7d6d47e46b1a0111001cd09fe131743700af834e", "signature": false}, {"version": "f04d0838ee80e780fb45f8f2da44b1d2944f0ceee511bebb3a879ed9d51b5a72", "signature": false}, {"version": "91aff288a7d5ce60a36865f4fd3fe3593befe72699c41d971853d8e66416574a", "signature": false}, {"version": "0c13edb8be44a43b639d68b50668a585efe452efd8e7a09c61c6a14e548566e6", "signature": false}, {"version": "b98851d9d1a2e7f81dde35f44b044ce944ae6f04337fb56af4791392a5c15f8a", "signature": false}, {"version": "86da37dcae68155c85368c75aa1440ef2782f71d9272a58e0d0ea0afc4eea948", "signature": false}, {"version": "b491fa3345ff8291057a93d2577e1406a0143995302fef52a4ec625767c4507d", "signature": false}, {"version": "38fa304464a9291c969c798daa3e3053cebeafb7c75b0519d14739b3860c81ee", "signature": false}, {"version": "91b4ce96f6ad631a0a6920eb0ab928159ff01a439ae0e266ecdc9ea83126a195", "signature": false, "impliedFormat": 1}, {"version": "88efe27bebddb62da9655a9f093e0c27719647e96747f16650489dc9671075d6", "signature": false, "impliedFormat": 1}, {"version": "e348f128032c4807ad9359a1fff29fcbc5f551c81be807bfa86db5a45649b7ba", "signature": false, "impliedFormat": 1}, {"version": "8ee6b07974528da39b7835556e12dd3198c0a13e4a9de321217cd2044f3de22e", "signature": false, "impliedFormat": 1}, {"version": "5e1d8a07714f909beaaaf4d0ffe507345a99f2db967493dd8ebbfbb4f18e83ca", "signature": false, "impliedFormat": 1}, {"version": "5f12132800d430adbe59b49c2c0354d85a71ada7d756e34250a655baa8ad4ae5", "signature": false, "impliedFormat": 1}, {"version": "1996d1cd7d585a8359a35878f67abdd73cc35b1f675c9c6b147b202fdd8dfc3f", "signature": false, "impliedFormat": 1}, {"version": "5a50dbfc042633fdb558e53b30b0a005e0b78e142a1fe1147a8d6618ca69ec99", "signature": false, "impliedFormat": 1}, {"version": "7d1fd5b1f5f9a463fbd2088e81b1a97571a942448e5dc292021f7c89b1b1135c", "signature": false, "impliedFormat": 1}, {"version": "6fb55bb881f4a7167649e6925df076f64a1db2f50632df4674e4621a9445c954", "signature": false, "impliedFormat": 1}, {"version": "4374cefdde5c6e9bad52b0436e887b8325b8f407c12035194ad02c28f1553a3a", "signature": false, "impliedFormat": 1}, {"version": "9b70cad270593f676aecfe4d1611dc766464f0b8138527b0ebbf1ff773578d69", "signature": false, "impliedFormat": 1}, {"version": "b4f85bfb7e831703ac81737361842f1ae4d924b42c5d1af2bff93cca521de4d1", "signature": false, "impliedFormat": 1}, {"version": "5fea76008a2d537ca09d569ffae4e08b991b4a5ff90e9f4783bc983584454ede", "signature": false, "impliedFormat": 1}, {"version": "21575cdeaca6a2c2a0beb8c2ecbc981d9deb95f879f82dc7d6e325fe8737b5ba", "signature": false, "impliedFormat": 1}, {"version": "40ec58f0fadd0b3981b3d383e1c12fa0680115ae9f018387fc2cfc0bbcf23204", "signature": false, "impliedFormat": 1}, {"version": "849b9e7283b7309a4556c9b90bb8e2dfc27751f157798065bbc513dcddb09a8c", "signature": false, "impliedFormat": 1}, {"version": "10e109212c7be8a9f66e988e5d6c2a8900c9d14bf6beadf5fa70d32ada3425cf", "signature": false, "impliedFormat": 1}, {"version": "2b821aeb31e690092f8eae671dd961a9d0fd598ff4883ce0a600c90e9e8fa716", "signature": false, "impliedFormat": 1}, {"version": "26602933b613e4df3868a6c82e14fffa2393a08531cb333ed27b151923462981", "signature": false, "impliedFormat": 1}, {"version": "f57a588d8f6b3ce5c8b494f2dc759a8885eaee18e80a4952df47de45403fedbe", "signature": false, "impliedFormat": 1}, {"version": "34735727b3fe7a0ed0651a0f88d06449163d1989a2b2de7f047473adc7c1c383", "signature": false, "impliedFormat": 1}, {"version": "a5b13abc88ab3186e713c445e59e2f6eee20c6167943517bc2f56985d89b8c55", "signature": false, "impliedFormat": 1}, {"version": "3844b45a774bafe226260cf0772376dce72121ebb801d03902c70a7f11da832b", "signature": false, "impliedFormat": 1}, {"version": "7ae65fe95b18205e241e6695cb2c61c0828d660aca7d08f68781b439a800e6b8", "signature": false, "impliedFormat": 1}, {"version": "4e28cc749981da4c24922104abd9a8f94261d0e25281df675e7c0c032f6f79aa", "signature": false, "impliedFormat": 1}, {"version": "369b7270eeeb37982203b2cb18c7302947b89bf5818c1d3d2e95a0418f02b74e", "signature": false, "impliedFormat": 1}, {"version": "94f95d223e2783b0aef4d15d7f6990a6a550fe17d099c501395f690337f7105e", "signature": false, "impliedFormat": 1}, {"version": "039bd8d1e0d151570b66e75ee152877fb0e2f42eca43718632ac195e6884be34", "signature": false, "impliedFormat": 1}, {"version": "89fb1e22c3c98cbb86dc3e5949012bdae217f2b5d768a2cc74e1c4b413c25ad2", "signature": false, "impliedFormat": 1}, {"version": "d3cfde44f8089768ebb08098c96d01ca260b88bccf238d55eee93f1c620ff5a5", "signature": false, "impliedFormat": 1}, {"version": "293eadad9dead44c6fd1db6de552663c33f215c55a1bfa2802a1bceed88ff0ec", "signature": false, "impliedFormat": 1}, {"version": "54f6ec6ea75acea6eb23635617252d249145edbc7bcd9d53f2d70280d2aef953", "signature": false, "impliedFormat": 1}, {"version": "c25ce98cca43a3bfa885862044be0d59557be4ecd06989b2001a83dcf69620fd", "signature": false, "impliedFormat": 1}, {"version": "8e71e53b02c152a38af6aec45e288cc65bede077b92b9b43b3cb54a37978bb33", "signature": false, "impliedFormat": 1}, {"version": "754a9396b14ca3a4241591afb4edc644b293ccc8a3397f49be4dfd520c08acb3", "signature": false, "impliedFormat": 1}, {"version": "f672c876c1a04a223cf2023b3d91e8a52bb1544c576b81bf64a8fec82be9969c", "signature": false, "impliedFormat": 1}, {"version": "e4b03ddcf8563b1c0aee782a185286ed85a255ce8a30df8453aade2188bbc904", "signature": false, "impliedFormat": 1}, {"version": "de2316e90fc6d379d83002f04ad9698bc1e5285b4d52779778f454dd12ce9f44", "signature": false, "impliedFormat": 1}, {"version": "25b3f581e12ede11e5739f57a86e8668fbc0124f6649506def306cad2c59d262", "signature": false, "impliedFormat": 1}, {"version": "2da997a01a6aa5c5c09de5d28f0f4407b597c5e1aecfd32f1815809c532650a2", "signature": false, "impliedFormat": 1}, {"version": "5d26d2e47e2352def36f89a3e8bf8581da22b7f857e07ef3114cd52cf4813445", "signature": false, "impliedFormat": 1}, {"version": "3db2efd285e7328d8014b54a7fce3f4861ebcdc655df40517092ed0050983617", "signature": false, "impliedFormat": 1}, {"version": "d5d39a24c759df40480a4bfc0daffd364489702fdbcbdfc1711cde34f8739995", "signature": false, "impliedFormat": 1}, {"version": "309ebd217636d68cf8784cbc3272c16fb94fb8e969e18b6fe88c35200340aef1", "signature": false, "impliedFormat": 1}, {"version": "6e4fde24e4d82d79eaff2daa7f5dffa79ba53de2a6b8aef76c178a5a370764bb", "signature": false, "impliedFormat": 1}, {"version": "ef9b6279acc69002a779d0172916ef22e8be5de2d2469ff2f4bb019a21e89de2", "signature": false, "impliedFormat": 1}, {"version": "12b8d97a20b0fb267b69c4a6be0dfad7c88851d2dcab6150aa4218f40efa45f4", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0e86102dbab93227b2702cba0ba06cb638961394577dc28cd5b856f0184c3156", "signature": false, "impliedFormat": 1}, {"version": "6c859096094c744d2dd7b733189293a5b2af535e15f7794e69a3b4288b70dcfc", "signature": false, "impliedFormat": 1}, {"version": "915d51e1bcd9b06ab8c922360b3f74ffe70c2ab6264f759f2b3e5f4130df0149", "signature": false, "impliedFormat": 1}, {"version": "716a022c6d311c8367d830d2839fe017699564de2d0f5446b4a6f3f022a5c0c6", "signature": false, "impliedFormat": 1}, {"version": "c939cb12cb000b4ec9c3eca3fe7dee1fe373ccb801237631d9252bad10206d61", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "3b25e966fd93475d8ca2834194ea78321d741a21ca9d1f606b25ec99c1bbc29a", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "3b25e966fd93475d8ca2834194ea78321d741a21ca9d1f606b25ec99c1bbc29a", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "7ceb8bc679a90951354f89379bc37228e7cf87b753069cd7b62310d5cbbe1f11", "signature": false, "impliedFormat": 1}, {"version": "92d777bf731e4062397081e864fbc384054934ab64af7723dfbf1df21824db31", "signature": false, "impliedFormat": 1}, {"version": "ee415a173162328db8ab33496db05790b7d6b4a48272ff4a6c35cf9540ac3a60", "signature": false, "impliedFormat": 1}, {"version": "80e653fbbec818eecfe95d182dc65a1d107b343d970159a71922ac4491caa0af", "signature": false, "impliedFormat": 1}, {"version": "f978b1b63ad690ff2a8f16d6f784acaa0ba0f4bcfc64211d79a2704de34f5913", "signature": false, "impliedFormat": 1}, {"version": "00c7c66bbd6675c5bc24b58bac2f9cbdeb9f619b295813cabf780c08034cfaba", "signature": false, "impliedFormat": 1}, {"version": "9078205849121a5d37a642949d687565498da922508eacb0e5a0c3de427f0ae5", "signature": false, "impliedFormat": 1}, {"version": "0ce71e5ee7c489209494c14028e351ccb1ffe455187d98a889f8e07ae2458ef7", "signature": false, "impliedFormat": 1}, {"version": "f5c8f2ef9603893e25ed86c7112cd2cc60d53e5387b9146c904bce3e707c55de", "signature": false, "impliedFormat": 1}, {"version": "8e6427dd1a4321b0857499739c641b98657ea6dc7cc9a02c9b2c25a845c3c8e6", "signature": false, "impliedFormat": 1}, {"version": "58da08d1fe876c79c47dcf88be37c5c3fab55d97b34c8c09a666599a2191208d", "signature": false, "impliedFormat": 1}, {"version": "d928196c731d0532828f048957968de24f4286b89e36e956a4cc731b02acdea6", "signature": false}, {"version": "caa8fe1141721b3bab79364471425952184458a1602013c345a5fb6ad6bd1cc8", "signature": false}, {"version": "9f128da33c4d160ca5fdea4fd456d9199d952f6854191e55882fcc1211b2f0b2", "signature": false}, {"version": "ac38e22d819e10020d83ea27c5c21db50ab5abc3537da44c8fe51236733ad323", "signature": false}, {"version": "0cd694f823b9757963675dba872809e62e05dc582bf225f318975600342e7a0a", "signature": false}, {"version": "e2794fccfbb6251047da7d8821b56f3c0ef5dfade15de5c8c7b6c6d17c58584d", "signature": false}, {"version": "c652e3653150b8ee84ffc9034860d9183e6b4c34be28e3ba41b34b1417941982", "signature": false, "impliedFormat": 99}, {"version": "eee0f5492d6584224fb6465b635e6fcde743565c130aba0d830dfd447d5e42b8", "signature": false, "impliedFormat": 1}, {"version": "37034508bd671c33317aae9253aba288809f1511a2a844f0508365bdc0d3dde8", "signature": false}, {"version": "6db04637232a056d65c8fea81ed17094a6ea0baf9af1dec2fb02c75d72fb7f48", "signature": false}, {"version": "d3f6b10548f8ae52b2a05ecafee7335780cdbcf76a8eb9f26f8b375d640959fb", "signature": false}, {"version": "56e1b5470f0524844c5bf9d0c44e08cc96c2ad4794b861fd10b3b935d9da0278", "signature": false}, {"version": "ba02594f2f5d344dc75863c28da8cb39dea13bc702ac8cbfda9a0dbf04f35c0d", "signature": false}, {"version": "58505e8a77a12fa02667b48e2e181b74cf96575892c32e8c1af5ea279395fe75", "signature": false}, {"version": "6452b9a39f8ad465425edf068b8c471f37450546eb49b3d8567fc7b708b79416", "signature": false}, {"version": "242fd632258df0bf3bca57018aa666f33fed48535a28d1528ddbd3d7e4e5450a", "signature": false}, {"version": "135a7c56d37637ec48dfa6bb379a1e71384a9ac2076e310bb42248e8dd4d5954", "signature": false}, {"version": "91dfc3a17c98991b2fb4ab22ea098c1678796dc6e626e00cb56ff9793b349316", "signature": false}, {"version": "c20a4e686a7e27d5444ef36c1f86c305910192062e8b29c94e4dd977951fc209", "signature": false}, {"version": "b17eb07a76115d486ff3e6ea732e8f8368c4c8b399ee52a523179dcf058a375e", "signature": false}, {"version": "d30e67059f5c545c5f8f0cc328a36d2e03b8c4a091b4301bc1d6afb2b1491a3a", "signature": false, "impliedFormat": 1}, {"version": "210469f9985f125b0725d46b0c97a3cf367904463c138030f4c86217510316e9", "signature": false, "impliedFormat": 1}, {"version": "f77db1be13db8dcf8c7a268c1f235c1ba1e4707e829175cdc237421b3c346f9d", "signature": false, "impliedFormat": 99}, {"version": "05cb95655ecadfd1230547b84367d1c4d40a4bb6309545b8dc4d1638a3de1a50", "signature": false, "impliedFormat": 99}, {"version": "e980a9cf8404a649ff3a2f0628d76e4469613c02b98bd3b922756d7d962476c9", "signature": false, "impliedFormat": 99}, {"version": "f8adbcb59256526bc69d94fb5c826e812ebd1321b30ab35baa9997d74d45dd73", "signature": false, "impliedFormat": 99}, {"version": "050a4570de5ad6e47cc9ac9fd9db7a26e57dbe1daadadbc19b20567941f8bf1a", "signature": false, "impliedFormat": 99}, {"version": "5ed040255a4d5181f8ecb4ba90b8b38e0a6f1becf0ed860ca75b6e52c46db0bc", "signature": false, "impliedFormat": 99}, {"version": "e22a49cd604cab3b62b1968a363d7b182edcb23d46793ed12cf5cfc6b1597f39", "signature": false, "impliedFormat": 99}, {"version": "ff1b4730f5d49d37b73ee2db3443145daa0bfc7ff9c865134d871b08955e389b", "signature": false, "impliedFormat": 99}, {"version": "8e64b72fa289b7f133b8cdb7d837f73e30ca7eb76ad88e1020d97c405c94fd7e", "signature": false, "impliedFormat": 99}, {"version": "1f907507e41cc3df66b4521b80134bb8f7afada8d31c10f7100c93c90ab0f84e", "signature": false, "impliedFormat": 99}, {"version": "6bb14070b70b4c9a897a4f5088af984e6e316b420d00d82fb962bad577896723", "signature": false, "impliedFormat": 99}, {"version": "46e17953f7ffbf43d4328fcb5983e0ade2932fb56e84181e6929fcdcfa7c7aa6", "signature": false, "impliedFormat": 99}, {"version": "113aef5576cd65f310927b17ae5f6ac8745c542a660bace5f019034d536fbd04", "signature": false, "impliedFormat": 99}, {"version": "ddf0fdbb010c94978c1151441171f0aac236a23b6786e9f6332f745527d905e9", "signature": false, "impliedFormat": 99}, {"version": "a2c1678ec68c42795e2ac068a7d026b61680357d2a881c9df211dd0f83d077fd", "signature": false, "impliedFormat": 99}, {"version": "2fe207d2e8662abb709772fff1f3ec3116a4787b5caa4e862daa5dab2753edd7", "signature": false, "impliedFormat": 99}, {"version": "a7af5f01007f450dc8cf2cdbbb11f4d4bf8bf3faa869d21267db5de74ebf665a", "signature": false, "impliedFormat": 99}, {"version": "709cb4986cbe2b58ac3bbbad45dbfa24cda1b62c794c73b96e9ff1236dd0d5d1", "signature": false, "impliedFormat": 99}, {"version": "afdc9b1fd1937d9b649bca2b377d1144cc9c48158403c17cfd21b6e1e8b25099", "signature": false, "impliedFormat": 99}, {"version": "1d47324801b498d62f31ea179f58e1f3eaa1e607914504a7c92fb5465affb851", "signature": false, "impliedFormat": 99}, {"version": "95fdf978302838125ac79d9d5e9485d8fa1ddd909664bf5cc3b45ec31f794fda", "signature": false, "impliedFormat": 99}, {"version": "d92bf7d6d30c85e53b961236ceeb099e73a1a874849d038a348b51383087872f", "signature": false, "impliedFormat": 99}, {"version": "e56e4a57ca5aa762d67fd3d16471c47592469944315fa5e92b3b09c83eabae91", "signature": false, "impliedFormat": 99}, {"version": "f3d31927b7a3d0f2f119a05a102af2bdd1fc4f759fe43d508a64a80b3b341f6b", "signature": false, "impliedFormat": 99}, {"version": "678700fba88589e28648a923e4b98ab60f3f7df4742412419e29f95966da4475", "signature": false, "impliedFormat": 99}, {"version": "5a71b307074ef3d2794c4104248b7a3cad5f486df204da65862a7d24f698fc95", "signature": false, "impliedFormat": 99}, {"version": "9a4496ad6d48bc801a122c11e94ee1e3f0710bda38b125573f67f5cb0add1733", "signature": false, "impliedFormat": 99}, {"version": "afa5e16f2ad07d847701e3bde9e7ab36f87e0e3a5c0cb7998644791a1fa3c5b1", "signature": false, "impliedFormat": 99}, {"version": "98cd9124b5d8438db4b4dbd247b2c68ac22b6366a43e6dc4945ae32972f157fc", "signature": false, "impliedFormat": 99}, {"version": "dc21879e45f3a023b5fe459c3da5f2f3cf995f21a1ac533049d8950ce394c045", "signature": false, "impliedFormat": 99}, {"version": "622d6ce66ac838d5d7e968daf4ae760cf49797e3fbfaa2b21d01e0fb5d625bc9", "signature": false, "impliedFormat": 99}, {"version": "ecfa30418b2200ba6496b5f59b4c09a95cce9ea37c1daaf5a5db9bb306ee038f", "signature": false, "impliedFormat": 99}, {"version": "3b56e30b1cbe1bfa7710e88e5e0b8fa6eddc7c2e67615f73bdf8637af68403e6", "signature": false, "impliedFormat": 99}, {"version": "92a8de4f8f6595bf1eb24a19aebff7371c66ae8751f2e045edd9e25ca435e4a2", "signature": false, "impliedFormat": 99}, {"version": "01810afb0ed31afdea0846cee91e85a474727d0966e5bb57c2a4a732854deab1", "signature": false, "impliedFormat": 99}, {"version": "c874e98cd875727ea62fdcd978ac9e067ce07cf7493aa4b8b193fdc3b7318eea", "signature": false, "impliedFormat": 99}, {"version": "455e843c1f8e0df452f101c9ec0b63ab8e749f296c947249f8bbc29bff58c83c", "signature": false, "impliedFormat": 99}, {"version": "eadc4c556b494cc52676e084eadf0b60fb2cc6e2408d1411eeae5cb74068ca86", "signature": false, "impliedFormat": 99}, {"version": "3b8689266e8fb628ca2068ff610ed0b842ff4e407c3a914358ef1895dabfcfcd", "signature": false, "impliedFormat": 99}, {"version": "fc741907f6d8158b2c4722932d745b11dd41f9355a5b325c8cd3cdfbd966d76d", "signature": false, "impliedFormat": 99}, {"version": "ddb29a7337f7db32ce4b4e46e8759a7049b1a1587c8fe26492eb4df74e6dea42", "signature": false}, {"version": "a6626ef0db1d0efce14d287d7e36c5d975a27bfbcf5dfc1609e67b56d18bab29", "signature": false}, {"version": "6207fc4ac6e824713739461ce8e295ca2fea23ee6758f39b885e38a4b404b718", "signature": false}, {"version": "f47405df875c02c5bdfd094e5ad2bdc43da05de695ce2d53aaf3cecd5f1dfe1e", "signature": false}, {"version": "5034136fb33c03e4f0e8ca11026a74b659c07319b09073322c73be630a71c744", "signature": false}, {"version": "a02c64e6c1ff0da3700ff6f1824e335bbc271a4742d4da4a52014ae710da56b3", "signature": false}, {"version": "fb9e309b8f569bf93ba026ff5eae847d3fb9c61aa55811f4999aff19972215db", "signature": false}, {"version": "7b6f2f998842d9a6293830015b9b796f57a4a77113d5cc223ff4be8f27d56013", "signature": false}, {"version": "2e13a2e09bf4feec329948a0547d633850787d1963c1bf14932bced84a1caafa", "signature": false}, {"version": "54ab5763dcf81f1998eacd746d22b26a9d1dfb0887d16acad715a68c922b075e", "signature": false}, {"version": "fbf09f1ff7ad76013150b5716ec263951e8af05e82b0f5f33dc843a61bcb59ed", "signature": false}, {"version": "f695234637e722fcb7b6d5824cf7d921453dd0fe64c2f8f02ddc0b4a08194fdb", "signature": false}, {"version": "cb78b45f485fb686d8de18922c9169465982bffbb165b09fd03e88bd5cb16f10", "signature": false}, {"version": "7e4eda1d3837bae07d78f105bdc72e648555bcffb6c33e9912392b221b45fa39", "signature": false}, {"version": "2266999682ca8eb1e9c214c4f641498eb5e0aba9fc49333423829a3f83629b48", "signature": false}, {"version": "16b5a66d29d5450789ec2c4aa7d3c1d92645fe287c599bd979e179b89126e5cf", "signature": false}, {"version": "0adbe2890ae5f928dd959f193940d941f0aaf3efd1e098585663fecb8cda63ce", "signature": false}, {"version": "c8da1186922c28d79cb6c477690cc758a59b7470db9a4c6b9cc6a5ae2a604266", "signature": false}, {"version": "15e668083a06b067208f86cacbbdf8d8fd08c8f633e1d6d6a3a336521f9c8e6f", "signature": false}, {"version": "631ac999ce30acdba30215f1132d0af4b35c4960e5661041547f30cf7c67167a", "signature": false}, {"version": "5551dcad71db887df007404cdf407596c8857ec9da7e9a41e875c13437bd3a9a", "signature": false}, {"version": "151838d3a830d61dbb060fd0f248767ed76358a16f0a26c872474fc4e42eb91e", "signature": false}, {"version": "e2e6dfe7721c742bb732e759fa09f0cd28c4aa4d74685adb29f91fc8b92ca6d5", "signature": false}, {"version": "f4fe958d7c251f4d1e06b004a58e9eda32947ad35eec079505a9350193783770", "signature": false}, {"version": "eed3e9b44eefafeeadd059a0b8c708501f172b026d651e9bdf88e34e01b31632", "signature": false}, {"version": "6f33e56c54573a2d8ff814e9e6389f1896768dcd8e32ef7c195a0d2ad0ceb9d7", "signature": false}, {"version": "2612ec16c9c63d28cdc2fe87247aeef222d0926a7d80d9568030452618a6e125", "signature": false}, {"version": "a4e17cfa7a762ab88da3468e85dbae057a2af30fc073bc0f71e073864a3a576c", "signature": false}, {"version": "5f8512650aff5c931e37d2c434baf065ac64c9b35d5ca5e0a24bc88de4c6fecb", "signature": false}, {"version": "448a3f5def78d7654ed48e217408d21a3b947e901996f910c00fe384cac624de", "signature": false}, {"version": "52bbe47a3d860596dab651901d8dc3bc9bc56344e587e1a9129cd38d10439715", "signature": false}, {"version": "fb8146b6acf32944b11406bd9a89f56611c485112d10306e66503fb9bb9d64b3", "signature": false}, {"version": "3d342f8a2240029e2f317a3bc8c3aaa45c2624b547d9196bd5f42292e7d6e7d0", "signature": false}, {"version": "14e50b8a95a90a896228bb5ac8604f5125d3381854e0ba1d12072cd5d0126a97", "signature": false}, {"version": "f56b479a1b2f79274a325ca6a3fcc89c97f2a9ab884bd018d5ed23d0883dfd0f", "signature": false}, {"version": "8ec1d0d1b1d148b841edb983090065a52c9c1b137f3cbb1226de9cbbe401b9f7", "signature": false}, {"version": "4f5965c09578fd6353d149c932ffef9e77eb899825a431cc596cebdd16adc2f2", "signature": false}, {"version": "6569cdabf6aea25c256f6892988e4ea293918f57639a4ef69293bd229f230cdc", "signature": false}, {"version": "8c98fc6d9e69c19a58c179dbac24624b3320f7b9cde4c4907a8681313bb315ec", "signature": false}, {"version": "d8d96f2ee043a692dae23729482cc3d4d5037f23a222410d7f56186460a81cfa", "signature": false}, {"version": "95e620f3e66f27b20232f3860b170e97b709559d73db5f0c003578184666fdf1", "signature": false}, {"version": "2f9be8077c8e634d3a758aef871a28b029f7e23b63e0409d0f2edbb177d7322b", "signature": false}, {"version": "3c308356c6a7d3fb2cafd0978248fcfa44b4cf0c00bb2673664c29549d22d374", "signature": false}, {"version": "c8e857b6224783e90301f09988fb3c237fe24f4ebf04778d0cbe8147a26fffe7", "signature": false, "impliedFormat": 1}, {"version": "df33f22efcbdd885a1ea377b014e0c1dfbe2e42d184d85b26ea38db8ee7834c4", "signature": false, "impliedFormat": 1}, {"version": "f400febd2140549f95c47b2b9a45841c495dfeb51cc1639950fa307cd06a7213", "signature": false, "impliedFormat": 1}, {"version": "7048016c91c6203433420b9e16db56eec9c3f5d5a1301398e9907ac1fed63b58", "signature": false, "impliedFormat": 1}, {"version": "a4234645829a455706bf2d7b85642ee3c96bfe1cfddc9918e25bac9ce2062465", "signature": false, "impliedFormat": 1}, {"version": "9ff2d17592dec933b2b9e423fab8b8bc20feed486f16d35c75edd77c061de6e3", "signature": false, "impliedFormat": 1}, {"version": "fe9fc5b80b53a1982fe8fc0f14a002941b471213717536987d0cf4093a0c90a0", "signature": false, "impliedFormat": 1}, {"version": "4921f21de15ba1e7d1d5c83cf17466d30d4371bc9acf0c2c98015ebc646702ef", "signature": false, "impliedFormat": 1}, {"version": "f728f13a2965aacfb75807a27837509c2ab20a4bb7b0c9242e9b5ca2e5576d22", "signature": false, "impliedFormat": 1}, {"version": "c340ac804b0c549d62956f78a877dda3b150e79954be0673e1fc55f4a415f118", "signature": false, "impliedFormat": 1}, {"version": "2bfe95f5f0ea1a7928d7495c4f3df92cdc7b24872f50b4584e90350255181839", "signature": false, "impliedFormat": 1}, {"version": "9dfe677f6d3a486eebe1101b4cf6d4ec1c4f9ee24cc5b5391f27b1a519c926f7", "signature": false, "impliedFormat": 1}, {"version": "2766c9a60df883b515c418a938f3c8fd932241c89aba12aedf418e02a73017ce", "signature": false, "impliedFormat": 1}, {"version": "394967bc5f7707312a95cd7da0e5b30b736b7ab2f25817a8fea2d73b9398d102", "signature": false, "impliedFormat": 1}, {"version": "014a4afcc1674f40c7d77ca215e68bb3b0a254c2c925bcaa9932b6fb8f1ccd4e", "signature": false, "impliedFormat": 1}, {"version": "f816538db9388ac17bd354cf38d52da6c01d9a83f0589b3ff579af80cff0c8c6", "signature": false, "impliedFormat": 1}, {"version": "d2e0c04dce50f51b98ee32fd461dfa6e416a4b703c3d6d7e7fb7e68eca57a8de", "signature": false, "impliedFormat": 1}, {"version": "a8995e0a2eae0cdcd287dca4cf468ea640a270967ed32678d6fbf89e9f56d76d", "signature": false, "impliedFormat": 1}, {"version": "b151ad192b8e11b5ca8234d589abd2ae9c3fc229cdbe2651e9599f104fe5aa6b", "signature": false, "impliedFormat": 1}, {"version": "c37f352ab276b3cd4117f29e4cc70ed8ac911f3d63758ca45202a1a052fa9d00", "signature": false, "impliedFormat": 1}, {"version": "c97ffd10ec4e8d2ae3da391ca8a7ff71b745594588acc5d5bdef9c6da3e221bc", "signature": false, "impliedFormat": 1}, {"version": "74c373c562b48a0bde3ee68ac563403883b81cabe15c5ada4642a559cbd5d04e", "signature": false, "impliedFormat": 1}, {"version": "d42fe36f52e0ae09274753ed0fdedb32c42c2ad6ad247c81e6bd9982d1762004", "signature": false, "impliedFormat": 1}, {"version": "87f162804c7a5615d3ea9bdb2c828cd1d1f8378d5e2a9c3be1bd45c12f1fc1a5", "signature": false, "impliedFormat": 1}, {"version": "ccb92f285e2f3a3462262945fa59506aebe6ec569e9fec223d45d41c7c6cd447", "signature": false, "impliedFormat": 1}, {"version": "04e45000cf1381e6a84196aa01ca811ab192ca0a09debacc9e75dcfc6777bae1", "signature": false, "impliedFormat": 1}, {"version": "566007f48fa4cc7d29e4cb5cce9c315ccd52b72300d2d45ab0c639889e42d455", "signature": false, "impliedFormat": 1}, {"version": "4c2f8fb8a8f4afce6e05b9c554c012eb50147084933d78f7d218108740afd803", "signature": false, "impliedFormat": 1}, {"version": "6f72b3ebad0276cfcc7291fd2aefd1fbbd229487ec1acbbad03e798e8760e02e", "signature": false, "impliedFormat": 1}, {"version": "096681898d7131c1183f164ccfec478d99a9efa3744a1b6617116bc6713ed7be", "signature": false, "impliedFormat": 1}, {"version": "2c9626288e967ebb03ec2bc27ea504f6f829b1686f65b86fd5074d53e0160d70", "signature": false, "impliedFormat": 1}, {"version": "4de35fb3800a92324c59c1d2ed28a4dc1284d507d27ef2eed680c2f9ebb32cd2", "signature": false, "impliedFormat": 1}, {"version": "4c3cccf01f76ca4292746b6dfebd6df4382eb7a05315724116feacecf952f106", "signature": false, "impliedFormat": 1}, {"version": "492d1d21f79a8fa084e9dfd8fab89247301a49f1a0c12765b99c30a0ad8629ff", "signature": false, "impliedFormat": 1}, {"version": "69872cabf40dd4399939184cd7c5e47da62a9df811d3f56d193a437817a85b21", "signature": false, "impliedFormat": 1}, {"version": "19d00382e69115eeb1214d9b865030b61ec14f1bd5e91fb6e2b75acf5a6bef80", "signature": false, "impliedFormat": 1}, {"version": "3c3045d2661ef44458559f6bd48ebb47ccdfcbc513d859dc60c5e18e0544ac87", "signature": false, "impliedFormat": 1}, {"version": "e1de43a7fb0dda59dd9ed398fa306abdcb99da16b54edd3c7dc5e1a45d7e91df", "signature": false, "impliedFormat": 1}, {"version": "8301449ecbf03d5f893c298863fb66d97f1becb31f157276bdba7c708174a5be", "signature": false, "impliedFormat": 1}, {"version": "a556bdee2de2416a026022aeb260b5d684da34e322b5a95c7503143e51787b4f", "signature": false, "impliedFormat": 1}, {"version": "e8bc04f55c1b3da172412955b2785de54f2e1f2c9cb8949c0748ff143525310e", "signature": false, "impliedFormat": 1}, {"version": "683ad3639d8a96cfc782d672c44797d13c735ca9792d6c57e2fa5ada89e18e0c", "signature": false, "impliedFormat": 1}, {"version": "25b171a82c55909032e85448d89f8409e045a24a2b0458080bf304845b29b6ba", "signature": false, "impliedFormat": 1}, {"version": "ce25751e5374e1f13100276ecf2e2e8aac4d4c7229f762b3dc206639640954b8", "signature": false, "impliedFormat": 1}, {"version": "2f0a5a8ef5c6f5866d3caf04151422d05e64765ee250a7e9defc62908cfe73af", "signature": false, "impliedFormat": 1}, {"version": "79726fbe0854724f5bc3f16d4e40c0b320bbaa7a6296d1d782d70909f3b3a2eb", "signature": false, "impliedFormat": 1}, {"version": "6d391889910947acbe7d110271463ef74e7f65ae372d355756b1a6b0a987168d", "signature": false, "impliedFormat": 1}, {"version": "b3dadc705ad865a3acd5b40561ac0dcbce38fa28872ecb903eb586bd64cfa8b6", "signature": false, "impliedFormat": 1}, {"version": "8181adc6c7145eb6b2596249f3a2e1ff2fa7ebc604e73fe583f98c4b40916d6a", "signature": false, "impliedFormat": 1}, {"version": "dc84bb520982504eb30b09b870b32be8eccff2cd9beb963efd6a78971ae104b6", "signature": false, "impliedFormat": 1}, {"version": "bafdca74b47f54e116a9f2d589d39f1c677c777198b96a677a2d2f628b43c8f5", "signature": false, "impliedFormat": 1}, {"version": "9ccc168fc7cb696b5f60f216c72881db1f6c2d8e39eadd6c68130711f8eddd19", "signature": false, "impliedFormat": 1}, {"version": "6187a2dae6a9d910f272bfae324625437343f43a6ff48a28a5c5dd5e9cfc2d5f", "signature": false, "impliedFormat": 1}, {"version": "f063f87a44b1e92948bd5ef6db5b8cadef75218126e75ff02df83196e2b43c4b", "signature": false, "impliedFormat": 1}, {"version": "333df4996910e46b00aa9b7c8be938f6c5c99bfbf3a306596e56af9fff485acb", "signature": false, "impliedFormat": 1}, {"version": "deaf2e9bfb510a40e9413d5e940f96bf5a98a144b4e09a0e512efe12bfe10e9b", "signature": false, "impliedFormat": 1}, {"version": "de2395fb1d7aa90b75e52395ca02441e3a5ec66aa4283fb9ced22e05c8591159", "signature": false, "impliedFormat": 1}, {"version": "64be79c9e846ee074b3a6fb3becdbb7ac2b0386e1e1c680e43984ec8e2c2bbb9", "signature": false, "impliedFormat": 1}, {"version": "9c09e723f7747efc123e19f0ced5f3e144bbc3f40a6e1644a8c23437c4e3527f", "signature": false, "impliedFormat": 1}, {"version": "36fc129c8e3ad288656ea0e9ba0112728c7ec9507c75c6a3bce6d66f821a31d5", "signature": false, "impliedFormat": 1}, {"version": "3771470dde36546305e0431b0f107e2175d94e11f09b116611156f134364127e", "signature": false, "impliedFormat": 1}, {"version": "18c6715ca6b4304a314ff9adb864bd9266fc73813efd33d2992a7c6a8c6e7f73", "signature": false, "impliedFormat": 1}, {"version": "90cde8ac2173d2008c51996e52db2113e7a277718689f59cd3507f934ced2ac2", "signature": false, "impliedFormat": 1}, {"version": "69d01aac664fe15d1f3135885cd9652cca6d7d3591787124ae88c6264140f4b1", "signature": false, "impliedFormat": 1}, {"version": "55ab3dd3c8452b12f9097653247c83d49530b7ea5fe2cb9ef887434e366aee8c", "signature": false, "impliedFormat": 1}, {"version": "abd2ce77050bfd6da9017f3e4d7661e11f5dc1c5323b780587829c49fcac0d26", "signature": false, "impliedFormat": 1}, {"version": "d9dfcbbd2f1229ce6216cb36c23d106487a66f44d72e68fd9b6cb21186b360cd", "signature": false, "impliedFormat": 1}, {"version": "244abd05ca8a96a813bf46ddb76c46675427dd3a13434d06d55e477021a876ef", "signature": false, "impliedFormat": 1}, {"version": "5298f6656d93b1e49cf9c7828306b8aefc0aa39ac56c0a1226f1d4fba50a2019", "signature": false, "impliedFormat": 1}, {"version": "93268ed85b0177943983c9e62986795dcb4db5226732883e43c6008a24078d7f", "signature": false, "impliedFormat": 1}, {"version": "843fa59ad0b6b285865b336b2cbc71cdc471e0076a43d773d580cb8ba2d7030d", "signature": false, "impliedFormat": 1}, {"version": "aa2d452401748a5b296bf6c362b9788418b0ab09ee35f87a89ba6b3daa929872", "signature": false, "impliedFormat": 1}, {"version": "a4ef3c3f6f0aadacac6b21320d0d5d77236360e755183802e307afd38f1cbcc9", "signature": false, "impliedFormat": 1}, {"version": "853b1daed2861381ddda861a0450ce031c280d04caec035cc7433872643871c6", "signature": false, "impliedFormat": 1}, {"version": "1058ed9becf0c63ba0a5f56caaafbfd0bf79edf2159c2f2f2fe39a423ae548ae", "signature": false, "impliedFormat": 1}, {"version": "8b6eab9a4a523909ee1c698a10d332c544aa1fb363f482fe60f79c4d59ca2662", "signature": false, "impliedFormat": 1}, {"version": "f2b2c244b10a8e87192b8730ed5b413623bf9ea59f2bf7322545da5ae6eae54b", "signature": false, "impliedFormat": 1}, {"version": "92bbeada67d476b858679032b2c7b260b65dbccc42a27d0084953767d1a8cf46", "signature": false, "impliedFormat": 1}, {"version": "545afad55926e207ac8bdd9b44bb68f0bbffc5314e1f3889d4a9ad020ea10445", "signature": false, "impliedFormat": 1}, {"version": "4c8ef63125ed4d1eef8154ec9da0b6b7ca9effdf4fa5a53ab74a9d73c9754ff5", "signature": false, "impliedFormat": 1}, {"version": "e76a7e0b4f2f08e2bef00eacc036515b176020ab6b0313380dd7a5bd557a17f0", "signature": false, "impliedFormat": 1}, {"version": "fabd983e4148e2dce2a817c8c5cdbbc9cf7540445c2126a88f4bf9c3e29562b2", "signature": false, "impliedFormat": 1}, {"version": "a80c5c5bab0eb6cc1b3276ac276e5b618ead5de62ec8b0e419ea5259af0a9355", "signature": false, "impliedFormat": 1}, {"version": "d8cf5ded7dd2d5ce6c4e77f4e72e3e1d74bb953940a93d3291fb79158e1afc6e", "signature": false, "impliedFormat": 1}, {"version": "bdb10c13a7ababaae91932d0957ef01cd8a789979cd0b606a2106d198848b16c", "signature": false, "impliedFormat": 1}, {"version": "0fd3f9fed4dd35b1b07c18b4c3f612b7542c91835ad8a26e0e83d905709543dc", "signature": false, "impliedFormat": 1}, {"version": "441b5f5ac4619df9dbf436ecdb9f0bbaacf8696e6fdb2f81c6f5b1db76f5a1c0", "signature": false, "impliedFormat": 1}, {"version": "5d2284728400ee7b4fd1acd69e48d649d4056916cc70950a0000e5d70a32a750", "signature": false, "impliedFormat": 1}, {"version": "27ef186120f9e7ee90686aa7ad5163eb5c7f4cdeb19bb87850c4a5fe4b8e05e8", "signature": false, "impliedFormat": 1}, {"version": "4f1f9e056e0c9d23031367b4c7e7eedffb3e1ed58e64befc90749ca4dd9363ee", "signature": false, "impliedFormat": 1}, {"version": "2b0ccf76bcf10f61612135f951a74327ea0a2d5a80f397b767e0e0b08cdac265", "signature": false, "impliedFormat": 1}, {"version": "4e42e643f05a7fa69581a1a697a1cf967d9b2657dd9dd66e59d90500ec053ba0", "signature": false, "impliedFormat": 1}, {"version": "0ea8485dc0bb7d2a258a93b16305e17fb5be9f877a9df88de7023a9821c537ab", "signature": false, "impliedFormat": 1}, {"version": "5c221ba5333b775cef94d4a30076cc30730cceba649e9d30c5a7224a698c8825", "signature": false, "impliedFormat": 1}, {"version": "cb61ba4d5b5e39ecafe74ad7d88dc8e67defcffe15fb9216addee0fa06d5df38", "signature": false, "impliedFormat": 1}, {"version": "d83e8f0c10477fb4a7729a51aaad853cee81e0e332581dd2244da09e5526b5ff", "signature": false, "impliedFormat": 1}, {"version": "c8933a5b693306696e78315dca1fa57f6f5493fed44cd90aa2d4a4d354dd6516", "signature": false, "impliedFormat": 1}, {"version": "af8e2bf3df20cd2e6b8d744dd83499e174609d0c88864af3f30cd43671e719f5", "signature": false, "impliedFormat": 1}, {"version": "4186fd8b51535399c7ad1edc08f9c4ebb2a9e8e327b131cc1f950c5dfbb0c358", "signature": false, "impliedFormat": 1}, {"version": "b92965f503f55830702062f9e0832fabfbded49ff28728686a6fd84aa32f454d", "signature": false, "impliedFormat": 1}, {"version": "172dbc7933ff46ba3b2efe8b5c7828fd4f0d45c08755df8200213b6055d57f2e", "signature": false, "impliedFormat": 1}, {"version": "89e2ec7ed42725f89fa537c38f20144782bec6c5710e467a46a647647c8255cf", "signature": false, "impliedFormat": 1}, {"version": "5165882999957fa041e423a4fb64627dcb310bf50183af70a6ee8e10a584b0c3", "signature": false, "impliedFormat": 1}, {"version": "390997d64e1e5721fa807aa9e05c97086f58627170d9a7ed84b127126a3e5202", "signature": false, "impliedFormat": 1}, {"version": "00cf8ed9b47860a5f8cc0a65d7a41f85a7026f68162057728abc9249943a8629", "signature": false, "impliedFormat": 1}, {"version": "fc8b086c99f6d721eae8125a96833e0ba1762d00b80aad1d55c7a8b59d007466", "signature": false, "impliedFormat": 1}, {"version": "ff72c74ccdc5570c4a75a93e605a5586596444d96048d52c72f322da183c556d", "signature": false, "impliedFormat": 1}, {"version": "b8755448066177191edcd0b7e19e7fe44d69ed6dc97b16a420b7aa9070e2b850", "signature": false, "impliedFormat": 1}, {"version": "822a0c843f492ad2dc815080f24d4ddac4817a9df0de8cd35830e88fbbafbbe4", "signature": false, "impliedFormat": 1}, {"version": "467865324b9f66a1b8f68d9350c5aa0e749eec499e4863fe017b16ea8bcaccdf", "signature": false, "impliedFormat": 1}, {"version": "863bd77d5546877e19594759a901cc7b75da8d27336d4351e54413ec12032d09", "signature": false, "impliedFormat": 1}, {"version": "a17a62c94da321c0bf2315c35033e313daf1298a75aa43a01a4daf6937980c01", "signature": false, "impliedFormat": 1}, {"version": "851271a09d3c2db3eab80d64beb468d775a9818df06a826ba58925c900231ccb", "signature": false, "impliedFormat": 1}, {"version": "da2c95cd1f0f9cc19f3dd599b4c8fb0930eccb78a5c73f683e7ea98262d2f55e", "signature": false, "impliedFormat": 1}, {"version": "e40d3ca85fb1362763067506784635aa28863640cf7cf9be9e8c1c521c0fbbd5", "signature": false, "impliedFormat": 1}, {"version": "77a2f84e19aca9d03efdf0c484aba8daad3fd23c70b72e63aca78fadf71b448d", "signature": false, "impliedFormat": 1}, {"version": "00c5b6248c69e66729e5c4acb239db849b1497d7eb111fed3eba979432461ebf", "signature": false, "impliedFormat": 1}, {"version": "8e13abf75e9394f3a4b1d0b3f99468e15f4c7e2115153d2a1ca3c0de035bad1c", "signature": false, "impliedFormat": 1}, {"version": "07097dab1c068118806fecb8544aba3cca30965d0864b1998af1bee326a9990c", "signature": false, "impliedFormat": 1}, {"version": "c490ca6eb9149c28e4f2def6acb1bc058d160edb40fd249cf2a70c206a8cfecc", "signature": false, "impliedFormat": 1}, {"version": "7c9aab9a76abba65aa6389e41707d57ea0288dac9a8b6359465dcb462d2cfaa1", "signature": false, "impliedFormat": 1}, {"version": "97fbe30fd1b61b26f807ae1c78b681b0999af71cd9604c08a1d45e44690ca0c2", "signature": false, "impliedFormat": 1}, {"version": "ef91bf45a3d149db0b9e4e612ed1400c35f6a3d2a09669d1441add612d5f16b8", "signature": false, "impliedFormat": 1}, {"version": "dacebdc0353168f259724bccfd273b892e883baf36cf3dee21cf4178f3ef9ea0", "signature": false, "impliedFormat": 1}, {"version": "5416fb031a72377c3c17faa2041428a5f19f9d46a70b645dda6e3293fd0ca8ce", "signature": false, "impliedFormat": 1}, {"version": "95611472fd03e0992070caa3a5387133e76a079719994d237947f6bcf67f9bca", "signature": false, "impliedFormat": 1}, {"version": "6141d19bfa7698f362e84460856ace80a1eac3eab1956b188427988f4cd8e750", "signature": false, "impliedFormat": 1}, {"version": "1acded787e1fc09fd56c004d3ba5b719916c06b61976338a92a2f04ec05cba5c", "signature": false, "impliedFormat": 1}, {"version": "8fb0d41cd90f47b9148e4a474fb03484d9af1735871321a2f57f456e40a7e319", "signature": false, "impliedFormat": 1}, {"version": "a25cd4cf54bcdd109dd46274e2369fc1cad6d74350b5642441d2b9eef515c3bf", "signature": false, "impliedFormat": 1}, {"version": "af4b9f16e50a0ae803745150e4c091e86ab95f3dac649286af28505258f7a189", "signature": false, "impliedFormat": 1}, {"version": "3d209a6c3c53366b3bcb72dcf04a7ceda57362cae6ac47dbb783321934a0c5ad", "signature": false, "impliedFormat": 1}, {"version": "4766770027d93a5ad1d4cc880cce405b4c6f67c64303ab34b347d6428eb783f2", "signature": false, "impliedFormat": 1}, {"version": "43d2bec085f0fab54d7b9dfa3f5c5ce65e30da6a19d82ed37d1d41867682f86e", "signature": false, "impliedFormat": 1}, {"version": "e5efb9781a0ef18d60cbb8afa261489efd260d87642c095cacba0b09b2684fcf", "signature": false, "impliedFormat": 1}, {"version": "775ca7538a2f9bc674ebe5f3cb8aa8fa346ef4c1faec4c5b13b4784a744854dc", "signature": false, "impliedFormat": 1}, {"version": "c0037c7c6fb8031f7047a1ccdb381762862b48429e9ab07bac8fc35fc5b5dd14", "signature": false, "impliedFormat": 1}, {"version": "af4db63c6e4d55df1ad7f3dabdde31bc30555debf1cd6b79ea65a36c52bf199c", "signature": false, "impliedFormat": 1}, {"version": "d291ffc234a58061b8192f74422f2e51fb87f6d10e82c30a555bccf9641b3e38", "signature": false, "impliedFormat": 1}, {"version": "6d683695e9765b29165bb0823f88755211d48949f0b95a9a4236802afddf41e1", "signature": false, "impliedFormat": 1}, {"version": "8fcd568ba937d867544cd8e726f35a515690ad041387fdebc93d820c8720e08c", "signature": false, "impliedFormat": 1}, {"version": "81a0ff507ece65e130c1dd870ba79b8337c1fd345db7b154a2749282c994d2d5", "signature": false, "impliedFormat": 1}, {"version": "64e2ffc72047548fa3c04095abb9dab48e2eaac169161fd2ed3564dea0c67e57", "signature": false, "impliedFormat": 1}, {"version": "b525d2fc6b694512a877219ebba25d5fa244f99253a5bbe6c6421f8d71b1c806", "signature": false, "impliedFormat": 1}, {"version": "d695f0d65f5fba0e275cf7801399575c272b86e7bf8e70133f8fc03517305b1d", "signature": false, "impliedFormat": 1}, {"version": "0836f15e5e7dcad64fd50d49a39267da34371d1c2b803b38dffcfabcd2ff604e", "signature": false, "impliedFormat": 1}, {"version": "56eff313f885482d44e4aa7cefdd55f7d0d92a91c1ddf9cd73c533abc36f4dff", "signature": false, "impliedFormat": 1}, {"version": "022ff6b725f6ab95b1c4d229893b3047002a9c1fab6798c8fe63797ec1e63dc5", "signature": false, "impliedFormat": 1}, {"version": "5e64d04301aa6ae6bf0f3435d07804889342873ab2875a16c827db9e6543002d", "signature": false, "impliedFormat": 1}, {"version": "0b8c3effe0c65129d493be140da1a83eb61a1e83481d441dd2bc359a926b453e", "signature": false, "impliedFormat": 1}, {"version": "0816c977ef73d99cbb134427a83f91ca6f7fe00eb7544118320d613a85da6879", "signature": false, "impliedFormat": 1}, {"version": "068db2994f5926e888462b0852ada2c24f2cb50028f034f475407957ca51c6cd", "signature": false, "impliedFormat": 1}, {"version": "59106b469557319ad26f40f054861be3fd2cf09911c3b66df280b9340a1d9caf", "signature": false, "impliedFormat": 1}, {"version": "69e8e2dc21b0636f671485867555439facd68ee9e234fc9190c3b42e7f1a74e9", "signature": false, "impliedFormat": 1}, {"version": "5fb0c0cae187f6554769cd4ff36575ddbc43078a4fdf9b17a5c0c25dfa9a9f2b", "signature": false, "impliedFormat": 1}, {"version": "918d99a7aa4b7f5edf2cdcb33c163837a892f43b9e22c10634d61d0a28fc09a2", "signature": false, "impliedFormat": 1}, {"version": "097b0d1e237bfcc97411fcae19a484a717fd4055a48e98ade5cc28b26afd21f6", "signature": false, "impliedFormat": 1}, {"version": "5fb0eef64cb75951f7ae2dc6a704aa0567a25a39a616a5dd10ba7cfbfcf73b78", "signature": false, "impliedFormat": 1}, {"version": "0a649cbc59a47f224d0494a6d5167a803ed049f995ade8423c7cb62bb6a38b64", "signature": false, "impliedFormat": 1}, {"version": "68e25d1a79523b18fae630ca57100ce2dff6c5023376a2f57e9d0d07e1b9b8ef", "signature": false, "impliedFormat": 1}, {"version": "1a505f408bc7d484553b7701f712dc52e1174648baff7d6c9c1f38b5cb83b772", "signature": false, "impliedFormat": 1}, {"version": "b19badf31df455f10cf44fda9f6a0e0b42d6e970ac122b66c5da5d683fa270d4", "signature": false, "impliedFormat": 1}, {"version": "71b6fe5c85eb877c3e3ed2f142b95a69f97905c34f11fc6d9052a4317e7f6bae", "signature": false, "impliedFormat": 1}, {"version": "bd55536c0f989f59af6ca66cbc8121485f978f4e07c3df1688623c5f898058c6", "signature": false, "impliedFormat": 1}, {"version": "dcb868c613ccd06b1a3ff56ee235e5987820c0c8bbd77fedc9af4dcfdd4c54bf", "signature": false, "impliedFormat": 1}, {"version": "f3d1b3cd130e3cd67fe8e06256deb5d678243c6976ea498c81a48e542efb7529", "signature": false, "impliedFormat": 1}, {"version": "772b881836efbdceb7ae8d3ae038f14ec83444397d8429b866312dcd78714dde", "signature": false, "impliedFormat": 1}, {"version": "314d516eb3bf1eda07e898935edcbd1e74739493c8ad444e82181f8a020eef2c", "signature": false, "impliedFormat": 1}, {"version": "8cfced8e57c64563f91e90a76a6df2d8f934c90a425327a9ed5393bc88c27d97", "signature": false, "impliedFormat": 1}, {"version": "67bd754a8775c81794c9fc84b1a1e9fca44a402fa7d93fcdad4ba2d37737d929", "signature": false, "impliedFormat": 1}, {"version": "5128e32c57068eb09d5189eb68681ca7d0e5e4b0cdedecbef9c67689f0970876", "signature": false, "impliedFormat": 1}, {"version": "7fcdedd29146e5a2a6c86eda652f8485a1eeda1b8646825bbf729023f6ea6013", "signature": false, "impliedFormat": 1}, {"version": "86b9b361ce8ea1d9f04e15bbe49e5ac72e5f97d8cfa8592930d32f267729a201", "signature": false, "impliedFormat": 1}, {"version": "671f5e3a931c2737f8dfa43b34c4a320eca27fc6584ecef890ddd7374cee5cb7", "signature": false, "impliedFormat": 1}, {"version": "ff213315eebd3ff05e01b383f704d79d8139aad5cb0d6a13c082f2e29625adbc", "signature": false, "impliedFormat": 1}, {"version": "83ed351a10ef17b7811d3c06fc2775e36b6911278326d55da8d1eef8ff2f29df", "signature": false, "impliedFormat": 1}, {"version": "2f5f146f1d6c04cf89ae0e9b4cf2b064b2ce4319ba6a5bf18ab8fb29db1cfd1a", "signature": false, "impliedFormat": 1}, {"version": "7fc2b96a8465725bf774bd490c383edd5ee3dfe0d38c13551d082cae2de4041e", "signature": false, "impliedFormat": 1}, {"version": "9eaeb6696e4218cb5bded9ee27c3e95589ad4af1fd4b97ccdca43eadd62c94d5", "signature": false, "impliedFormat": 1}, {"version": "fd580a99cb9bb84288da00eea67dce300bdef06d4da2a727c0fc466d2922dca2", "signature": false, "impliedFormat": 1}, {"version": "b82809d4468b6ba4d72437adaab7ca273547c59974e954c48f655a4b1bdca429", "signature": false, "impliedFormat": 1}, {"version": "c6455d4ed4f7337bcb885c61372c7d9b03991995ed73e29023bad502d1336f0a", "signature": false, "impliedFormat": 1}, {"version": "b5e6f0491b5a2002eb9b1146165cf915ee58e0fddf7f2adb5f2aa4bc44b4fb83", "signature": false, "impliedFormat": 1}, {"version": "f534aef095a62fb82f57768fc52995d3e58d95e0a1671b0256a4704802aee818", "signature": false, "impliedFormat": 1}, {"version": "cdc6f1d471882782cdac7442dbdad65aede5f749c09799a84918bd916eb6d6db", "signature": false, "impliedFormat": 1}, {"version": "2475197472c609662f09660e3964a86aa355cea0e671653656800690bb508b7c", "signature": false, "impliedFormat": 1}, {"version": "b4067760d0447747d82b6848b640168d656d0b916c3add2ec94c3c4dea92fc9f", "signature": false, "impliedFormat": 1}, {"version": "c6c591a17f9c0c2821baf15f775f5c7d6dd4a0786365ee9c182d7a97e38ad96a", "signature": false, "impliedFormat": 1}, {"version": "ede44ddf9d274a859e9f1f34333d5f0e8cf2167c3265f81d5280d37b872b4552", "signature": false, "impliedFormat": 1}, {"version": "6317aba53c9152998bb1f8bd593f55730084d05c00c774ff72a3aa4d687a6dbb", "signature": false, "impliedFormat": 1}, {"version": "26f1bd15980b19d925be98afde3918a6a181435b87e9b7c70d15726ecbfff0e5", "signature": false, "impliedFormat": 1}, {"version": "57af4faf6847adff5048f82929b9a7d44619d482f571534539ae96a59bb29d3a", "signature": false, "impliedFormat": 1}, {"version": "874770f851ac64a93aaddfb86a2f901f158711911fee14a98a67fe32533ee48b", "signature": false, "impliedFormat": 1}, {"version": "3d933e519ad9cc8cf811124f50d0bc14223cdea9f17adf155f11d190ceb2a6c8", "signature": false, "impliedFormat": 1}, {"version": "d5dfce61a7bf994d2cb711af824efa4de9afa5854d34e6725b9c69d925b6b2dc", "signature": false, "impliedFormat": 1}, {"version": "f77d1e10417bf43f8fa5d18916935f342d4d443e371206ede7239faaf9abbbb8", "signature": false, "impliedFormat": 1}, {"version": "c94e0b8815b72ba924c6b8aa666b25903d949a7ab0d38ed84e4bf65da3d06a3b", "signature": false, "impliedFormat": 1}, {"version": "15db84e660fdcd8468f23973ab83c31d7fd28bdddb30b0aed16cfa051aafe900", "signature": false, "impliedFormat": 1}, {"version": "7c01cbfe181c0e10044831b899de6c2eec4fba32de1f1cca12742d2333c1345b", "signature": false, "impliedFormat": 1}, {"version": "62cb1636513ef26d3ea83fb5d2369cf8569d04aa30d8fd7f5327d0e10841635d", "signature": false, "impliedFormat": 1}, {"version": "8282a076b07dc3dc6b2265377627ab3860cb6a1bcbae85a5a4006dec4c9f0066", "signature": false, "impliedFormat": 1}, {"version": "b273c241dd08c6276fd35be413c64508ae50f847fa052bf7781799b51da8e9e9", "signature": false, "impliedFormat": 1}, {"version": "3bc0bbef6d7fb63002fe80167db350b9677cfce5872c0cc7ecec42ba8248ded6", "signature": false, "impliedFormat": 1}, {"version": "4880c6a85442934b81f3b1a92cb6b43df36f8c1b56b6822eb8cbc8c10c438462", "signature": false, "impliedFormat": 1}, {"version": "1bfdd8c1710a3d1654746ca17f512f4a162968a28e1be1a3a1fdd2a8e5bf385f", "signature": false, "impliedFormat": 1}, {"version": "5405aedafdf272dde53b89036199aaed20d81ddc5ec4bea0cb1ab40232fff3fe", "signature": false, "impliedFormat": 1}, {"version": "db2ee45168db78cc83a4368546e0959318374d7256cbd5fa5692a430d5830a59", "signature": false, "impliedFormat": 1}, {"version": "49993b0eaa14d6db6c334ef0e8b1440c06fee2a21ffd4dea64178880bd3d45a2", "signature": false, "impliedFormat": 1}, {"version": "fb9d9dc0a51cb4014d0e5d5f230ec06ffc4eb6caae6eecfe82ea672b7f3c6967", "signature": false, "impliedFormat": 1}, {"version": "84f44079a0793547d3a629feb8f37d8ef6d07cb5bb5fdeefd887f89e9be871f6", "signature": false, "impliedFormat": 1}, {"version": "295c5ec088a1bfc286e8dbdc9807958588979988cd7a74ad32be774a6f6ea512", "signature": false, "impliedFormat": 1}, {"version": "f15129c62ed04410ac0a3326ae6fa5ef7229bbb1b0cbfa252b5c558505a38253", "signature": false, "impliedFormat": 1}, {"version": "4bf500d9a554d43cb9133d60f1b3f58ca98b0f794486d1377f3effc551b40faf", "signature": false, "impliedFormat": 1}, {"version": "536f6a9208c89eb8f0a5eeda629175b0fa62ccd22e387af7f35297fa2af6897d", "signature": false, "impliedFormat": 1}, {"version": "8c95fe5a655ea1c78f0335f8da58e70d98e72fe915987c3b61c6df49d6e276d1", "signature": false, "impliedFormat": 1}, {"version": "4bd434d3055d1b4588f9d7522d44c43611341de7227db9718a700703c608e822", "signature": false, "impliedFormat": 1}, {"version": "935507b695f420fddff2d41ddc12ff3935931a3f26d6aa65afbb276bfdf51cb4", "signature": false, "impliedFormat": 1}, {"version": "e851c14c9dbe365592f5084c76d4b801e2f80302f82cebbe7c2b86095b3ae08a", "signature": false, "impliedFormat": 1}, {"version": "b5c90d931d285d9d1c4cb92d71f2719e28caaa9ca062072d0bb3b69300b436c2", "signature": false, "impliedFormat": 1}, {"version": "40b3e953e9ea51a86a1e5b60a2355eeb780f2f8ce895ece252910d3e0a033a16", "signature": false, "impliedFormat": 1}, {"version": "0264b432aace8398f174e819a0fc4dc196d5aed49ae65aae071fc2ec8e6dc029", "signature": false, "impliedFormat": 1}, {"version": "3b29bb23855a1924264c3a30b5c73b00c52a57c2ffb5f91c48c9572e71048f19", "signature": false, "impliedFormat": 1}, {"version": "8b9b2e76db07d8926bcc432c9bdfb38af390568951b39fe122d8251b954f9ed2", "signature": false, "impliedFormat": 1}, {"version": "96e85c6fa102741a25418ab2c8f740c994e27ea86fd6518a17ec01a84b64dd5c", "signature": false, "impliedFormat": 1}, {"version": "9525b28a4fa959c8d8c7d6815f842f78c67b40def9160afdced5c9daf14cd4a8", "signature": false, "impliedFormat": 1}, {"version": "0e59a6944a52f52138315b6658fb1d217fa017b7abec12006c491d51e07fb56d", "signature": false, "impliedFormat": 1}, {"version": "cfa8acfeb9d68702aa6249b7295ca73ea598e441f014cd4184b6e2a3ea9a275c", "signature": false, "impliedFormat": 1}, {"version": "21b0c616f61cd6699135a34a500f7df30022abf9358ba612f10668ea3c988e00", "signature": false, "impliedFormat": 1}, {"version": "9ad1d0b171f7bb9f484ad156e97f0d8e760a5fee13e342831669c7b2d1137a30", "signature": false, "impliedFormat": 1}, {"version": "7ccadd4ba126bb2c0564bfb85ddd7d084aa5f2880cc2d0149fbe183fd5ceb6d1", "signature": false, "impliedFormat": 1}, {"version": "ebbde5a8a356a1547ac6ecdfba7547036a5ada116011cb96634c32df1cf69084", "signature": false, "impliedFormat": 1}, {"version": "e703eded767e3a944ac1f7c58c201a0821da1d68c88d6ba94bb985a347c53e42", "signature": false, "impliedFormat": 1}, {"version": "99953f3f1f9deae755b97ed3f43ce2bee2ae1324c21c1e5fa9285c0fe7b5077f", "signature": false, "impliedFormat": 1}, {"version": "2afd452bfa6ebaacbead1ca5d8ab6eda3064d1ea7df60f2f8a2e8e69b40259e9", "signature": false, "impliedFormat": 1}, {"version": "dae0f3382477d65621b86a085bdb0caabf49e6980e9f50ee1506b7466c4d678d", "signature": false, "impliedFormat": 1}, {"version": "e5793b3f4cbd73c841790264db591d3abe9bd09128302a2901fedd2353ab24d5", "signature": false, "impliedFormat": 1}, {"version": "41ed74193a13f64a53705a83e243235920fd58d4b115b4a9f5d122362cda7662", "signature": false, "impliedFormat": 1}, {"version": "478e31b207faa7110b04f6a406240f26b06243eb2d2cff3234c3fc8dd075bf6c", "signature": false, "impliedFormat": 1}, {"version": "ea917cdbfb87d11cd2f8b03e357b22b1114d0ba39ce4ce52b1a4f0dc10c6c20a", "signature": false, "impliedFormat": 1}, {"version": "3ef0c5634d9aabee346f9ba056c1c5d977f2e811f6d13c082614c9062cd4b624", "signature": false, "impliedFormat": 1}, {"version": "1ddb49c7f8fc4b9e4da2d5ddca91b4e2763fe7d17aa79940bd60406f3e2739bd", "signature": false, "impliedFormat": 1}, {"version": "d5b01eab562dc40986a5ceb908519dc7f02a7ded2bcb74318317a75714dbc54c", "signature": false, "impliedFormat": 1}, {"version": "b19ef44e991aa150a19a9f84be1fd1c4d86496241300fd904216762246700623", "signature": false, "impliedFormat": 1}, {"version": "87df6cf2565a88dae3ec50e403e9ef6b434ad3e34d922fe11924299018b38e58", "signature": false, "impliedFormat": 1}, {"version": "9d999d30b52fb0b916f7a64c468f6d5c7a994e0c1ef74d363562e9bda3cb8b99", "signature": false, "impliedFormat": 1}, {"version": "9b1b05f88ded21046391276ff60d2d987bf160d77b40399e07b7bdbfe2e38b31", "signature": false, "impliedFormat": 1}, {"version": "628177f7eb0314f0189e4d90f663233606b3936ce391c7f98da46105ae402c65", "signature": false, "impliedFormat": 1}, {"version": "3c80bf6873eb3b95cd590aab8eb1612f0f7cef6a30b3f49535844f7cecd99351", "signature": false, "impliedFormat": 1}, {"version": "da367ede4ebd5ff4cb1cf9e6bc8eb35848b23c57c22c53360e53dc772c7be8f9", "signature": false, "impliedFormat": 1}, {"version": "4337acbd8896efb7e7d8d6e0eca78607fc7c1a9ad2bb228240f13f97b3492f1f", "signature": false, "impliedFormat": 1}, {"version": "505c7800f8195961302dee715870b7212bdfb667e5e47de76447151dd35a40f1", "signature": false, "impliedFormat": 1}, {"version": "cf5a3eed6cd493d198b0c1eacf70486d8bd527fc411d57660caf2c93b5ea0fb6", "signature": false, "impliedFormat": 1}, {"version": "900e344adae3c65076c9ba4ee1a77c6db19fb0c7e54d7ce23c28ff8d272cba26", "signature": false, "impliedFormat": 1}, {"version": "bcc5186a38d1eecf60b2c4d1e3eb9abd8ab91cb492f384a9d2ed7bcda2abd0d5", "signature": false, "impliedFormat": 1}, {"version": "0ec1b41954fea9def7d9d87e0f3beea2ba3ec5b7beb769f308cfe32ad2968669", "signature": false, "impliedFormat": 1}, {"version": "51189c085256f11da13b22792f1d7c928f8a8e9d9b6c7b38e956e72a51ef8219", "signature": false, "impliedFormat": 1}, {"version": "504f509e23f2ab3a8018533925c034a340fbce4af9e77a1f71a8ddffbe0c19fa", "signature": false, "impliedFormat": 1}, {"version": "635c049483e13e1dc8bee72dde300c40d350046cff59b202d41a12ec8c733d27", "signature": false, "impliedFormat": 1}, {"version": "7fd8d5f70ea745e1a0338de7aaacd9bd6ff086ce6de75dcf91749c77d1e23831", "signature": false, "impliedFormat": 1}, {"version": "78d2a7795bfd2be490937e8b01968a0acca8a6bdf5933570bc013806049d4175", "signature": false, "impliedFormat": 1}, {"version": "db49833b6e9aa54b535076f40615349a7465005367a787b50ba7b92421e26442", "signature": false, "impliedFormat": 1}, {"version": "6a936fc917de40c44ca81331ee7d7a71dc30ae1895871e7be7b6ed85d96cc41f", "signature": false, "impliedFormat": 1}, {"version": "bdd2a764cf87c4ab1efd7084597d1ca4ba17f6b6496553095ecca5a14b5d4278", "signature": false, "impliedFormat": 1}, {"version": "ddef8e6676fd572ee3de174ad28df05c7b3803542d7318482b8f98779ff25612", "signature": false, "impliedFormat": 1}, {"version": "34eae3bc7f5bfb515d2ec163ccd4b63fdb73ad7f66564707686d84f42a8b7c35", "signature": false, "impliedFormat": 1}, {"version": "d240d106cf9bc3c0efdb323d807b944ce16ac5d837ecef5b75f1e66d606b2a72", "signature": false, "impliedFormat": 1}, {"version": "639d5a26be297431e0bcc9f71f969fd7d84319fc03b5e1c672ea10fb0094c616", "signature": false, "impliedFormat": 1}, {"version": "770c3e6367c2802c027c0b1f86928f288e11ad77ac2f454d7f682460eab30a0c", "signature": false, "impliedFormat": 1}, {"version": "c9dd2760e0419a059cf733c38ef5d44eeca3fc647f9c201d88656e5040f5a3a7", "signature": false, "impliedFormat": 1}, {"version": "16766b8f3d1bba66ac8167e6407be6c490d4462e802f67c140b1174869db5b67", "signature": false, "impliedFormat": 1}, {"version": "f9267391788ac81ca54dfae32c5d86e99a19abaee9b172b2f8d98a3c2b578a2f", "signature": false, "impliedFormat": 1}, {"version": "92441638c0fa88072ef9f7b296a30e806bac70219ce2736ef33c8941259d9b70", "signature": false, "impliedFormat": 1}, {"version": "8774efbaf39f9ea3a0ff5b1c662c224babee5abb3d754796278e30eb2e51ae3c", "signature": false, "impliedFormat": 1}, {"version": "e634b47a7d3f9468572a7c9af1fe2f52687ee1afb23ba5568205a7a4c55662ef", "signature": false, "impliedFormat": 1}, {"version": "1cbef47ee169c717a1ef7ea91b15582c61ac721fd5f5671de95c3df9f026db9a", "signature": false, "impliedFormat": 1}, {"version": "0db0ee49f803c9b901dfe06be9c8fb6a1c05f98664ca34c68e0da575eae76f2b", "signature": false, "impliedFormat": 1}, {"version": "4b745fcadf040899979b6b26e24aca6d2fa2bbe52a919d67f717bfe0339354a3", "signature": false, "impliedFormat": 1}, {"version": "bc57f3550b3fd3b7d31b9a278d0b491dd45d170e37c4046a3105fdea9ebe5f89", "signature": false, "impliedFormat": 1}, {"version": "b5f7093d62a228669dd56edd0bcb86a0cf0b46db4816a3967b4632503c21b93c", "signature": false, "impliedFormat": 1}, {"version": "4d70bbb1f35f345b2c2e1b5c9b8174d5397bba76ffef12656bca16ce9a1830d3", "signature": false, "impliedFormat": 1}, {"version": "a004fc80aa8f78dfb1d47b0e098fe646e759311c276b6b27404f5e356528f22d", "signature": false, "impliedFormat": 1}, {"version": "c8933d9afe6c5ee7ecbeec5aa01f6b37d3c2be2f7dd203ee75ee4850164007cb", "signature": false, "impliedFormat": 1}, {"version": "b1129b38f1eea70951ece3ccd1cc3e1d094379b64d3958ba8ce55b0ec0083434", "signature": false, "impliedFormat": 1}, {"version": "b2bb10f992cfd1cf831eb005311a80f7f28bc14cfac5883f17e75f758d1354e1", "signature": false, "impliedFormat": 1}, {"version": "58b621b924324874a67e92d7626809fd4b72fc079ce909f6da7097654026af00", "signature": false, "impliedFormat": 1}, {"version": "149288ae23bb3b31ffe5cfb7eea669fc6872e41901d60be932af2581601fc70f", "signature": false, "impliedFormat": 1}, {"version": "01a0fd262c8fdc6c91078255c4fe2f8602fd4fe4c753b2eae88537585b21dddf", "signature": false, "impliedFormat": 1}, {"version": "deb69e6754a61784daadc35b318544b0aa69048ebfb142073c62b7f46bb1d5d0", "signature": false, "impliedFormat": 1}, {"version": "60eef77c9b5cec20516907628f849845975a8137773ddb0bcb53fc2ea7d28870", "signature": false, "impliedFormat": 1}, {"version": "67bcdcbd8cece34ae28180c636908af1b118fa9603d0d4b7dea877156d4de519", "signature": false, "impliedFormat": 1}, {"version": "5a1c2cee26d1f8d9bb15b334f5b2df7de27a3944bff9ccf71d3b69c588612bda", "signature": false, "impliedFormat": 1}, {"version": "a04d60b205af1f28461f3d2f5a8222ec2d8af54d436bc53a0460756e07e4207d", "signature": false, "impliedFormat": 1}, {"version": "14c85d4debb2e0c8939f81b85cb9ab4543f70c8fe53be5fb5caf1192677c8ca4", "signature": false, "impliedFormat": 1}, {"version": "c507cdc9757c048620ff08a85b9cf6278598eb1738d729fdbfa1e387a35e639a", "signature": false, "impliedFormat": 1}, {"version": "4a4807c3096f49a463476742e3b5d23ccf0e087e43c017891c332ae5b8ad667d", "signature": false, "impliedFormat": 1}, {"version": "c611af558c5d19fa477f1b03ceac7b0ae28fe5ad4f8bc61e8ad64c46f97e86e2", "signature": false, "impliedFormat": 1}, {"version": "0cec41f583efa1f1033a4d546d926ee949756f19040bb65807c5a3ab6f3b8449", "signature": false, "impliedFormat": 1}, {"version": "73b1eda15491d4f3052d6fac202190e76d6453fce832034bd29901cb198448b9", "signature": false, "impliedFormat": 1}, {"version": "08c66989383183f3d7c43346617c8f466bef28f1e3eb4da829316d548cdbdf80", "signature": false, "impliedFormat": 1}, {"version": "1f283476bbeaa589fe644fe6ba9da223baf118ecd4756863deae7362b246aff3", "signature": false, "impliedFormat": 1}, {"version": "0a8f91ace4d1803eb2a50079c9e233fb262b0027d19aa250eb7ecbf6319e52d6", "signature": false, "impliedFormat": 1}, {"version": "65bab52912be03b374ab591d73ee40aff3a465ac20bc0f2024b4c80ac5ce8397", "signature": false, "impliedFormat": 1}, {"version": "6a647bf0620a4a7777527c688c62636a503e8b4d5e680037503066dd2af6d0dd", "signature": false, "impliedFormat": 1}, {"version": "f1466e4d708815280c849956a506e132b7dc243907b9c8e07d52862e32dfcd91", "signature": false, "impliedFormat": 1}, {"version": "cb4b99f8e47f57df841c95fcb1afc28488a2b5442e3524f6261e611b86105331", "signature": false, "impliedFormat": 1}, {"version": "7c5fc61fc40a9f3aa3a09fd867536ff94a93b16f4ae99f1fb748fae6e13ae8bc", "signature": false, "impliedFormat": 1}, {"version": "473d9ca5b242db0471d418336f410922eadd290679914f37ef21ee26dbeee2b4", "signature": false, "impliedFormat": 1}, {"version": "2ffeb6ad0b074d1cfa3dc9671dad062b08129d1e8a8988b727dd2ce9fd4298d8", "signature": false, "impliedFormat": 1}, {"version": "fa1d4332a68d84300895af592811f65f5f1d725ed0664f17d5c215a63408b6b4", "signature": false, "impliedFormat": 1}, {"version": "7a09768c36d8b7d8e44b6085031712559362b28a54f133b803bed19408676cdf", "signature": false, "impliedFormat": 1}, {"version": "f0b807278b2619fbe0acb9833bd285acabbf31da3592da949f4668a2e4bcbcf0", "signature": false, "impliedFormat": 1}, {"version": "bc6419ca69c35169941d9d0f7a15c483a82ac601c3448257f29a1123bc2399e1", "signature": false, "impliedFormat": 1}, {"version": "45f530610645ca6e25621ce8e7b3cf6c28cd5988871bc68b3772488bd8e45c25", "signature": false, "impliedFormat": 1}, {"version": "2d3e715ca71765b491ae8bd76257e8ccfe97201c605dadc4e6532bb62e4f6eee", "signature": false, "impliedFormat": 1}, {"version": "c519419c11e61347181ba3b77e8d560d8cc7614b6231cacefe206b41474792d4", "signature": false, "impliedFormat": 1}, {"version": "24823640771cf82865c3b1cb48a8a88119b69e56aef594171cc0570f35f60b8a", "signature": false, "impliedFormat": 1}, {"version": "30398045bda704d03d23e78a37095aa56e69ab2dd8bb7304b15df9e183b9800a", "signature": false, "impliedFormat": 1}, {"version": "9a816fe54ea736ecf02b6865c10157724cdb5ba3f57ead02d9216b2dd4bd0d5f", "signature": false, "impliedFormat": 1}, {"version": "a67582f2933f5b6faebba3484c99e78b529aa016369b768021726e400c93ddb8", "signature": false, "impliedFormat": 1}, {"version": "96cd7367cc076d36d9f10cbe34b91e94467caf9b64a7a0fe1c4f6c8287e0a1b5", "signature": false, "impliedFormat": 1}, {"version": "17c7be2c601e4b7e6292932997e491ff874418bef9ee6137e69ea6ef497e0e5d", "signature": false, "impliedFormat": 1}, {"version": "eb7ed3b69718cf40c1ab8ce9a0e917819e0ef0b7480ba2890cddbb94a1386b10", "signature": false, "impliedFormat": 1}, {"version": "7a7cec0720ee6d20e08fa9def697b149a94db1763bbec6e1ab5da8d7726ebddc", "signature": false, "impliedFormat": 1}, {"version": "c024677c477a9dd20e7aba894c2f3e6ef81c4076af932a7fc00c210543cd53bc", "signature": false, "impliedFormat": 1}, {"version": "7f31b6e6d0c03a34e462fdaaf2f7ab6daf85bed51fcaa61ee794aaa1c9b890ac", "signature": false, "impliedFormat": 1}, {"version": "47d4a955185abae52afbb2bda35dfa6eea175662d25c94410cb214d68f93a80c", "signature": false}, {"version": "eae0f0bd272650a83a592c6000b7733520eb5aa42efcc8ab62d47dc1acb5ee78", "signature": false, "impliedFormat": 99}, {"version": "0f321818befa1f90aa797afdc64c6cf1652c133eca86d5dd6c99548a8bdaf51e", "signature": false, "impliedFormat": 99}, {"version": "481c19996de65c72ebf9d7e8f9952298072d4c30db6475cd4231df8e2f2d09b1", "signature": false, "impliedFormat": 99}, {"version": "406be199d4f2b0c74810de31b45fecb333d0c04f6275d6e9578067cced0f3b8c", "signature": false, "impliedFormat": 99}, {"version": "2401f5d61e82a35b49f8e89fe5e826682d82273714d86454b5d8ff74838efa7a", "signature": false, "impliedFormat": 99}, {"version": "87ba3ab05e8e23618cd376562d0680ddd0c00a29569ddddb053b9862ef73e159", "signature": false, "impliedFormat": 99}, {"version": "2b4276dde46aa2faf0dd86119999c76b81e6488cd6b0d0fcf9fb985769cd11c0", "signature": false, "impliedFormat": 99}, {"version": "88247402edb737af32da5c7f69ff80e66e831262065b7f0feb32ea8293260d22", "signature": false, "impliedFormat": 99}, {"version": "5ecea63968444d55f7c3cf677cbec9525db9229953b34f06be0386a24b0fffd2", "signature": false, "impliedFormat": 99}, {"version": "b50ee4bde16b52ecb08e2407dca49a5649b38e046e353485335aa024f6efb8ef", "signature": false, "impliedFormat": 99}, {"version": "a3d603c46b55d51493799241b8a456169d36301cc926ff72c75f5480e7eb25bf", "signature": false, "impliedFormat": 99}, {"version": "324869b470cb6aa2bc54e8fb057b90d972f90d24c7059c027869b2587efe01aa", "signature": false, "impliedFormat": 99}, {"version": "eedf3960076a5b33a84cd28476e035983b7c71a9a8728f904d8e17e824259a8e", "signature": false, "impliedFormat": 99}, {"version": "d7058b71aae678b2a276ecbeb7a9f0fdf4d57ccf0831f572686ba43be26b8ef7", "signature": false, "impliedFormat": 99}, {"version": "ed57d309b3d74719526912a9952a1ff72ca38fe0243c51701a49976c771cbb6c", "signature": false, "impliedFormat": 99}, {"version": "9e0b04a9586f6f7bcf2cd160a21630643957553fc49197e8e10d8cca2d163610", "signature": false, "impliedFormat": 99}, {"version": "2df4f080ac546741f1963d7b8a9cc74f739fbdedf8912c0bad34edeb99b64db6", "signature": false, "impliedFormat": 99}, {"version": "4b62ccc8a561ee6f6124dec319721c064456d5888a66a31a5f2691d33aa93a5f", "signature": false, "impliedFormat": 99}, {"version": "430fa8183f4a42a776af25dac202a5e254598ff5b46aa3016165570ea174b09e", "signature": false, "impliedFormat": 99}, {"version": "7cd3e62c5a8cc665104736a6b6d8b360d97ebc9926e2ed98ac23dca8232e210b", "signature": false, "impliedFormat": 99}, {"version": "ff434ea45f1fc18278b1fc25d3269ec58ce110e602ebafba629980543c3d6999", "signature": false, "impliedFormat": 99}, {"version": "d39e6644c8b9854b16e6810f6fc96c2bf044e2fd200da65a17e557c1bac51bc4", "signature": false, "impliedFormat": 99}, {"version": "cd6f4c96cb17765ebc8f0cc96637235385876f1141fa749fc145f29e0932fc2b", "signature": false, "impliedFormat": 99}, {"version": "45ea8224ec8fc3787615fc548677d6bf6d7cec4251f864a6c09fc86dbdb2cd5d", "signature": false, "impliedFormat": 99}, {"version": "3347361f2bf9befc42c807101f43f4d7ea4960294fb8d92a5dbf761d0ca38d71", "signature": false, "impliedFormat": 99}, {"version": "0bbc9eb3b65e320a97c4a1cc8ee5069b86048c4b3dd12ac974c7a1a6d8b6fb36", "signature": false, "impliedFormat": 99}, {"version": "68dc445224378e9b650c322f5753b371cccbeca078e5293cbc54374051d62734", "signature": false, "impliedFormat": 99}, {"version": "93340b1999275b433662eedd4b1195b22f2df3a8eb7e9d1321e5a06c5576417c", "signature": false, "impliedFormat": 99}, {"version": "cbcdb55ee4aafef7154e004b8bf3131550d92e1c2e905b037b87c427a9aa2a0f", "signature": false, "impliedFormat": 99}, {"version": "37fcf5a0823c2344a947d4c0e50cc63316156f1e6bc0f0c6749e099642d286b1", "signature": false, "impliedFormat": 99}, {"version": "2d2f9018356acf6234cd08669a94b67de89f4df559c65bf52c8c7e3d54eea16b", "signature": false, "impliedFormat": 99}, {"version": "1b50e65f1fbcf48850f91b0bc6ff8c61e6fa2e2e64dd2134a087c40fcfa84e28", "signature": false, "impliedFormat": 99}, {"version": "3736846e55c2a2291b0e4b8b0cb875d329b0b190367323f55a5ab58ee9c8406c", "signature": false, "impliedFormat": 99}, {"version": "f86c6ba182a8b3e2042a61b7e4740413ddca1b68ed72d95758355d53dac232d4", "signature": false, "impliedFormat": 99}, {"version": "33aab7e0f4bf0f7c016e98fb8ea1a05b367fedb2785025c7fa628d91f93818cc", "signature": false, "impliedFormat": 99}, {"version": "20cb0921e0f2580cb2878b4379eedab15a7013197a1126a3df34ea7838999039", "signature": false, "impliedFormat": 99}, {"version": "791e064fad56d73dc5c6b33473e90fec271ae19b7765d245f876d8586c22d3e2", "signature": false}, {"version": "942adafab111c439b00821f81607fc7d1b3b193dec2a2724a6ebe23f9c19b19d", "signature": false}, {"version": "d51ad0a8cef192d20824441d512e35f1396cd2150bb9a7c4a0194c935cc64ab7", "signature": false}, {"version": "cf67e3ab470da6609f0ad9d6cf944bf85f8f0437ca8abacd2b91539df4d7a4f2", "signature": false, "impliedFormat": 1}, {"version": "1223adbe8a47efadb4dd6ed12c4f04bd718b1ae8ef09eb7e5ff405c48cb1c582", "signature": false}, {"version": "311792e21f18f0c7dcda7e0fd9f8ef320e93d42f0cb91cb137cb24699856756c", "signature": false}, {"version": "5c105465ca7bd23bcb636b82596d08b078dc39fb905676a5e031a4589ab6271a", "signature": false}, {"version": "ad4dd955765808395367b7500eff97163ca3c4708327d32389e7720b5fb5f5fa", "signature": false}, {"version": "b6a47d8bf987d6fa45135262503e6e804e46e1084cf0824dce832591ca18b50b", "signature": false}, {"version": "b4d1af143e9a79beda5e2ba3bcc62de51d4da25694db25178a7568664700b5ad", "signature": false}, {"version": "c2a761f65adb75731dfde2f9e5fe60f54cb1f8bcaec3bea18e588681979e6fd5", "signature": false}, {"version": "3663595501d1e59d444a101adb52ff3a32c41ba3b81391aebc9a695ee48cd15c", "signature": false}, {"version": "fe251f335bda3546aaed9ae74916bf2690ec766b4c76f3a84ea755eb7af2a3cb", "signature": false}, {"version": "b7dbfd4d88f9e1306bb109ebeaffe49f5c1e05887155833fbd404369806131e2", "signature": false}, {"version": "b8843329b760254aa28c11ccc32247c421ca864fabe4d26b719f987af5ab4390", "signature": false}, {"version": "3d8e9ed341d5c4f753b92ebcecb31ef2304626fca0a2aaf9409d0b9d620bc432", "signature": false}, {"version": "5b0c23f25c45a1228354f5b745beedb9d843213b9dfd8b1ea46901abb4acc063", "signature": false}, {"version": "d8830ed15ebc4570ddb965a48b07a6fc6322cafd9a46a2b8db7edb3467dd9904", "signature": false}, {"version": "2753c1e1e5073b1ab23b2b335787f12c0c2aa2e1a14fc2294649f3ba1cf1d9a1", "signature": false}, {"version": "77557ba98c100478ef7987c4c879f6387aa420047792e925d92eced1e2f8594a", "signature": false}, {"version": "be79d3140e1f8f92d9ec326138e36146cf341cc627ef87072c7e8870c467ea1e", "signature": false}, {"version": "6a127416ebab12629142878a7891264fe12cfddf8fb178c5fb226d1ddba32641", "signature": false}, {"version": "1b051397096b11620e598abf93536104fc7629981c7b38db51f1e5b1d98c929f", "signature": false}, {"version": "5699251ad82eb0f4aad5092e0e266b077621d0ce4a261bef766d23cbb2018873", "signature": false}, {"version": "f1c5794566214fa903bd5caf845bc1efb0b01b98d466dada4f4d501d7d672b03", "signature": false}, {"version": "790a52212fa67bd48f2e93bf04e0028755cdaa63944d6f69ccfebb7bf25f0dc9", "signature": false}, {"version": "1370b45db90fd11e0dc65ef5b436f981db5b29e802cc6de69f4c715c25f170e3", "signature": false}, {"version": "570d58d7a55e3ee8cf1e7657c44ac7ac0317b2e122d5142fd3d52423b14caea4", "signature": false}, {"version": "5430b8ce21106b0c523ba7f4fde1e4a4aa1ab27de5fa3a6b13f9fd0cb530c5dc", "signature": false}, {"version": "0c94c314e6279c44d2a3e9e0fa8d5e97b14596849c85104ddf041d0afe872363", "signature": false}, {"version": "1e6c4ca40a6fd49685292f184cb81ac736e1304f469fd4ea1db6ee69acd45b4b", "signature": false}, {"version": "368958fd0011cd93be99876d6b96b193ea0d411d572230a3bfe809e3380b9c6b", "signature": false}, {"version": "9813e3f82ca80d72c9df4a7c4f0a02567720df121efceabd39e62f50b78fabcf", "signature": false}, {"version": "05db1bdc2823e57fd88a7fca8bda47b185771d7ad5a417a1803431a650147bf5", "signature": false}, {"version": "87412a7561090da7f1397e5dd96c5c6c27bf56f6dbd5ea3c635e3b9c1c40542b", "signature": false}, {"version": "8f523f7781953f49011f12780c79358034f6c2ff22948587f6aa8fa7feb75a65", "signature": false}, {"version": "6dc9854da4d7ee87a19ede292f97090e68c5bdb6cf8c215088596c9d39e3d163", "signature": false}, {"version": "a0a5a3aac6bee5c8a01faf2d59151df6519a237d66dc417eef8eae84e679577c", "signature": false}, {"version": "e93dc745cf2f5bf6d75fca47fe88fa3a304a510191a7e8866621c549a273cd15", "signature": false}, {"version": "abf29ab9eee70d1c4488067535e459738d7f55a01b0e3ba54f10f62bff4c4e10", "signature": false}, {"version": "e41435b5ce73e06de24d701c8dfbef64068b11794ee3d4d0664fd6bb1f5a7d9c", "signature": false}, {"version": "6fa3d5305611778607585f7472983cbb32ae2f8f91b7c3dd60b880dd5506a100", "signature": false}, {"version": "e6b99a670a1b96fb3a7020b5a9fb78255c8dcd243bd38e87ae7c66c06de4795f", "signature": false}, {"version": "5d26d97b9711eca868e7c720362a97cb9d462ae84f8caba6a40c0a7dc22554a7", "signature": false}, {"version": "063a88eeb26fc3167287128bd94cd5e0997bb426b48ce2526f25ab2d09c335cd", "signature": false}, {"version": "1791a0721d066dfaa46f069aafd00e5654d8e71c6032d79014fb3ee2b09584dc", "signature": false}, {"version": "438c615f78de91e6bd424c85b54b0f999ded2c8bff7977dd779442e4b1c704ff", "signature": false}, {"version": "0cd2514a264738ac5f45fa404631d1d13c84009c561c2b478e2fb1932e019983", "signature": false}, {"version": "f931d00d37d70a43cec0d1d93f9ccc4da31cbd40b457de2d1abbad4da812f1bd", "signature": false}, {"version": "ddf082f023b33c4fd5d2bf85f936de218f0c22f8ed5a16d1ecbe046919db6e36", "signature": false}, {"version": "97ce1ef6ed8749658ec29d1804ee521eed9db6f28f67239b5f6e028a7120d477", "signature": false}, {"version": "5fb4b22371b040f240d03444c01a9ab2924cbb775ed4107960f4a011e47192a9", "signature": false}, {"version": "123e42a6bb1499da6631569de239cbeeb0e9b06d6a3635a48d87233bb8d33f4d", "signature": false}, {"version": "3c86b923e602eac290936332d717733c98b42273169ea2f2bb210a9ff6645a65", "signature": false}, {"version": "d0d68d141ccab6ce2078dab0d488096f8d953a5301a6197445d6e9b8cb15c1c2", "signature": false}, {"version": "96d14f21b7652903852eef49379d04dbda28c16ed36468f8c9fa08f7c14c9538", "signature": false, "impliedFormat": 1}, {"version": "916be7d770b0ae0406be9486ac12eb9825f21514961dd050594c4b250617d5a8", "signature": false, "impliedFormat": 1}, {"version": "4cf58cd73f135e59d2268b4b792623bd8cc7ea887d96498f2a64d550beb930bb", "signature": false, "impliedFormat": 1}, {"version": "960a68ced7820108787135bdae5265d2cc4b511b7dcfd5b8f213432a8483daf1", "signature": false, "impliedFormat": 1}, {"version": "2e7ebdc7d8af978c263890bbde991e88d6aa31cc29d46735c9c5f45f0a41243b", "signature": false, "impliedFormat": 1}, {"version": "b57fd1c0a680d220e714b76d83eff51a08670f56efcc5d68abc82f5a2684f0c0", "signature": false, "impliedFormat": 1}, {"version": "8cf121e98669f724256d06bebafec912b92bb042a06d4944f7fb27a56c545109", "signature": false, "impliedFormat": 1}, {"version": "1084565c68b2aed5d6d5cea394799bd688afdf4dc99f4e3615957857c15bb231", "signature": false, "impliedFormat": 1}, {"version": "15fe687c59d62741b4494d5e623d497d55eb38966ecf5bea7f36e48fc3fbe15e", "signature": false, "impliedFormat": 1}, {"version": "2c3b8be03577c98530ef9cb1a76e2c812636a871f367e9edf4c5f3ce702b77f8", "signature": false, "affectsGlobalScope": true, "impliedFormat": 1}], "root": [405, 406, 408, 409, [439, 453], [459, 463], [465, 474], [800, 829], [831, 897], [1484, 1487], [1491, 1506], [1608, 1613], [1616, 1627], [1670, 1712], 2038, [2075, 2077], [2079, 2129]], "options": {"allowJs": true, "composite": false, "declarationMap": false, "emitDeclarationOnly": false, "esModuleInterop": true, "jsx": 1, "module": 99, "skipLibCheck": true, "strict": true, "target": 1, "tsBuildInfoFile": "./.tsbuildinfo"}, "referencedMap": [[2085, 1], [2084, 2], [2086, 3], [2087, 4], [2088, 5], [2090, 6], [2089, 7], [2097, 8], [2091, 9], [2092, 10], [2093, 11], [2094, 12], [2095, 13], [2096, 14], [2098, 15], [2099, 16], [2100, 17], [2101, 18], [2102, 19], [2103, 20], [2104, 21], [2105, 22], [2106, 23], [2083, 24], [2107, 25], [2108, 26], [2110, 27], [2109, 28], [2111, 29], [2112, 30], [2113, 31], [2115, 32], [2116, 33], [2114, 34], [2117, 35], [2118, 36], [2121, 37], [2120, 38], [2122, 39], [2119, 40], [2123, 41], [2125, 42], [2124, 43], [2126, 44], [2127, 45], [2128, 46], [2129, 47], [406, 48], [1499, 49], [1498, 50], [1502, 51], [1503, 51], [1504, 52], [409, 53], [440, 54], [1501, 55], [1505, 56], [1506, 57], [1612, 58], [1613, 59], [1610, 58], [1611, 60], [441, 53], [1608, 61], [1609, 62], [442, 53], [444, 63], [445, 64], [1623, 65], [443, 53], [1616, 66], [1617, 67], [1618, 48], [1619, 48], [1620, 48], [1621, 48], [1622, 48], [1493, 68], [1624, 57], [446, 53], [1627, 69], [1625, 62], [447, 53], [1671, 70], [1672, 71], [1670, 72], [1673, 73], [1674, 62], [1675, 62], [1678, 74], [1676, 62], [1677, 75], [448, 53], [1679, 76], [450, 77], [451, 78], [1681, 79], [1680, 55], [449, 80], [1682, 81], [1683, 82], [1495, 83], [1492, 84], [819, 85], [1684, 57], [1685, 86], [820, 87], [818, 85], [817, 88], [816, 89], [463, 85], [462, 85], [460, 85], [459, 90], [461, 91], [826, 92], [824, 93], [821, 94], [825, 95], [801, 62], [802, 96], [472, 97], [800, 98], [470, 99], [469, 100], [473, 101], [474, 100], [467, 102], [471, 97], [810, 48], [815, 103], [814, 62], [812, 104], [813, 105], [811, 106], [828, 107], [465, 48], [466, 48], [468, 108], [452, 53], [453, 109], [1687, 48], [1686, 57], [1688, 110], [1689, 48], [833, 111], [831, 112], [1690, 113], [832, 114], [836, 115], [835, 116], [834, 113], [1691, 117], [823, 53], [829, 118], [1693, 62], [1694, 62], [1692, 57], [1695, 48], [1696, 62], [839, 119], [838, 120], [845, 121], [846, 122], [844, 112], [1698, 123], [1699, 124], [840, 125], [841, 53], [850, 126], [849, 127], [847, 124], [1701, 128], [1702, 129], [1700, 130], [1703, 131], [1704, 132], [1705, 133], [842, 134], [843, 135], [1697, 57], [889, 136], [888, 137], [887, 138], [886, 139], [866, 140], [867, 141], [860, 142], [852, 143], [865, 144], [893, 145], [891, 146], [890, 147], [892, 148], [1706, 149], [878, 150], [877, 151], [873, 152], [876, 153], [871, 154], [870, 154], [875, 154], [874, 155], [869, 156], [872, 152], [848, 48], [885, 157], [879, 158], [882, 159], [884, 160], [880, 161], [881, 162], [868, 48], [863, 48], [864, 163], [837, 53], [851, 164], [827, 53], [822, 53], [1708, 62], [1707, 57], [1710, 165], [1711, 166], [894, 80], [1712, 62], [2075, 167], [2077, 168], [2076, 167], [2079, 169], [2038, 170], [2080, 171], [895, 80], [883, 80], [1496, 62], [854, 172], [858, 173], [855, 174], [859, 175], [857, 48], [853, 48], [856, 176], [2081, 177], [1485, 178], [1484, 179], [1709, 62], [861, 112], [2082, 180], [862, 62], [1497, 62], [1626, 181], [1494, 182], [804, 183], [805, 184], [809, 185], [807, 48], [808, 186], [803, 48], [806, 183], [1500, 187], [897, 188], [896, 189], [439, 190], [1491, 191], [405, 192], [906, 193], [905, 48], [908, 194], [907, 195], [918, 196], [911, 197], [919, 198], [916, 196], [920, 199], [914, 196], [915, 200], [917, 201], [913, 202], [912, 203], [921, 204], [909, 205], [910, 206], [900, 48], [901, 207], [924, 208], [922, 62], [923, 209], [926, 210], [925, 211], [903, 212], [902, 213], [904, 214], [2046, 215], [2048, 216], [2049, 217], [2050, 218], [2045, 48], [2047, 48], [2041, 219], [2042, 219], [2043, 220], [2053, 221], [2054, 219], [2055, 219], [2056, 222], [2057, 219], [2058, 219], [2059, 219], [2060, 219], [2061, 219], [2052, 219], [2062, 223], [2063, 221], [2064, 224], [2065, 224], [2066, 219], [2067, 225], [2068, 219], [2069, 226], [2070, 219], [2071, 219], [2073, 219], [2044, 48], [2074, 227], [2072, 62], [2051, 228], [2039, 62], [2040, 229], [1713, 62], [1714, 62], [1715, 62], [1716, 62], [1718, 62], [1717, 62], [1719, 62], [1725, 62], [1720, 62], [1722, 62], [1721, 62], [1723, 62], [1724, 62], [1726, 62], [1727, 62], [1730, 62], [1728, 62], [1729, 62], [1731, 62], [1732, 62], [1733, 62], [1734, 62], [1736, 62], [1735, 62], [1737, 62], [1738, 62], [1741, 62], [1739, 62], [1740, 62], [1742, 62], [1743, 62], [1744, 62], [1745, 62], [1768, 62], [1769, 62], [1770, 62], [1771, 62], [1746, 62], [1747, 62], [1748, 62], [1749, 62], [1750, 62], [1751, 62], [1752, 62], [1753, 62], [1754, 62], [1755, 62], [1756, 62], [1757, 62], [1763, 62], [1758, 62], [1760, 62], [1759, 62], [1761, 62], [1762, 62], [1764, 62], [1765, 62], [1766, 62], [1767, 62], [1772, 62], [1773, 62], [1774, 62], [1775, 62], [1776, 62], [1777, 62], [1778, 62], [1779, 62], [1780, 62], [1781, 62], [1782, 62], [1783, 62], [1784, 62], [1785, 62], [1786, 62], [1787, 62], [1788, 62], [1791, 62], [1789, 62], [1790, 62], [1792, 62], [1794, 62], [1793, 62], [1798, 62], [1796, 62], [1797, 62], [1795, 62], [1799, 62], [1800, 62], [1801, 62], [1802, 62], [1803, 62], [1804, 62], [1805, 62], [1806, 62], [1807, 62], [1808, 62], [1809, 62], [1810, 62], [1812, 62], [1811, 62], [1813, 62], [1815, 62], [1814, 62], [1816, 62], [1818, 62], [1817, 62], [1819, 62], [1820, 62], [1821, 62], [1822, 62], [1823, 62], [1824, 62], [1825, 62], [1826, 62], [1827, 62], [1828, 62], [1829, 62], [1830, 62], [1831, 62], [1832, 62], [1833, 62], [1834, 62], [1836, 62], [1835, 62], [1837, 62], [1838, 62], [1839, 62], [1840, 62], [1841, 62], [1843, 62], [1842, 62], [1844, 62], [1845, 62], [1846, 62], [1847, 62], [1848, 62], [1849, 62], [1850, 62], [1852, 62], [1851, 62], [1853, 62], [1854, 62], [1855, 62], [1856, 62], [1857, 62], [1858, 62], [1859, 62], [1860, 62], [1861, 62], [1862, 62], [1863, 62], [1864, 62], [1865, 62], [1866, 62], [1867, 62], [1868, 62], [1869, 62], [1870, 62], [1871, 62], [1872, 62], [1873, 62], [1874, 62], [1879, 62], [1875, 62], [1876, 62], [1877, 62], [1878, 62], [1880, 62], [1881, 62], [1882, 62], [1884, 62], [1883, 62], [1885, 62], [1886, 62], [1887, 62], [1888, 62], [1890, 62], [1889, 62], [1891, 62], [1892, 62], [1893, 62], [1894, 62], [1895, 62], [1896, 62], [1897, 62], [1901, 62], [1898, 62], [1899, 62], [1900, 62], [1902, 62], [1903, 62], [1904, 62], [1906, 62], [1905, 62], [1907, 62], [1908, 62], [1909, 62], [1910, 62], [1911, 62], [1912, 62], [1913, 62], [1914, 62], [1915, 62], [1916, 62], [1917, 62], [1918, 62], [1920, 62], [1919, 62], [1921, 62], [1922, 62], [1924, 62], [1923, 62], [2037, 230], [1925, 62], [1926, 62], [1927, 62], [1928, 62], [1929, 62], [1930, 62], [1932, 62], [1931, 62], [1933, 62], [1934, 62], [1935, 62], [1936, 62], [1939, 62], [1937, 62], [1938, 62], [1941, 62], [1940, 62], [1942, 62], [1943, 62], [1944, 62], [1946, 62], [1945, 62], [1947, 62], [1948, 62], [1949, 62], [1950, 62], [1951, 62], [1952, 62], [1953, 62], [1954, 62], [1955, 62], [1956, 62], [1958, 62], [1957, 62], [1959, 62], [1960, 62], [1961, 62], [1963, 62], [1962, 62], [1964, 62], [1965, 62], [1967, 62], [1966, 62], [1968, 62], [1970, 62], [1969, 62], [1971, 62], [1972, 62], [1973, 62], [1974, 62], [1975, 62], [1976, 62], [1977, 62], [1978, 62], [1979, 62], [1980, 62], [1981, 62], [1982, 62], [1983, 62], [1984, 62], [1985, 62], [1986, 62], [1987, 62], [1989, 62], [1988, 62], [1990, 62], [1991, 62], [1992, 62], [1993, 62], [1994, 62], [1996, 62], [1995, 62], [1997, 62], [1998, 62], [1999, 62], [2000, 62], [2001, 62], [2002, 62], [2003, 62], [2004, 62], [2005, 62], [2006, 62], [2007, 62], [2008, 62], [2009, 62], [2010, 62], [2011, 62], [2012, 62], [2013, 62], [2014, 62], [2015, 62], [2016, 62], [2017, 62], [2018, 62], [2019, 62], [2020, 62], [2023, 62], [2021, 62], [2022, 62], [2024, 62], [2025, 62], [2027, 62], [2026, 62], [2028, 62], [2029, 62], [2030, 62], [2031, 62], [2032, 62], [2034, 62], [2033, 62], [2035, 62], [2036, 62], [475, 62], [476, 62], [477, 62], [478, 62], [480, 62], [479, 62], [481, 62], [487, 62], [482, 62], [484, 62], [483, 62], [485, 62], [486, 62], [488, 62], [489, 62], [492, 62], [490, 62], [491, 62], [493, 62], [494, 62], [495, 62], [496, 62], [498, 62], [497, 62], [499, 62], [500, 62], [503, 62], [501, 62], [502, 62], [504, 62], [505, 62], [506, 62], [507, 62], [530, 62], [531, 62], [532, 62], [533, 62], [508, 62], [509, 62], [510, 62], [511, 62], [512, 62], [513, 62], [514, 62], [515, 62], [516, 62], [517, 62], [518, 62], [519, 62], [525, 62], [520, 62], [522, 62], [521, 62], [523, 62], [524, 62], [526, 62], [527, 62], [528, 62], [529, 62], [534, 62], [535, 62], [536, 62], [537, 62], [538, 62], [539, 62], [540, 62], [541, 62], [542, 62], [543, 62], [544, 62], [545, 62], [546, 62], [547, 62], [548, 62], [549, 62], [550, 62], [553, 62], [551, 62], [552, 62], [554, 62], [556, 62], [555, 62], [560, 62], [558, 62], [559, 62], [557, 62], [561, 62], [562, 62], [563, 62], [564, 62], [565, 62], [566, 62], [567, 62], [568, 62], [569, 62], [570, 62], [571, 62], [572, 62], [574, 62], [573, 62], [575, 62], [577, 62], [576, 62], [578, 62], [580, 62], [579, 62], [581, 62], [582, 62], [583, 62], [584, 62], [585, 62], [586, 62], [587, 62], [588, 62], [589, 62], [590, 62], [591, 62], [592, 62], [593, 62], [594, 62], [595, 62], [596, 62], [598, 62], [597, 62], [599, 62], [600, 62], [601, 62], [602, 62], [603, 62], [605, 62], [604, 62], [606, 62], [607, 62], [608, 62], [609, 62], [610, 62], [611, 62], [612, 62], [614, 62], [613, 62], [615, 62], [616, 62], [617, 62], [618, 62], [619, 62], [620, 62], [621, 62], [622, 62], [623, 62], [624, 62], [625, 62], [626, 62], [627, 62], [628, 62], [629, 62], [630, 62], [631, 62], [632, 62], [633, 62], [634, 62], [635, 62], [636, 62], [641, 62], [637, 62], [638, 62], [639, 62], [640, 62], [642, 62], [643, 62], [644, 62], [646, 62], [645, 62], [647, 62], [648, 62], [649, 62], [650, 62], [652, 62], [651, 62], [653, 62], [654, 62], [655, 62], [656, 62], [657, 62], [658, 62], [659, 62], [663, 62], [660, 62], [661, 62], [662, 62], [664, 62], [665, 62], [666, 62], [668, 62], [667, 62], [669, 62], [670, 62], [671, 62], [672, 62], [673, 62], [674, 62], [675, 62], [676, 62], [677, 62], [678, 62], [679, 62], [680, 62], [682, 62], [681, 62], [683, 62], [684, 62], [686, 62], [685, 62], [799, 231], [687, 62], [688, 62], [689, 62], [690, 62], [691, 62], [692, 62], [694, 62], [693, 62], [695, 62], [696, 62], [697, 62], [698, 62], [701, 62], [699, 62], [700, 62], [703, 62], [702, 62], [704, 62], [705, 62], [706, 62], [708, 62], [707, 62], [709, 62], [710, 62], [711, 62], [712, 62], [713, 62], [714, 62], [715, 62], [716, 62], [717, 62], [718, 62], [720, 62], [719, 62], [721, 62], [722, 62], [723, 62], [725, 62], [724, 62], [726, 62], [727, 62], [729, 62], [728, 62], [730, 62], [732, 62], [731, 62], [733, 62], [734, 62], [735, 62], [736, 62], [737, 62], [738, 62], [739, 62], [740, 62], [741, 62], [742, 62], [743, 62], [744, 62], [745, 62], [746, 62], [747, 62], [748, 62], [749, 62], [751, 62], [750, 62], [752, 62], [753, 62], [754, 62], [755, 62], [756, 62], [758, 62], [757, 62], [759, 62], [760, 62], [761, 62], [762, 62], [763, 62], [764, 62], [765, 62], [766, 62], [767, 62], [768, 62], [769, 62], [770, 62], [771, 62], [772, 62], [773, 62], [774, 62], [775, 62], [776, 62], [777, 62], [778, 62], [779, 62], [780, 62], [781, 62], [782, 62], [785, 62], [783, 62], [784, 62], [786, 62], [787, 62], [789, 62], [788, 62], [790, 62], [791, 62], [792, 62], [793, 62], [794, 62], [796, 62], [795, 62], [797, 62], [798, 62], [1607, 232], [1606, 233], [1210, 234], [1209, 48], [1211, 235], [1204, 236], [1203, 48], [1205, 237], [1207, 238], [1206, 48], [1208, 239], [1213, 240], [1212, 48], [1214, 241], [1064, 242], [1061, 48], [1065, 243], [1070, 244], [1069, 48], [1071, 245], [1073, 246], [1072, 48], [1074, 247], [1107, 248], [1106, 48], [1108, 249], [1110, 250], [1109, 48], [1111, 251], [1113, 252], [1112, 48], [1114, 253], [1120, 254], [1119, 48], [1121, 255], [1123, 256], [1122, 48], [1124, 257], [1129, 258], [1128, 48], [1130, 259], [1126, 260], [1125, 48], [1127, 261], [1132, 262], [1131, 48], [1133, 263], [1140, 264], [1139, 48], [1141, 265], [1053, 266], [1052, 48], [1054, 267], [1051, 268], [1050, 48], [1135, 269], [1137, 62], [1134, 48], [1136, 270], [1138, 271], [1158, 272], [1157, 48], [1159, 273], [1143, 274], [1142, 48], [1144, 275], [1146, 276], [1145, 48], [1147, 277], [1149, 278], [1148, 48], [1150, 279], [1152, 280], [1151, 48], [1153, 281], [1155, 282], [1154, 48], [1156, 283], [1163, 284], [1162, 48], [1164, 285], [1076, 286], [1075, 48], [1077, 287], [1166, 288], [1165, 48], [1167, 289], [1357, 62], [1358, 290], [1169, 291], [1168, 48], [1170, 292], [1172, 293], [1171, 294], [1173, 295], [1174, 296], [1175, 297], [1190, 298], [1189, 48], [1191, 299], [1177, 300], [1176, 48], [1178, 301], [1180, 302], [1179, 48], [1181, 303], [1183, 304], [1182, 48], [1184, 305], [1193, 306], [1192, 48], [1194, 307], [1196, 308], [1195, 48], [1197, 309], [1201, 310], [1200, 48], [1202, 311], [1216, 312], [1215, 48], [1217, 313], [1117, 314], [1118, 315], [1222, 316], [1221, 48], [1223, 317], [1228, 318], [1227, 48], [1229, 319], [1231, 320], [1230, 321], [1225, 322], [1224, 48], [1226, 323], [1233, 324], [1232, 48], [1234, 325], [1236, 326], [1235, 48], [1237, 327], [1239, 328], [1238, 48], [1240, 329], [1244, 330], [1245, 48], [1246, 331], [1242, 332], [1241, 48], [1243, 333], [1248, 334], [1247, 48], [1249, 335], [1056, 336], [1055, 48], [1057, 337], [1251, 338], [1250, 48], [1252, 339], [1257, 340], [1256, 48], [1258, 341], [1254, 342], [1253, 48], [1255, 343], [1267, 344], [1266, 345], [1265, 48], [1261, 346], [1260, 347], [1259, 48], [1220, 348], [1219, 349], [1218, 48], [1264, 350], [1263, 351], [1262, 48], [1049, 352], [1161, 353], [1160, 48], [1270, 354], [1269, 355], [1268, 48], [1273, 356], [1272, 357], [1271, 48], [1294, 358], [1293, 359], [1292, 48], [1282, 360], [1281, 361], [1280, 48], [1276, 362], [1275, 363], [1274, 48], [1285, 364], [1284, 365], [1283, 48], [1279, 366], [1278, 367], [1277, 48], [1288, 368], [1287, 369], [1286, 48], [1291, 370], [1290, 371], [1289, 48], [1297, 372], [1296, 373], [1295, 48], [1308, 374], [1307, 375], [1306, 48], [1300, 376], [1299, 377], [1298, 48], [1302, 378], [1301, 379], [1311, 380], [1310, 381], [1309, 48], [1188, 382], [1187, 383], [1186, 48], [1185, 48], [1315, 384], [1314, 385], [1313, 48], [1312, 386], [1319, 387], [1318, 388], [1317, 48], [1045, 389], [1323, 390], [1322, 391], [1321, 48], [1326, 392], [1325, 393], [1324, 48], [1060, 394], [1059, 395], [1058, 48], [1305, 396], [1304, 397], [1303, 48], [1100, 398], [1103, 399], [1101, 400], [1102, 48], [1098, 401], [1097, 402], [1096, 62], [1334, 403], [1333, 404], [1332, 48], [1331, 405], [1327, 406], [1330, 407], [1328, 62], [1329, 408], [1337, 409], [1336, 410], [1335, 48], [1340, 411], [1339, 412], [1338, 48], [1344, 413], [1343, 414], [1342, 48], [1341, 415], [1347, 416], [1346, 417], [1345, 48], [1199, 418], [1198, 314], [1353, 419], [1352, 420], [1351, 48], [1350, 421], [1349, 48], [1348, 62], [1361, 422], [1360, 423], [1359, 48], [1356, 424], [1355, 425], [1354, 48], [1365, 426], [1364, 427], [1363, 48], [1371, 428], [1370, 429], [1369, 48], [1374, 430], [1373, 431], [1372, 48], [1377, 432], [1375, 433], [1376, 294], [1400, 434], [1398, 435], [1397, 48], [1399, 62], [1380, 436], [1379, 437], [1378, 48], [1383, 438], [1382, 439], [1381, 48], [1386, 440], [1385, 441], [1384, 48], [1389, 442], [1388, 443], [1387, 48], [1392, 444], [1391, 445], [1390, 48], [1396, 446], [1394, 447], [1393, 48], [1395, 62], [1464, 448], [1460, 449], [1465, 450], [1039, 451], [1040, 48], [1466, 48], [1463, 452], [1461, 453], [1462, 454], [1043, 48], [1041, 455], [1475, 456], [1482, 48], [1480, 48], [899, 48], [1483, 457], [1476, 48], [1458, 458], [1457, 459], [1467, 460], [1472, 48], [1042, 48], [1481, 48], [1471, 48], [1473, 461], [1474, 462], [1479, 463], [1469, 464], [1470, 465], [1459, 466], [1477, 48], [1478, 48], [1044, 48], [1048, 467], [1047, 468], [1046, 48], [1402, 469], [1401, 470], [1405, 471], [1404, 472], [1403, 48], [1441, 473], [1440, 474], [1439, 48], [1429, 475], [1428, 476], [1427, 48], [1408, 477], [1407, 478], [1406, 48], [1411, 479], [1410, 480], [1409, 48], [1414, 481], [1413, 482], [1412, 48], [1438, 483], [1437, 484], [1436, 48], [1417, 485], [1416, 486], [1415, 48], [1426, 487], [1425, 488], [1421, 48], [1420, 489], [1418, 490], [1419, 48], [1432, 491], [1431, 492], [1430, 48], [1435, 493], [1434, 494], [1433, 48], [1447, 495], [1446, 496], [1445, 48], [1444, 497], [1443, 498], [1442, 48], [1450, 499], [1449, 500], [1448, 48], [1453, 501], [1452, 502], [1451, 48], [1456, 503], [1455, 504], [1454, 48], [1424, 505], [1423, 506], [1422, 48], [1368, 507], [1367, 508], [1366, 48], [1362, 509], [1116, 510], [1068, 511], [1067, 512], [1066, 48], [1105, 513], [1104, 514], [1316, 515], [1320, 62], [1099, 514], [1063, 516], [973, 48], [978, 517], [975, 518], [974, 519], [977, 520], [976, 519], [929, 521], [930, 522], [931, 523], [928, 524], [927, 62], [934, 525], [935, 526], [983, 527], [984, 48], [985, 528], [951, 529], [952, 530], [1001, 48], [1002, 531], [953, 525], [954, 532], [1023, 533], [1020, 48], [1021, 534], [1022, 535], [1024, 536], [986, 537], [987, 538], [936, 539], [1468, 540], [988, 541], [989, 542], [946, 543], [938, 48], [949, 544], [950, 545], [937, 48], [947, 540], [948, 546], [959, 525], [960, 547], [1010, 548], [1013, 549], [1016, 48], [1017, 48], [1014, 48], [1015, 550], [1008, 48], [1011, 48], [1012, 48], [1009, 551], [955, 525], [956, 552], [957, 525], [958, 553], [971, 48], [972, 554], [979, 555], [980, 556], [1027, 557], [1026, 558], [1028, 48], [1030, 559], [1025, 560], [1031, 561], [1029, 540], [1038, 562], [1007, 563], [1006, 62], [1005, 543], [962, 564], [961, 525], [964, 565], [963, 525], [1019, 566], [1018, 48], [966, 567], [965, 525], [968, 568], [967, 525], [982, 569], [981, 525], [1034, 570], [1036, 571], [1033, 572], [1035, 48], [1032, 560], [933, 573], [932, 543], [991, 574], [990, 575], [940, 576], [944, 525], [943, 577], [945, 578], [941, 579], [939, 579], [942, 580], [1004, 581], [1003, 582], [970, 583], [969, 525], [1000, 584], [999, 48], [996, 585], [995, 586], [993, 48], [994, 587], [992, 48], [998, 588], [997, 48], [1037, 48], [898, 62], [1062, 62], [358, 48], [1095, 589], [1091, 590], [1078, 48], [1094, 591], [1087, 592], [1085, 593], [1084, 593], [1083, 592], [1080, 593], [1081, 592], [1089, 594], [1082, 593], [1079, 592], [1086, 593], [1092, 595], [1093, 596], [1088, 597], [1090, 593], [1632, 598], [1638, 599], [1640, 600], [1633, 601], [1641, 602], [1639, 603], [1642, 48], [1634, 604], [1635, 602], [1643, 605], [1644, 598], [1647, 606], [1636, 607], [1645, 608], [1646, 609], [1637, 610], [416, 611], [412, 612], [419, 613], [414, 614], [415, 48], [417, 611], [413, 614], [410, 48], [418, 614], [411, 48], [432, 615], [438, 616], [429, 617], [437, 62], [430, 615], [431, 618], [422, 617], [420, 619], [436, 620], [433, 619], [435, 617], [434, 619], [428, 619], [427, 619], [421, 617], [423, 621], [425, 617], [426, 617], [424, 617], [1628, 48], [2130, 48], [1629, 622], [136, 623], [137, 623], [138, 624], [97, 625], [139, 626], [140, 627], [141, 628], [92, 48], [95, 629], [93, 48], [94, 48], [142, 630], [143, 631], [144, 632], [145, 633], [146, 634], [147, 635], [148, 635], [150, 48], [149, 636], [151, 637], [152, 638], [153, 639], [135, 640], [96, 48], [154, 641], [155, 642], [156, 643], [188, 644], [157, 645], [158, 646], [159, 647], [160, 648], [161, 649], [162, 650], [163, 651], [164, 652], [165, 653], [166, 654], [167, 654], [168, 655], [169, 48], [170, 656], [172, 657], [171, 658], [173, 659], [174, 660], [175, 661], [176, 662], [177, 663], [178, 664], [179, 665], [180, 666], [181, 667], [182, 668], [183, 669], [184, 670], [185, 671], [186, 672], [187, 673], [2131, 48], [84, 48], [2078, 674], [2132, 48], [193, 675], [194, 676], [192, 62], [2133, 48], [2134, 510], [2137, 677], [2135, 62], [1115, 62], [2136, 510], [190, 678], [191, 679], [82, 48], [85, 680], [281, 62], [2139, 681], [2138, 48], [407, 48], [83, 48], [1614, 579], [830, 62], [91, 682], [361, 683], [365, 684], [367, 685], [214, 686], [228, 687], [332, 688], [260, 48], [335, 689], [296, 690], [305, 691], [333, 692], [215, 693], [259, 48], [261, 694], [334, 695], [235, 696], [216, 697], [240, 696], [229, 696], [199, 696], [287, 698], [288, 699], [204, 48], [284, 700], [289, 618], [376, 701], [282, 618], [377, 702], [266, 48], [285, 703], [389, 704], [388, 705], [291, 618], [387, 48], [385, 48], [386, 706], [286, 62], [273, 707], [274, 708], [283, 709], [300, 710], [301, 711], [290, 712], [268, 713], [269, 714], [380, 715], [383, 716], [247, 717], [246, 718], [245, 719], [392, 62], [244, 720], [220, 48], [395, 48], [1489, 721], [1488, 48], [398, 48], [397, 62], [399, 722], [195, 48], [326, 48], [227, 723], [197, 724], [349, 48], [350, 48], [352, 48], [355, 725], [351, 48], [353, 726], [354, 726], [213, 48], [226, 48], [360, 727], [368, 728], [372, 729], [209, 730], [276, 731], [275, 48], [267, 713], [295, 732], [293, 733], [292, 48], [294, 48], [299, 734], [271, 735], [208, 736], [233, 737], [323, 738], [200, 739], [207, 740], [196, 688], [337, 741], [347, 742], [336, 48], [346, 743], [234, 48], [218, 744], [314, 745], [313, 48], [320, 746], [322, 747], [315, 748], [319, 749], [321, 746], [318, 748], [317, 746], [316, 748], [256, 750], [241, 750], [308, 751], [242, 751], [202, 752], [201, 48], [312, 753], [311, 754], [310, 755], [309, 756], [203, 757], [280, 758], [297, 759], [279, 760], [304, 761], [306, 762], [303, 760], [236, 757], [189, 48], [324, 763], [262, 764], [298, 48], [345, 765], [265, 766], [340, 767], [206, 48], [341, 768], [343, 769], [344, 770], [327, 48], [339, 739], [238, 771], [325, 772], [348, 773], [210, 48], [212, 48], [217, 774], [307, 775], [205, 776], [211, 48], [264, 777], [263, 778], [219, 779], [272, 780], [270, 781], [221, 782], [223, 783], [396, 48], [222, 784], [224, 785], [363, 48], [362, 48], [364, 48], [394, 48], [225, 786], [278, 62], [90, 48], [302, 787], [248, 48], [258, 788], [237, 48], [370, 62], [379, 789], [255, 62], [374, 618], [254, 790], [357, 791], [253, 789], [198, 48], [381, 792], [251, 62], [252, 62], [243, 48], [257, 48], [250, 793], [249, 794], [239, 795], [232, 712], [342, 48], [231, 796], [230, 48], [366, 48], [277, 62], [359, 797], [81, 48], [89, 798], [86, 62], [87, 48], [88, 48], [338, 799], [331, 800], [330, 48], [329, 801], [328, 48], [369, 802], [371, 803], [373, 804], [1490, 805], [375, 806], [378, 807], [404, 808], [382, 808], [403, 809], [384, 810], [390, 811], [391, 812], [393, 813], [400, 814], [402, 48], [401, 815], [356, 816], [1507, 48], [1522, 817], [1523, 817], [1536, 818], [1524, 819], [1525, 819], [1526, 820], [1520, 821], [1518, 822], [1509, 48], [1513, 823], [1517, 824], [1515, 825], [1521, 826], [1510, 827], [1511, 828], [1512, 829], [1514, 830], [1516, 831], [1519, 832], [1527, 819], [1528, 819], [1529, 819], [1530, 817], [1531, 819], [1532, 819], [1508, 819], [1533, 48], [1535, 833], [1534, 819], [1615, 834], [458, 835], [455, 62], [456, 62], [454, 48], [457, 836], [1631, 601], [1648, 837], [1649, 837], [1651, 838], [1652, 839], [1630, 598], [1653, 837], [1669, 840], [1650, 837], [1654, 601], [1655, 601], [1656, 837], [1657, 62], [1658, 837], [1659, 841], [1660, 837], [1661, 837], [1662, 601], [1663, 837], [1664, 837], [1665, 837], [1666, 837], [1667, 837], [1668, 601], [464, 62], [79, 48], [80, 48], [13, 48], [14, 48], [16, 48], [15, 48], [2, 48], [17, 48], [18, 48], [19, 48], [20, 48], [21, 48], [22, 48], [23, 48], [24, 48], [3, 48], [25, 48], [26, 48], [4, 48], [27, 48], [31, 48], [28, 48], [29, 48], [30, 48], [32, 48], [33, 48], [34, 48], [5, 48], [35, 48], [36, 48], [37, 48], [38, 48], [6, 48], [42, 48], [39, 48], [40, 48], [41, 48], [43, 48], [7, 48], [44, 48], [49, 48], [50, 48], [45, 48], [46, 48], [47, 48], [48, 48], [8, 48], [54, 48], [51, 48], [52, 48], [53, 48], [55, 48], [9, 48], [56, 48], [57, 48], [58, 48], [60, 48], [59, 48], [61, 48], [62, 48], [10, 48], [63, 48], [64, 48], [65, 48], [11, 48], [66, 48], [67, 48], [68, 48], [69, 48], [70, 48], [1, 48], [71, 48], [72, 48], [12, 48], [76, 48], [74, 48], [78, 48], [73, 48], [77, 48], [75, 48], [113, 842], [123, 843], [112, 842], [133, 844], [104, 845], [103, 846], [132, 815], [126, 847], [131, 848], [106, 849], [120, 850], [105, 851], [129, 852], [101, 853], [100, 815], [130, 854], [102, 855], [107, 856], [108, 48], [111, 856], [98, 48], [134, 857], [124, 858], [115, 859], [116, 860], [118, 861], [114, 862], [117, 863], [127, 815], [109, 864], [110, 865], [119, 866], [99, 867], [122, 858], [121, 856], [125, 48], [128, 868], [1550, 869], [1541, 870], [1548, 871], [1543, 48], [1544, 48], [1542, 872], [1545, 873], [1537, 48], [1538, 48], [1549, 874], [1540, 875], [1546, 48], [1547, 876], [1539, 877], [1602, 878], [1555, 879], [1557, 880], [1600, 48], [1556, 881], [1601, 882], [1605, 883], [1603, 48], [1558, 879], [1559, 48], [1599, 884], [1554, 885], [1551, 48], [1604, 886], [1552, 887], [1553, 48], [1560, 888], [1561, 888], [1562, 888], [1563, 888], [1564, 888], [1565, 888], [1566, 888], [1567, 888], [1568, 888], [1569, 888], [1571, 888], [1570, 888], [1572, 888], [1573, 888], [1574, 888], [1598, 889], [1575, 888], [1576, 888], [1577, 888], [1578, 888], [1579, 888], [1580, 888], [1581, 888], [1582, 888], [1583, 888], [1585, 888], [1584, 888], [1586, 888], [1587, 888], [1588, 888], [1589, 888], [1590, 888], [1591, 888], [1592, 888], [1593, 888], [1594, 888], [1595, 888], [1596, 888], [1597, 888], [1486, 48], [408, 48], [1487, 48]], "changeFileSet": [2140, 2085, 2084, 2086, 2087, 2088, 2090, 2089, 2097, 2091, 2092, 2093, 2094, 2095, 2096, 2098, 2099, 2100, 2101, 2102, 2103, 2104, 2141, 2105, 2106, 2083, 2107, 2108, 2110, 2109, 2111, 2112, 2113, 2115, 2116, 2114, 2117, 2118, 2142, 2121, 2120, 2122, 2143, 2119, 2144, 2145, 2146, 2123, 2125, 2124, 2126, 2127, 2128, 2129, 2147, 406, 1499, 1498, 1502, 1503, 1504, 409, 440, 1501, 1505, 1506, 1612, 1613, 1610, 1611, 441, 1608, 1609, 442, 444, 445, 1623, 443, 1616, 1617, 1618, 1619, 1620, 1621, 1622, 1493, 1624, 446, 1627, 1625, 447, 1671, 1672, 1670, 1673, 1674, 1675, 1678, 1676, 1677, 448, 1679, 450, 451, 1681, 1680, 449, 1682, 1683, 1495, 1492, 2148, 819, 2149, 2150, 2151, 2152, 2153, 2154, 2155, 2156, 2157, 2158, 2159, 2160, 2161, 2162, 1684, 1685, 820, 818, 817, 816, 463, 462, 460, 459, 461, 826, 824, 821, 825, 801, 802, 472, 800, 470, 469, 473, 474, 467, 471, 810, 815, 814, 812, 813, 811, 828, 465, 466, 468, 452, 453, 2163, 2164, 2165, 2166, 1687, 1686, 1688, 1689, 833, 831, 1690, 2167, 2168, 832, 836, 835, 834, 1691, 823, 2169, 829, 1693, 1694, 1692, 1695, 1696, 2170, 839, 838, 2171, 2172, 845, 846, 844, 1698, 1699, 840, 841, 850, 849, 847, 1701, 1702, 1700, 1703, 1704, 1705, 842, 843, 2173, 2174, 1697, 2175, 2176, 2177, 2178, 889, 888, 887, 886, 866, 867, 860, 852, 865, 893, 891, 890, 892, 1706, 878, 877, 873, 876, 871, 870, 875, 874, 869, 872, 848, 885, 879, 882, 884, 880, 881, 868, 863, 864, 837, 851, 827, 822, 1708, 1707, 1710, 1711, 894, 1712, 2075, 2077, 2076, 2079, 2038, 2080, 895, 883, 1496, 854, 858, 855, 859, 857, 853, 856, 2081, 1485, 1484, 1709, 861, 2082, 862, 1497, 1626, 2179, 1494, 804, 805, 809, 807, 808, 803, 806, 1500, 897, 896, 439, 1491, 405, 906, 905, 908, 907, 918, 911, 919, 916, 920, 914, 915, 917, 913, 912, 921, 909, 910, 900, 901, 924, 922, 923, 926, 925, 903, 902, 904, 2046, 2048, 2049, 2050, 2045, 2047, 2041, 2042, 2043, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2052, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2073, 2044, 2074, 2072, 2051, 2039, 2040, 1713, 1714, 1715, 1716, 1718, 1717, 1719, 1725, 1720, 1722, 1721, 1723, 1724, 1726, 1727, 1730, 1728, 1729, 1731, 1732, 1733, 1734, 1736, 1735, 1737, 1738, 1741, 1739, 1740, 1742, 1743, 1744, 1745, 1768, 1769, 1770, 1771, 1746, 1747, 1748, 1749, 1750, 1751, 1752, 1753, 1754, 1755, 1756, 1757, 1763, 1758, 1760, 1759, 1761, 1762, 1764, 1765, 1766, 1767, 1772, 1773, 1774, 1775, 1776, 1777, 1778, 1779, 1780, 1781, 1782, 1783, 1784, 1785, 1786, 1787, 1788, 1791, 1789, 1790, 1792, 1794, 1793, 1798, 1796, 1797, 1795, 1799, 1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808, 1809, 1810, 1812, 1811, 1813, 1815, 1814, 1816, 1818, 1817, 1819, 1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827, 1828, 1829, 1830, 1831, 1832, 1833, 1834, 1836, 1835, 1837, 1838, 1839, 1840, 1841, 1843, 1842, 1844, 1845, 1846, 1847, 1848, 1849, 1850, 1852, 1851, 1853, 1854, 1855, 1856, 1857, 1858, 1859, 1860, 1861, 1862, 1863, 1864, 1865, 1866, 1867, 1868, 1869, 1870, 1871, 1872, 1873, 1874, 1879, 1875, 1876, 1877, 1878, 1880, 1881, 1882, 1884, 1883, 1885, 1886, 1887, 1888, 1890, 1889, 1891, 1892, 1893, 1894, 1895, 1896, 1897, 1901, 1898, 1899, 1900, 1902, 1903, 1904, 1906, 1905, 1907, 1908, 1909, 1910, 1911, 1912, 1913, 1914, 1915, 1916, 1917, 1918, 1920, 1919, 1921, 1922, 1924, 1923, 2037, 1925, 1926, 1927, 1928, 1929, 1930, 1932, 1931, 1933, 1934, 1935, 1936, 1939, 1937, 1938, 1941, 1940, 1942, 1943, 1944, 1946, 1945, 1947, 1948, 1949, 1950, 1951, 1952, 1953, 1954, 1955, 1956, 1958, 1957, 1959, 1960, 1961, 1963, 1962, 1964, 1965, 1967, 1966, 1968, 1970, 1969, 1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 1980, 1981, 1982, 1983, 1984, 1985, 1986, 1987, 1989, 1988, 1990, 1991, 1992, 1993, 1994, 1996, 1995, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2023, 2021, 2022, 2024, 2025, 2027, 2026, 2028, 2029, 2030, 2031, 2032, 2034, 2033, 2035, 2036, 475, 476, 477, 478, 480, 479, 481, 487, 482, 484, 483, 485, 486, 488, 489, 492, 490, 491, 493, 494, 495, 496, 498, 497, 499, 500, 503, 501, 502, 504, 505, 506, 507, 530, 531, 532, 533, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 525, 520, 522, 521, 523, 524, 526, 527, 528, 529, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 553, 551, 552, 554, 556, 555, 560, 558, 559, 557, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 574, 573, 575, 577, 576, 578, 580, 579, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 598, 597, 599, 600, 601, 602, 603, 605, 604, 606, 607, 608, 609, 610, 611, 612, 614, 613, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 641, 637, 638, 639, 640, 642, 643, 644, 646, 645, 647, 648, 649, 650, 652, 651, 653, 654, 655, 656, 657, 658, 659, 663, 660, 661, 662, 664, 665, 666, 668, 667, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 682, 681, 683, 684, 686, 685, 799, 687, 688, 689, 690, 691, 692, 694, 693, 695, 696, 697, 698, 701, 699, 700, 703, 702, 704, 705, 706, 708, 707, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 720, 719, 721, 722, 723, 725, 724, 726, 727, 729, 728, 730, 732, 731, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 751, 750, 752, 753, 754, 755, 756, 758, 757, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 785, 783, 784, 786, 787, 789, 788, 790, 791, 792, 793, 794, 796, 795, 797, 798, 1607, 1606, 1210, 1209, 1211, 1204, 1203, 1205, 1207, 1206, 1208, 1213, 1212, 1214, 1064, 1061, 1065, 1070, 1069, 1071, 1073, 1072, 1074, 1107, 1106, 1108, 1110, 1109, 1111, 1113, 1112, 1114, 1120, 1119, 1121, 1123, 1122, 1124, 1129, 1128, 1130, 1126, 1125, 1127, 1132, 1131, 1133, 1140, 1139, 1141, 1053, 1052, 1054, 1051, 1050, 1135, 1137, 1134, 1136, 1138, 1158, 1157, 1159, 1143, 1142, 1144, 1146, 1145, 1147, 1149, 1148, 1150, 1152, 1151, 1153, 1155, 1154, 1156, 1163, 1162, 1164, 1076, 1075, 1077, 1166, 1165, 1167, 1357, 1358, 1169, 1168, 1170, 1172, 1171, 1173, 1174, 1175, 1190, 1189, 1191, 1177, 1176, 1178, 1180, 1179, 1181, 1183, 1182, 1184, 1193, 1192, 1194, 1196, 1195, 1197, 1201, 1200, 1202, 1216, 1215, 1217, 1117, 1118, 1222, 1221, 1223, 1228, 1227, 1229, 1231, 1230, 1225, 1224, 1226, 1233, 1232, 1234, 1236, 1235, 1237, 1239, 1238, 1240, 1244, 1245, 1246, 1242, 1241, 1243, 1248, 1247, 1249, 1056, 1055, 1057, 1251, 1250, 1252, 1257, 1256, 1258, 1254, 1253, 1255, 1267, 1266, 1265, 1261, 1260, 1259, 1220, 1219, 1218, 1264, 1263, 1262, 1049, 1161, 1160, 1270, 1269, 1268, 1273, 1272, 1271, 1294, 1293, 1292, 1282, 1281, 1280, 1276, 1275, 1274, 1285, 1284, 1283, 1279, 1278, 1277, 1288, 1287, 1286, 1291, 1290, 1289, 1297, 1296, 1295, 1308, 1307, 1306, 1300, 1299, 1298, 1302, 1301, 1311, 1310, 1309, 1188, 1187, 1186, 1185, 1315, 1314, 1313, 1312, 1319, 1318, 1317, 1045, 1323, 1322, 1321, 1326, 1325, 1324, 1060, 1059, 1058, 1305, 1304, 1303, 1100, 1103, 1101, 1102, 1098, 1097, 1096, 1334, 1333, 1332, 1331, 1327, 1330, 1328, 1329, 1337, 1336, 1335, 1340, 1339, 1338, 1344, 1343, 1342, 1341, 1347, 1346, 1345, 1199, 1198, 1353, 1352, 1351, 1350, 1349, 1348, 1361, 1360, 1359, 1356, 1355, 1354, 1365, 1364, 1363, 1371, 1370, 1369, 1374, 1373, 1372, 1377, 1375, 1376, 1400, 1398, 1397, 1399, 1380, 1379, 1378, 1383, 1382, 1381, 1386, 1385, 1384, 1389, 1388, 1387, 1392, 1391, 1390, 1396, 1394, 1393, 1395, 1464, 1460, 1465, 1039, 1040, 1466, 1463, 1461, 1462, 1043, 1041, 1475, 1482, 1480, 899, 1483, 1476, 1458, 1457, 1467, 1472, 1042, 1481, 1471, 1473, 1474, 1479, 1469, 1470, 1459, 1477, 1478, 1044, 1048, 1047, 1046, 1402, 1401, 1405, 1404, 1403, 1441, 1440, 1439, 1429, 1428, 1427, 1408, 1407, 1406, 1411, 1410, 1409, 1414, 1413, 1412, 1438, 1437, 1436, 1417, 1416, 1415, 1426, 1425, 1421, 1420, 1418, 1419, 1432, 1431, 1430, 1435, 1434, 1433, 1447, 1446, 1445, 1444, 1443, 1442, 1450, 1449, 1448, 1453, 1452, 1451, 1456, 1455, 1454, 1424, 1423, 1422, 1368, 1367, 1366, 1362, 1116, 1068, 1067, 1066, 1105, 1104, 1316, 1320, 1099, 1063, 973, 978, 975, 974, 977, 976, 929, 930, 931, 928, 927, 934, 935, 983, 984, 985, 951, 952, 1001, 1002, 953, 954, 1023, 1020, 1021, 1022, 1024, 986, 987, 936, 1468, 988, 989, 946, 938, 949, 950, 937, 947, 948, 959, 960, 1010, 1013, 1016, 1017, 1014, 1015, 1008, 1011, 1012, 1009, 955, 956, 957, 958, 971, 972, 979, 980, 1027, 1026, 1028, 1030, 1025, 1031, 1029, 1038, 1007, 1006, 1005, 962, 961, 964, 963, 1019, 1018, 966, 965, 968, 967, 982, 981, 1034, 1036, 1033, 1035, 1032, 933, 932, 991, 990, 940, 944, 943, 945, 941, 939, 942, 1004, 1003, 970, 969, 1000, 999, 996, 995, 993, 994, 992, 998, 997, 1037, 898, 1062, 358, 1095, 1091, 1078, 1094, 1087, 1085, 1084, 1083, 1080, 1081, 1089, 1082, 1079, 1086, 1092, 1093, 1088, 1090, 1632, 1638, 1640, 1633, 1641, 1639, 1642, 1634, 1635, 1643, 1644, 1647, 1636, 1645, 1646, 1637, 416, 412, 419, 414, 415, 417, 413, 410, 418, 411, 432, 438, 429, 437, 430, 431, 422, 420, 436, 433, 435, 434, 428, 427, 421, 423, 425, 426, 424, 1628, 2130, 1629, 136, 137, 138, 97, 139, 140, 141, 92, 95, 93, 94, 142, 143, 144, 145, 146, 147, 148, 150, 149, 151, 152, 153, 135, 96, 154, 155, 156, 188, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 172, 171, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 2131, 84, 2078, 2132, 193, 194, 192, 2133, 2134, 2137, 2135, 1115, 2136, 190, 191, 82, 85, 281, 2139, 2138, 407, 83, 1614, 2180, 2181, 2182, 2183, 2184, 2185, 2186, 2187, 2188, 2189, 2190, 2191, 2192, 2193, 2194, 2195, 2196, 2197, 2198, 2199, 2200, 2201, 2202, 2203, 2204, 2205, 2206, 2207, 2208, 2209, 2210, 2211, 2212, 2213, 2214, 2215, 2216, 2217, 2218, 2219, 2220, 2221, 2222, 2223, 2224, 2225, 2226, 2227, 2228, 2229, 2230, 2231, 2232, 2233, 2234, 2235, 2236, 2237, 2238, 2239, 2240, 2241, 2242, 2243, 2244, 2245, 2246, 2247, 2248, 2249, 2250, 2251, 2252, 2253, 830, 91, 361, 365, 367, 214, 228, 332, 260, 335, 296, 305, 333, 215, 259, 261, 334, 235, 216, 240, 229, 199, 287, 288, 204, 284, 289, 376, 282, 377, 266, 285, 389, 388, 291, 387, 385, 386, 286, 273, 274, 283, 300, 301, 290, 268, 269, 380, 383, 247, 246, 245, 392, 244, 220, 395, 1489, 1488, 398, 397, 399, 195, 326, 227, 197, 349, 350, 352, 355, 351, 353, 354, 213, 226, 360, 368, 372, 209, 276, 275, 267, 295, 293, 292, 294, 299, 271, 208, 233, 323, 200, 207, 196, 337, 347, 336, 346, 234, 218, 314, 313, 320, 322, 315, 319, 321, 318, 317, 316, 256, 241, 308, 242, 202, 201, 312, 311, 310, 309, 203, 280, 297, 279, 304, 306, 303, 236, 189, 324, 262, 298, 345, 265, 340, 206, 341, 343, 344, 327, 339, 238, 325, 348, 210, 212, 217, 307, 205, 211, 264, 263, 219, 272, 270, 221, 223, 396, 222, 224, 363, 362, 364, 394, 225, 278, 90, 302, 248, 258, 237, 370, 379, 255, 374, 254, 357, 253, 198, 381, 251, 252, 243, 257, 250, 249, 239, 232, 342, 231, 230, 366, 277, 359, 81, 89, 86, 87, 88, 338, 331, 330, 329, 328, 369, 371, 373, 1490, 375, 378, 404, 382, 403, 384, 390, 391, 393, 400, 402, 401, 356, 1507, 1522, 1523, 1536, 1524, 1525, 1526, 1520, 1518, 1509, 1513, 1517, 1515, 1521, 1510, 1511, 1512, 1514, 1516, 1519, 1527, 1528, 1529, 1530, 1531, 1532, 1508, 1533, 1535, 1534, 1615, 458, 455, 456, 454, 457, 1631, 1648, 1649, 1651, 1652, 1630, 1653, 1669, 1650, 1654, 1655, 1656, 1657, 1658, 1659, 1660, 1661, 1662, 1663, 1664, 1665, 1666, 1667, 1668, 464, 79, 80, 13, 14, 16, 15, 2, 17, 18, 19, 20, 21, 22, 23, 24, 3, 25, 26, 4, 27, 31, 28, 29, 30, 32, 33, 34, 5, 35, 36, 37, 38, 6, 42, 39, 40, 41, 43, 7, 44, 49, 50, 45, 46, 47, 48, 8, 54, 51, 52, 53, 55, 9, 56, 57, 58, 60, 59, 61, 62, 10, 63, 64, 65, 11, 66, 67, 68, 69, 70, 1, 71, 72, 12, 76, 74, 78, 73, 77, 75, 113, 123, 112, 133, 104, 103, 132, 126, 131, 106, 120, 105, 129, 101, 100, 130, 102, 107, 108, 111, 98, 134, 124, 115, 116, 118, 114, 117, 127, 109, 110, 119, 99, 122, 121, 125, 128, 1550, 1541, 1548, 1543, 1544, 1542, 1545, 1537, 1538, 1549, 1540, 1546, 1547, 1539, 1602, 1555, 1557, 1600, 1556, 1601, 1605, 1603, 1558, 1559, 1599, 1554, 1551, 1604, 1552, 1553, 1560, 1561, 1562, 1563, 1564, 1565, 1566, 1567, 1568, 1569, 1571, 1570, 1572, 1573, 1574, 1598, 1575, 1576, 1577, 1578, 1579, 1580, 1581, 1582, 1583, 1585, 1584, 1586, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 1594, 1595, 1596, 1597, 1486, 408, 1487], "version": "5.8.3"}