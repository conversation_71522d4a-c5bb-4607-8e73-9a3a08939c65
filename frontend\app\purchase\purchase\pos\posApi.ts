// posApi.ts - API utilities for POS (Purchase Order System) endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';
import { getAccountSettingsByUser } from '../../../settings/accountSettingsApi';
import { filterSuppliers as getSuppliersFromSuppliersApi } from '../../../purchasing/suppliers/suppliersApi';
import type { SupplierFilter } from '@/components/SupplierModal';

const API_BASE = '/api';

// Product interfaces
export interface Product {
  uuid: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  price?: number;
  cost?: number;
  warehouseUuid: string;
  currentStock?: number;
  stockStorageUuid?: string;
  supplierPrice?: number;
  stockWarning?: boolean;
  // Supplier pricing fields from backend
  retailPrice?: number;
  wholesalePrice?: number;
  midWholesalePrice?: number;
  institutionalPrice?: number;
  supplierType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}

export interface PaginatedProductsResponse {
  data: Product[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Response interface for categories-only requests
export interface CategoriesResponse {
  data: Product[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
  categories: string[];
}

// Supplier interfaces
export interface Supplier {
  uuid: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  warehouseUuid: string;
  creditLimit: number;
  currentCredit: number;
}

export interface CreateSupplierDto {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  warehouseUuid: string;
  creditLimit?: number;
}

export interface UpdateSupplierDto {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  creditLimit?: number;
}

// Purchase interfaces
export interface PurchaseItem {
  productUuid: string;
  name: string; // Add name field that backend expects
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  order?: number; // Add order field to preserve cart order
}

export interface CreatePurchaseDto {
  supplierUuid: string;
  warehouseUuid: string;
  userUuid: string;
  items: PurchaseItem[];
  paymentMethod: string;
  amountPaid: number;
  useTax: boolean;
  taxRate: number;
}

export interface UpdatePurchaseDto {
  userUuid: string;
  supplierUuid?: string;
  paymentMethod?: string;
  amountPaid?: number;
  useTax?: boolean;
  taxRate?: number;
}

// Product filtering and search
export async function filterProducts(params: {
  warehouseUuid: string;
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  supplierType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}): Promise<PaginatedProductsResponse> {
  const queryParams = new URLSearchParams();
  
  queryParams.append('warehouseUuid', params.warehouseUuid);
  
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.limit) queryParams.append('limit', params.limit.toString());
  if (params.search) queryParams.append('search', params.search);
  if (params.category) queryParams.append('category', params.category);
  if (params.supplierType) queryParams.append('supplierType', params.supplierType);

  const res = await axios.get(`${API_BASE}/products/filter?${queryParams.toString()}`, {
    headers: getAxiosAuthHeaders(),
  });
  
  return res.data;
}

// Get product categories
export async function getProductCategories(warehouseUuid: string): Promise<string[]> {
  const res = await axios.get(`${API_BASE}/products/filter?warehouseUuid=${warehouseUuid}&returnCategoriesOnly=true`, {
    headers: getAxiosAuthHeaders(),
  });

  return res.data.categories || [];
}

// Supplier filtering and search
export async function filterSuppliers(params: {
  warehouseUuid?: string;
  name?: string;
}): Promise<{ data: Supplier[] }> {
  try {
    const suppliers = await getSuppliersFromSuppliersApi(params);
    return { data: suppliers };
  } catch (error) {
    console.error('Error filtering suppliers:', error);
    throw error;
  }
}

// Create new supplier
export async function createSupplier(data: CreateSupplierDto): Promise<Supplier> {
  const res = await axios.post(`${API_BASE}/suppliers`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get default supplier (first supplier for the warehouse)
export async function getDefaultSupplier(warehouseUuid: string): Promise<Supplier | null> {
  try {
    const response = await filterSuppliers({ warehouseUuid });
    return response.data.length > 0 ? response.data[0] : null;
  } catch (error) {
    console.error('Error getting default supplier:', error);
    return null;
  }
}

// Get account settings for tax configuration
export async function getAccountSettings(userUuid: string) {
  try {
    return await getAccountSettingsByUser(userUuid);
  } catch (error) {
    console.error('Error getting account settings:', error);
    throw error;
  }
}

// Validate stock levels for purchase items
export async function validateStock(items: PurchaseItem[], warehouseUuid: string): Promise<{
  valid: boolean;
  errors: string[];
}> {
  // For purchases, we typically don't need to validate stock levels
  // as we're adding to inventory, not removing from it
  // But we can validate that products exist
  
  const errors: string[] = [];
  
  for (const item of items) {
    if (item.quantity <= 0) {
      errors.push(`Invalid quantity for ${item.name}: ${item.quantity}`);
    }
    if (item.unitPrice < 0) {
      errors.push(`Invalid price for ${item.name}: ${item.unitPrice}`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}
