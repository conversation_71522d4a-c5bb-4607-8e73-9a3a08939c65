#!/usr/bin/env python3
"""
Create Database Backup using Backend API

This script creates a database backup using the backend API instead of direct pg_dump.
It provides a simple interface for creating backups with automatic naming.

Usage:
    python scripts/create_backup.py [prefix]
    
    prefix: Optional prefix for the backup name (default: "manual")
"""

import sys
import os
import requests
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

BACKEND_DIR = Path(__file__).resolve().parent.parent
ENV_PATH = BACKEND_DIR / ".env"

# API configuration
BACKUP_API_BASE_URL = os.environ.get("BACKUP_API_BASE_URL", "http://localhost:5000")

def validate_environment():
    """Validate that the backup API is accessible."""
    try:
        response = requests.get(f"{BACKUP_API_BASE_URL}/health/", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            if health_data.get("status") == "healthy":
                print("[OK] Backup API is healthy and accessible")
                return True
            else:
                print(f"[WARNING] Backup API status: {health_data.get('status')}")
                return True
        else:
            print(f"[ERROR] Backup API health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Cannot connect to backup API at {BACKUP_API_BASE_URL}: {e}")
        print(f"Please ensure the backup backend is running on {BACKUP_API_BASE_URL}")
        return False

def api_request(method, endpoint, data=None, timeout=30):
    """Make a request to the backup API."""
    url = f"{BACKUP_API_BASE_URL}{endpoint}"
    headers = {"Content-Type": "application/json"}
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, timeout=timeout)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data, timeout=timeout)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers, timeout=timeout)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        response.raise_for_status()
        return response.json()
        
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] API request failed: {e}")
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_data = e.response.json()
                print(f"[ERROR] API Error: {error_data}")
            except:
                print(f"[ERROR] HTTP Status: {e.response.status_code}")
        return None

def create_backup(prefix="manual"):
    """Create a database backup using the API."""
    print(f"[CREATING] Creating backup with prefix: {prefix}")
    
    data = {"prefix": prefix}
    result = api_request("POST", "/backup/", data)
    
    if result and result.get("status") == "success":
        backup_data = result.get("data", {})
        backup_name = backup_data.get("backup_name")
        timestamp = backup_data.get("timestamp")
        size_bytes = backup_data.get("size_bytes", 0)
        size_mb = size_bytes / (1024 * 1024)
        
        print(f"[OK] Backup created successfully!")
        print(f"   [NAME] {backup_name}")
        print(f"   [TIME] {timestamp}")
        print(f"   [SIZE] {size_mb:.2f} MB")
        
        return backup_name
    else:
        print(f"[ERROR] Backup creation failed: {result}")
        return None

def main():
    """Main function."""
    # Get prefix from command line argument
    prefix = sys.argv[1] if len(sys.argv) > 1 else "manual"
    
    print("Database Backup Creator")
    print("=" * 40)
    
    # Validate environment and API connectivity
    if not validate_environment():
        print(f"\n[TROUBLESHOOTING] Troubleshooting tips:")
        print(f"1. Ensure the backup backend is running on {BACKUP_API_BASE_URL}")
        print(f"2. Check if the backup backend is accessible: curl {BACKUP_API_BASE_URL}/health/")
        print(f"3. Verify network connectivity to the backup backend")
        print(f"4. Check backup backend logs for any errors")
        print(f"5. Verify environment variables are set correctly")
        sys.exit(1)
    
    # Create backup
    backup_name = create_backup(prefix)
    
    if backup_name:
        print(f"\n[SUCCESS] Backup completed successfully!")
        print(f"[BACKUP] Backup name: {backup_name}")
        print(f"\n[HELP] To list all backups: python scripts/migration_manager_ysqlsh.py list")
        print(f"[HELP] To restore this backup: python scripts/migration_manager_ysqlsh.py restore-from {backup_name}")
    else:
        print(f"\n[ERROR] Backup creation failed")
        sys.exit(1)

if __name__ == "__main__":
    main() 